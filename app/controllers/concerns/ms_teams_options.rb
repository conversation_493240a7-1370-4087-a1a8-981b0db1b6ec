module MsTeamsOptions
  extend ActiveSupport::Concern
  include ContributorOptionsHelper

  attr_accessor :scoped_company

  def custom_fields_options
    fields_with_options = custom_form.custom_form_fields.where(name: ['status', 'priority']).select(:name, :options)
    options = {}
    fields_with_options.each do |field|
      options["#{field.name}_options"] = map_options(field)
    end
    options[:contributor_options] = contributor_options
    options
  end

  def workspace_options
    formatted_workspaces = fetch_workspaces.map do |workspace|
      title = has_multiple_companies ? "#{workspace.name} (#{workspace.company.name})" : workspace.name
      {
        title: title,
        value: workspace.id
      }
    end
    { workspaces: formatted_workspaces }
  end

  def fetch_workspaces
    if params[:is_linking]
      workspaces_of_current_user
    else
      workspaces_with_active_configs
    end
  end

  def workspaces_with_active_configs
    if has_multiple_companies && @current_user.present?
      Workspace.includes(:company).joins(:ms_teams_configs)
                                  .where(ms_teams_configs: { active: true })
                                  .where(company_id: @current_user.companies)
                                  .distinct
    else
      scoped_company.workspaces.joins(:ms_teams_configs)
                               .where(ms_teams_configs: { active: true })
                               .distinct
    end
  end

  def workspaces_of_current_user
    if has_multiple_companies && @current_user.present?
      Workspace.joins(:company)
               .where(companies: { subdomain: params['subdomain'] })
    else
      scoped_company.workspaces
    end
  end

  def fetch_options(option_type)
    case option_type
    when 'custom_fields'
      custom_fields_options
    when 'workspaces'
      workspace_options
    else
      { error: 'Invalid option type' }
    end
  end

  def has_multiple_companies
    return @has_multiple_companies if defined?(@has_multiple_companies)

    @has_multiple_companies = @current_user&.companies&.size.to_i > 1
  end
      
  def contributor_field
    @contributor_field ||= custom_form.custom_form_fields.find_by_name('assigned_to')
  end

  def contributor_options
    contributors = Contributor.where(id: @selected_cont_ids)
    contributors = contributors.map do |contributor|
      if contributor.name
        {
          title: contributor.name,
          value: contributor.id
        }
      else
        nil
      end
    end
    contributors.compact
  end

  def ticket_json
    json = {}
    begin
      ticket.help_ticket_fields
        .select{ |field| HelpTicket::MS_TEAMS_FIELDS.include?(field['name']) }
        .map do |field|
          if field['name'] == 'assigned_to' && json["#{field['name']}_name"].blank?
            assigned_to_ids = help_ticket_data['assigned_to']
            unless assigned_to_ids
              assigned_to_ids = ticket.custom_form_values.where(custom_form_field_id: field['id']).pluck(:value_int).compact
            end

            contributors = Contributor.includes(:group, company_user: :user).where(id: assigned_to_ids)
            if contributors.present?
              json["#{field['name']}_name"] = contributors.map(&:name).to_sentence
              @selected_cont_ids = contributors.pluck(:id)
              json["#{field['name']}_id"] = @selected_cont_ids.join(',')
            end
          elsif field['name'] == 'created_by'
            contributor_id = field[:values].pluck('value_int').compact.first
            creator_contributor = Contributor.includes(:group, company_user: :user).where(id: contributor_id).first
            json[field['name']] = creator_contributor&.name
          elsif field['name'] == 'description'
            description = field[:values].pluck('value_str').to_sentence.gsub('<div><!--block-->', '').gsub('</div>', '')
            json[field['name']] = HtmlToMarkdownConverter.new(parse_type: 'teams', content: description).call
          else
            json[field['name']] = field[:values].pluck('value_str').to_sentence
          end
        end

      json['help_ticket_id'] = ticket.id
      json['ticket_number'] = ticket.ticket_number
      json['url'] = "#{build_company_url(ticket.company)}help_tickets/#{ticket.id}"
      json['company_subdomain'] = "#{ticket.company.subdomain}"
      json['fields_options'] = custom_fields_options
    rescue => e
      Bugsnag.notify(e, { message: e.message, ticket_id: ticket&.id, backtrace: e.backtrace })
      json = {}
    end
    json
  end

  def map_options(field)
    options = JSON.parse(field.options)

    case field.name
    when 'status'
      return options.map { |option| { title: option['name'], value: option['name'] }}
    when 'priority'
      return options.map { |option| { title: option['name'].humanize, value: option['name'] }}
    end
  end

  def company
    @company ||= scoped_company
  end
end
