# These are the values of the custom form fields. They are what gets displayed
#   on the various view pages (for now it is solely help tickets).

class CustomFormValue < ApplicationRecord
  FIELD_ASSOCIATIONS = {
    "location_list" => :related_location_ids,
    "vendor_list" => :related_vendor_ids,
    "contract_list" => :related_contract_ids,
    "asset_list" => :related_asset_ids,
    "telecom_list" => :related_telecom_ids,
    "people_list" => :related_people_ids,
  }.freeze
  
  include PgSearch::Model
  include ActionView::Helpers::SanitizeHelper
  include ScheduledSlas
  include DeleteHelpTicketCacheKey
  include HelpDeskSettings
  include PusherLog
  include HandleCompanyCacheKeys
  include BusinessHours
  include QuickViewFilterUpdate
  include CompanyCache
  include FirstResponseAndSlaHelper

  pg_search_scope :search_text,
                  :against => [:value_str],
                  :using => { :tsearch => {:prefix => true} }

  attr_accessor :skip_event_trigger, :skip_sla_trigger, :activity_owner_id, :current_user_id, :is_last_bulk_item, :bulk_item, :current_contributor_id, :skip_survey_trigger

  belongs_to :company
  belongs_to :custom_form_field
  belongs_to :custom_form
  belongs_to :module, polymorphic: true
  belongs_to :help_ticket, -> { where(custom_form_values: { module_type: 'HelpTicket' }).includes(:custom_form_values) }, foreign_key: :module_id
  belongs_to :location, -> { where(custom_form_values: { module_type: 'Location' }).includes(:custom_form_values) }, foreign_key: :module_id
  belongs_to :company_user, -> { where(custom_form_values: { module_type: 'CompanyUser' }).includes(:custom_form_values) }, foreign_key: :module_id
  belongs_to :creator, :class_name => "CompanyUser", :foreign_key => :creator_id

  # This is highly bad, but we need to do this to get the ability to work 
  belongs_to :contributor, foreign_key: 'value_int', optional: true 

  has_one :custom_form_attachment, dependent: :destroy # this is for the general attachment upload field
  has_many :attachment_uploads, as: :attachable, dependent: :destroy # this is for the rich text form field
  has_many :scheduled_automated_tasks, class_name: "ScheduledAutomatedTasks", dependent: :destroy

  before_validation :set_guest_from_email, unless: -> { self.module_type == 'CompanyUser' }

  before_create :set_company
  before_destroy :set_company

  after_save :update_linkable
  after_commit :update_scheduled_slas, on: [:update, :create], if: :is_helpticket?
  after_commit :trigger_value_event, on: [:update, :create]
  after_commit :populate_value_str_tokens, on: [:update, :create]
  after_commit :set_module_status_value, on: [:update, :create], if: :is_helpticket_status_field?
  after_commit :add_module_assigned_to_value, on: [:update, :create], if: :is_helpticket_assigned_to_field?
  after_commit :remove_module_assigned_to_value, on: [:destroy], if: :is_helpticket_assigned_to_field?
  after_commit :process_related_field, on: [:update, :create], if: :is_help_ticket_list_field?
  after_commit :delete_related_field, on: [:destroy], if: :is_help_ticket_list_field?
  after_commit :update_module_group_members, on: [:update, :create], if: :is_helpticket_people_list_field?
  after_commit :remove_group_members_ids, on: [:destroy], if: :is_helpticket_people_list_field?
  after_commit :set_description_value, on: [:create, :update], prepend: true, if: :is_helpticket_description_field?
  after_commit :set_subject_value, on: [:create, :update], if: :is_helpticket_subject_field?
  after_commit :set_priority_value, on: [:create, :update], if: :is_helpticket_priority_field?
  after_commit :add_module_creator_value, on: [:update, :create], if: :is_helpticket_creator_field?
  after_commit :remove_module_creator_value, on: [:destroy], if: :is_helpticket_creator_field?
  after_update :set_assinged_to, if: :is_adding_agent_allowed?
  before_save :delete_cache_key, if: -> { self.module_type == 'HelpTicket' }
  before_destroy :delete_cache_key, if: -> { self.module_type == 'HelpTicket' }
  after_destroy :trigger_value_event
  after_create :schedule_ticket_idle_state_automated_tasks, if: -> { self.module_type == 'HelpTicket' }
  after_update :schedule_ticket_idle_state_automated_tasks, if: -> { self.module_type == 'HelpTicket' }
  after_update -> { delete_options_cache_keys('location_options') }, if: -> { self.module_type == 'Location' }
  after_commit :send_survey_email, on: [:create, :update], if: :is_helpticket?
  after_update :update_quick_view, if: -> { self.module_type == 'Location' }
  after_commit :add_help_ticket_first_response, on: :update, if: :should_add_first_response?

  accepts_nested_attributes_for :custom_form_attachment, reject_if: :all_blank

  validates_uniqueness_of :value_int, scope: [:custom_form_field_id, :module_type, :module_id], :allow_nil => true
  validate :value_must_be_present

  # validates :module_type, :module_id, :presence => true

  def is_helpticket_people_list_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.field_attribute_type == "people_list" && ["assigned_to", "created_by"].include?(self.custom_form_field.name)
  end

  def should_add_first_response?
    creator&.contributor_id.present? && is_helpticket? && is_helpticket_status_field?
  end

  def add_help_ticket_first_response
    add_first_response_and_delete_slas(creator.contributor_id)
  end

  def update_quick_view
    check_quick_view_filter if saved_change_to_value_str?
  end

  def update_module_group_members
    return unless self.contributor&.group.present?
    add_group_member_ids(self.custom_form_field.name)
  end

  def add_group_member_ids(field_name)
    ticket_member_ids = JSON.parse(self.module.group_member_ids.presence || '{}')
    current_group_member_ids = self.contributor.contributor_ids_only_users
  
    if ticket_member_ids[field_name].nil?
      ticket_member_ids[field_name] = current_group_member_ids
    else
      ticket_member_ids[field_name] = (ticket_member_ids[field_name] + current_group_member_ids).uniq
    end
  
    self.module.update_column(:group_member_ids, ticket_member_ids.to_json)
  end

  def remove_group_members_ids
    return unless self.contributor&.group.present?

    field_name = self.custom_form_field.name
    ticket_member_ids = self.module.group_member_ids
    ids_to_remove = self.contributor.contributor_ids_only_users
    if ticket_member_ids.present?
      ids = JSON.parse(ticket_member_ids)
      staff_list_cfvs = self.module.custom_form_values
                            .includes(:custom_form_field)
                            .where(custom_form_fields: { name: field_name })
                            .where.not(value_int: self.value_int)
      staff_list_cfvs.find_each do |cfv|
        if cfv.contributor.group.present?
          group_member_user_ids = cfv.contributor.contributor_ids_only_users
          ids_to_remove -= group_member_user_ids
        end
      end
      ids[field_name] -= ids_to_remove
      self.module.update_column(:group_member_ids, ids.to_json) if !self.module.destroyed?
    end
  end  

  def is_helpticket_assigned_to_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.name == "assigned_to" && self.custom_form_field.field_attribute_type == "people_list"
  end

  def is_help_ticket_list_field?
    self.module_type == 'HelpTicket' && FIELD_ASSOCIATIONS[field_type].present?
  end

  def is_helpticket_creator_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.name == "created_by" && self.custom_form_field.field_attribute_type == "people_list"
  end

  def is_helpticket_status_field?
    return self.module_type == 'HelpTicket' && current_custom_form_field.name == 'status' && current_custom_form_field.field_attribute_type == "status"
  end

  def is_helpticket?
    return self.module_type == 'HelpTicket'
  end

  def current_custom_form_field
    current_field ||= self.custom_form_field
  end

  def set_module_status_value
    self.module.update_columns(status: self.value_str)
    self.module.update_columns(closed: self.value_str == 'Closed')
    if self.value_str == 'Closed'
      self.module.update_column(:closed_at, DateTime.now)
    end
    self.module.update_columns(is_new: false) if !saved_change_to_attribute?(:created_at) && self.module.is_new?
    if self.module.source == 'ms_teams' && saved_change_to_value_str? && self.value_str == 'Open'
      perform_renewal
    end
  end

  def perform_renewal
    config = Integrations::MsTeams::Config.find_by('channel_name LIKE ?', "%-#{self.module.id}")
    return if config.nil?

    cushion_time = 12.hours
    if (config.subscription_expiry_date <= Time.current + cushion_time)
      graph_client = Integrations::MsTeams::GraphClient.new(ms_teams_config: config)
      response = graph_client.renew_subscription(config.subscription_id)
      config.subscription_expiry_date = response['expirationDateTime']
      config.save
    end
  end

  def is_adding_agent_allowed?
    return false unless is_helpticket?
    return false if help_ticket.is_first_response_from_agent

    is_adding_agent_to_assigned_to_allowed?(creator) && is_status_first_response?
  end

  def is_status_first_response?
    custom_form_field.name == 'status' && custom_form_field.field_attribute_type == 'status'
  end

  def set_assinged_to
    field = CustomFormField.find_by(custom_form_id: custom_form.id, name: 'assigned_to', field_attribute_type: 'people_list')
    if help_ticket.custom_form_values.where(custom_form_field: field).blank?
      CustomFormValue.create!(
        custom_form_field_id: field.id,
        value_int: creator.contributor.id,
        custom_form_id: help_ticket.custom_form_id,
        help_ticket_id: help_ticket.id,
        company_id: help_ticket.company_id,
        module_type: 'HelpTicket',
        module_id: help_ticket.id
      )
      help_ticket.update_columns(is_first_response_from_agent: true, is_seen: true)
    end
  end

  def add_module_assigned_to_value
    help_ticket = self.module
    help_ticket.save! if help_ticket.changed?
    help_ticket.with_lock do
      assigned_user_ids = help_ticket.assigned_user_ids << self.value_int
      help_ticket.update_column(:assigned_user_ids, assigned_user_ids)
      help_ticket.update_columns(is_new: false) if help_ticket.is_new?

      if current_user_id.present? && !help_ticket.is_seen?
        help_ticket.update_columns(is_seen: true) if should_update_is_seen?
      end
      update_first_response_from_activities(help_ticket)
    end
  end
  
  def update_first_response_from_activities(help_ticket)
    return unless help_ticket.first_response_at.nil?

    contributor = Contributor.find_by(id: self.value_int)
    return unless contributor.present?

    if contributor.contributor_group
      contributor_ids = contributor.contributor_ids_only_users
    else
      contributor_ids = [contributor.id]
    end
    company_user_ids = CompanyUser.where(contributor_id: contributor_ids).pluck(:id)
    activities_scope = help_ticket.help_ticket_activities
                                  .where(owner_id: company_user_ids)
                                  .where(activity_type: ['status', 'note'])
                                  .reorder(:created_at)
    first_activity = activities_scope.find do |activity|
      if activity.activity_type == 'note'
        activity.data['new_comment_body'].present?
      else
        true
      end
    end
    if first_activity.present?
      help_ticket.update_column(:first_response_at, first_activity.created_at)
    end
  end

  def add_module_creator_value
    help_ticket = self.module
    help_ticket.save! if help_ticket.changed?
    help_ticket.with_lock do
      creator_ids = help_ticket.creator_ids << self.value_int
      help_ticket.update_column(:creator_ids, creator_ids)
    end
  end

  def field_type
    @field_type ||= self.custom_form_field.field_attribute_type
  end

  def process_related_field
    association = FIELD_ASSOCIATIONS[field_type]
    association = :related_group_ids if is_group?
    add_field_value(association)
  end

  def delete_related_field
    association = FIELD_ASSOCIATIONS[field_type]
    association = :related_group_ids if is_group?
    remove_field_value(association)
  end

  def is_group?
    field_type == 'people_list' && Contributor.find_by_id(self.value_int)&.contributor_type == 'Group'
  end

  def add_field_value(association)
    help_ticket = self.module
    help_ticket.save! if help_ticket.changed?
    
    help_ticket.with_lock do
      related_ids = help_ticket.send(association)
      # Not checking for duplicates as same value can be added from multiple fields
      related_ids << self.value_int
      help_ticket.update_column(association, related_ids)
    end
  end

  def remove_field_value(association)
    self.module.reload unless self.module.destroyed?
    help_ticket = self.module
    associated_ids = help_ticket.send(association)
    if associated_ids.include?(self.value_int)
      associated_ids.delete_at(associated_ids.index(self.value_int))
    end
    help_ticket.update_column(association, associated_ids) unless help_ticket.destroyed?
  end

  def remove_module_assigned_to_value
    self.module.reload unless self.module.destroyed?
    assigned_user_ids = self.module.assigned_user_ids - [self.value_int]
    self.module.update_column(:assigned_user_ids, assigned_user_ids) if !self.module.destroyed?
  end

  def remove_module_creator_value
    self.module.reload unless self.module.destroyed?
    creator_ids = self.module.creator_ids - [self.value_int]
    self.module.update_column(:creator_ids, creator_ids) if !self.module.destroyed?
  end

  def should_update_is_seen?
    if self.contributor&.group.present? && !bulk_item
      current_group_cont_ids = self.contributor.contributor_ids_only_users
      return current_group_cont_ids.include?(self.creator.contributor_id)
    end

    self.value_int == current_user_id
  end

  def workspace
    self.module.respond_to?(:workspace) ? self.module.workspace : nil
  end

  def attachment
    custom_form_attachment
  end

  def value=(val)
    if val.is_a?(Numeric)
      self.value_int = val
      self.value_str = nil
    else
      self.value_str = val
      self.value_int = nil
    end
  end

  def value
    value_str || value_int
  end

  def set_company
    self.company ||= self.module.company
  end

  def automated_task_value_key(field_type)
    case field_type
    when 'priority', 'status'
      field_type
    else
      'text'
    end
  end

  def schedule_ticket_idle_state_automated_tasks(at_tasks = nil)
    tasks = if at_tasks.present?
              at_tasks
            else
              AutomatedTasks::AutomatedTask
                .where(workspace: workspace, disabled_at: nil)
                .joins(task_events: :event_type)
                .includes(task_events: :event_type)
                .where(automated_tasks_event_types: { event_class: 'TicketRemainsInSameState' })
                .uniq
            end
    tasks.flat_map(&:task_events).flat_map(&:event_details).each do |detail|
      value = JSON.parse(detail.value)
      if custom_form_field.singular? && value['form_field']['name'] == custom_form_field.name && value['time_interval'].present?
        if value[automated_task_value_key(custom_form_field.field_attribute_type)] == self.value
          trigger_time = set_trigger_time(value['time_interval'].to_i.hours, value['is_business_hours'])
          task =  ScheduledAutomatedTasks.find_or_initialize_by(
                    status: 'pending',
                    help_ticket: self.ticket,
                    custom_form_value_id: self.id,
                    automated_tasks_automated_task_id: detail.task_event.automated_task_id
                  )
          task.trigger_at = trigger_time
          task.save
        end
      end
    end
  end

  def set_trigger_time(time_interval, is_business_hours)
    if is_business_hours
      calculate_business_hours_trigger_time(time_interval)
    else
      created_at == updated_at ? created_at + time_interval : updated_at + time_interval
    end
  end

  def telecom_service
    company.telecom_services.find_by(id: value_int)
  end

  def contract
    company.contracts.find_by(id: value_int)
  end

  def managed_asset
    company.managed_assets.find_by(id: value_int)
  end

  def vendor
    company.vendors.find_by(id: value_int)
  end

  def location
    company.locations.find_by(id: value_int)
  end

  def as_json(options = {})
    my_json = super(options)
    my_json = my_json.merge({ attachment: attachment.as_json })
    my_json
  end

  def update_linkable
    if self.custom_form&.company_module == 'location'
      if self.custom_form_field&.name == 'name' && self.module&.linkable
        linkable = self.module.linkable
        linkable.name = self.value_str
        linkable.save!
      end
    else
      if self.custom_form_field&.name == 'subject' && self.module&.linkable
        linkable = self.module.linkable
        linkable.name = self.value_str
        linkable.save!
      end
    end
  end

  def update_helpdesk_dashboard
    Pusher.trigger(
      self.company.guid,
      "helpdesk-dashboard-updated",
      { }
    );
  rescue => e
    create_pusher_log(e, 'custom_form_value') if (Rails.env.staging? || Rails.env.production?) && !e.class.to_s.include?('HTTPError')
  end

  def trigger_value_event
    if !self.skip_event_trigger || bulk_item
      if !self.skip_event_trigger || (bulk_item && is_last_bulk_item)
        "PusherActions::FormFieldUpdated::#{self.module_type}".constantize.new(self).call
      end
      if ["priority", "status", "assigned_to"].include?(self.custom_form_field.name) && !self.skip_sla_trigger && !self.destroyed?
        PusherActions::HelpTickets::TicketDueTime.new(self).call
      end
    end
    update_helpdesk_dashboard if self.module_type == 'HelpTicket' && !self.skip_event_trigger || is_last_bulk_item
  end

  def creator_json
    return nil unless creator.present?
    {
      id: creator.id,
      name: creator.full_name
    }
  end

  def value_must_be_present
    return unless custom_form_field
    return if %w{attachment avatar}.include?(custom_form_field.field_attribute_type)
    if custom_form_field.alpha?
      errors.add(:value_str, "must not be blank") unless value_str
    else
      errors.add(:value_int, "must not be blank") unless value_int
    end
  end

  def module_from_entity(module_type)
    company_modules = {
      "CompanyUser": "company_user",
      "Location": "location",
      "HelpTicket": "helpdesk",
    }
    company_modules[module_type.to_sym]
  end
  
  def populate_value_str_tokens
    if self.value_str.present?
      token_text = ''
      chunk_size = 20000

      truncated_str = self.value_str.byteslice(0, 1_048_576)

      truncated_str.scan(/.{1,#{chunk_size}}/m).each do |chunk|
        processed_chunk = chunk.gsub("'", "''")
        token_text << processed_chunk
      end
      
      sql = """SELECT to_tsvector('simple', coalesce('#{token_text}', ''))"""
      results = ActiveRecord::Base.connection.execute(sql)
      self.update_columns(value_str_tokens: results.as_json.last['to_tsvector'])
    elsif self.value_str.blank? && self.value_str_tokens.present?
      self.update_columns(value_str_tokens: nil)
    end
  end

  def set_guest_from_email
    # if we have a people list field AND we have a value_str, 
    # then we know this has to be converted to a guest
    if self.custom_form_field.people_list? && self.value_str
      email = value_str.downcase
      company = self.module&.company || self.company
      entity = company_user_cache(company, email)
      entity ||= company.guests.find_or_create_by(email: email, workspace: self.workspace)
      if entity.id
        self.value_int = entity.contributor_id
        self.value_str = nil
      end
    end
  end

  def is_helpticket_description_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.name == 'description' && self.custom_form_field.field_attribute_type == "rich_text"
  end

  def set_description_value
    self.module.update_columns(description: self.value_str, description_body_tokens: self.value_str_tokens)
  end

  def is_helpticket_subject_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.name == 'subject' && self.custom_form_field.field_attribute_type == 'text'
  end

  def set_subject_value
    self.module.update_column(:subject, self.value_str)
  end

  def is_helpticket_priority_field?
    return self.module_type == 'HelpTicket' && self.custom_form_field.name == 'priority' && self.custom_form_field.field_attribute_type == "priority"
  end

  def set_priority_value
    self.module.update_column(:priority, self.value_str)
  end

  def send_survey_email
    if !self.skip_survey_trigger && (saved_change_to_value_str? || saved_change_to_value_int?)
      SendCustomSurveyEmailWorker.perform_async(self.module_id)
    end
  end
end
