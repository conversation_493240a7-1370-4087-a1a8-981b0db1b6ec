ActiveAdmin.register MfaSetting do
  menu parent: 'Metrics', label: "MFA", priority: 8
  breadcrumb do
    ['admin', 'metrics']
  end
  config.filters = false

  permit_params :timeout_period
  actions :all, except: [:new, :create, :destroy]

  controller do
    around_action :set_read_replica_db, only: [:index, :show]
  end

  index do
    selectable_column
    id_column
    column :timeout_period
    column ("created_at") { |company| "#{company.created_at.to_written_time}" }
    column ("updated_at") { |company| "#{company.updated_at.to_written_time}" }
    column :company
    column :enabled_for_all_users
    column :enabled_for_self_user
    column :mfa_enabled
    column :remember_device
    column :email_auth
    column :restrict_user_access
    column :enabled_for_all_admins
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
