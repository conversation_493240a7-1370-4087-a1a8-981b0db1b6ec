ActiveAdmin.register Logs::ApiEvent, as: 'api_event' do
  menu parent: 'Logs', label: "API Event Log", priority: 2
  breadcrumb do
    ['admin', 'logs']
  end

  filter :status
  filter :resolution_status
  filter :company, as: :select, collection: Company.order("name ASC").map { |comp| [comp.name, comp.id] }
  filter :class_name, as: :select

  scope :all do |events|
    events.all
  end

  scope :clients_errors do |events|
    events.select("*").where("status = 0 AND (resolution_status = 0 OR resolution_status = 1) AND id IN (SELECT MAX(id) from api_events GROUP BY api_events.company_id, api_events.api_type, api_events.error_message)")
  end

  permit_params :resolution_status, :status

  form do |f|
    f.inputs 'Log Resolution Status' do
      f.input :resolution_status
      f.input :status
    end
    f.actions
  end

  index do
    selectable_column
    id_column
    column ("Event") { |event| "#{event.api_type}" }
    column ("Status") { |event| "#{event.status}" }
    column ("Integration") { |event| "#{event.name}" }
    column ("Activity") { |event| "#{event.activity}" }
    column ("Company") { |event| "#{event.company&.name}" }
    column ("Resolution Status") { |event| "#{event.resolution_status}" }
    column ("Time") { |event| "#{event.created_at.to_written_time}" }
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.edit'), edit_resource_path(resource), :class => "member_link edit_link"
      links
    end
  end

  show do
    attributes_table do
      row ("ID") { |event| "#{event.id}" }
      row ("Company") { |event| "#{event.company&.name}" }
      row ("class_name") { |event| "#{event.class_name}" }
      row ("api_type") { |event| "#{event.api_type}" }
      row ("integration name") { |event| "#{event.integration&.name}" }
      row ("error_detail") { |event| "#{event.error_detail}" }
      row ("activity") { |event| "#{event.activity}" }
      row ("created_at") { |event| "#{event.created_at.strftime("%b %d, %Y")}" }
      row ("updated_at") { |event| "#{event.updated_at.strftime("%b %d, %Y")}" }
      row ("error_message") { |event| "#{event.error_message}" }
      row ("response") { |event| "#{event.response}" }
      row ("resolution_status") { |event| "#{event.resolution_status}" }
    end
  end
  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
