ActiveAdmin.register Logs::SuperAdminLog, as: 'super_admin_log' do
  menu parent: 'Logs', label: "Super Admin Logs", priority: 5
  breadcrumb do
    ['admin', 'logs']
  end

  filter :email
  filter :ip_address
  filter :user, collection: proc { User.super_admins }

  actions :index, :show

  index do
    selectable_column
    column ("Email") { |log| "#{log.email}" }
    column ("IP Address") { |log| "#{log.ip_address}" }
    column ("User") { |log| "#{log.user.full_name}" }
    column ("Logged In At") { |log| "#{log.logged_in_at.to_written_time}" }
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
