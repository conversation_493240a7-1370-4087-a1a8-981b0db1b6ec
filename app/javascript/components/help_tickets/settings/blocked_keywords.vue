<template>
  <div class="clearfix row">
    <sub-menu />
    <table class="blocked-table mt-5 table-width">
      <thead>
        <workspace-settings-banner />
        <tr>
          <th class="mb-2">
            Blocked Email Keywords
          </th>
          <th/>
        </tr>
      </thead>
      <tbody>
        <tr
          v-if="!blockedEmailKeywords"
          class="py-1 blocked-value"
        >
          <td colspan="3">
            Loading...
          </td>
        </tr>
        <tr
          v-else-if="blockedEmailKeywords.length == 0"
          class="py-1 blocked-value text-muted"
        >
          <td colspan="3">
            No email keywords are being blocked.
          </td>
        </tr>
        <span v-else>
          <tr
            v-for="item in blockedEmailKeywords"
            :key="item.id"
            class="py-1 blocked-value"
          >
            <td>
              {{ item.entity }}
            </td>
            <td>
              <span @click="openUnblock(item)">
                <i class="remove-link nulodgicon-trash-b" />
              </span>
            </td>
          </tr>
        </span>
      </tbody>
    </table>

    <sweet-modal
      ref="unblockConfirmModal"
      v-sweet-esc
      title="Are you sure you want to unblock these items?"
    >
      <template slot="default">
        <div class="text-center">
          <p>Are you sure you want to unblock this?</p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click="closeUnblockConfirmModal"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click="submitUnblockItems"
      >
        Yes, I'm sure
      </button>
    </sweet-modal>

    <sweet-modal
      ref="blockModal"
      v-sweet-esc
      title="Block Email Keywords"
    >
      <template slot="default">
        <div class="h6 mb-3">
          Please fill in the information below to block email keywords
        </div>

        <form>
          <div class="form-group">
            <label class="mt-3">
              Enter value:
            </label>
            <input
              v-model="entity"
              class="form-control"
              placeholder="e.g Automatic Reply"
            >
          </div>
        </form>
      </template>

      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click="closeBlockModal"
      >
        Cancel
      </button>

      <button
        slot="button"
        :disabled="!entity"
        class="btn btn-primary ml-2"
        @click="submitBlockEntity"
      >
        Block
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import { mapMutations } from 'vuex';
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import SubMenu from './sub_menu.vue';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';


  export default {
    components: {
      SubMenu,
      SweetModal,
      workspaceSettingsBanner,
    },
    mixins: [ permissionsHelper ],
    data() {
      return {
        blockedEmailKeywords: null,
        entityType: null,
        entity: null,
      };
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),

      onWorkspaceChange() {
        this.fetchBlockedKeywords();
      },
      openModal() {
        this.$refs.blockModal.open();
      },
      submitBlockEntity() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          this.emitError("Must have a workspace selected.");
          return;
        }

        http
          .post(`/blocked_entities`, { entities: [this.entity], entity_type: 'keyword' })
          .then(() => {
            this.closeBlockModal();
            this.emitSuccess('Successfully added blocked keyword.');
            this.fetchBlockedKeywords();
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error adding this value: ${error.response.data.message}`);
          });
      },
      closeBlockModal() {
        this.$refs.blockModal.close();
        this.entity = null;
        this.entityType = null;
      },
      openUnblock(entity) {
        this.$refs.unblockConfirmModal.open();
        this.selectedEntity = entity;
      },
      submitUnblockItems() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          this.emitError("Must have a workspace selected.");
          return;
        }
        http
          .delete(`/blocked_entities/${this.selectedEntity.id}.json`)
          .then(() => {
            this.emitSuccess('Successfully unblocked item.');
            this.closeUnblockConfirmModal();
            this.fetchBlockedKeywords();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error unblocking the item.`);
          });
      },
      closeUnblockConfirmModal() {
        this.$refs.unblockConfirmModal.close();
      },

      fetchBlockedKeywords() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          return;
        }

        this.setLoadingStatus(true);

        http
          .get('/blocked_entities.json', { params: { keywords: true } })
          .then((res) => {
            if (res.data.keywords) {
              this.blockedEmailKeywords = res.data.keywords;
            } else {
              this.blockedEmailKeywords = [];
            }
            this.setLoadingStatus(false);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error loading blocked keywords. ${error}`);
            this.setLoadingStatus(false);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .blocked-value {
    display: flex;
    justify-content: space-between;
  }
  .table-width {
    max-width: 60%;
  }
</style>
