ActiveAdmin.register_page "Companies Status" do
  menu parent: "Metrics", label: "Companies Status", priority: 3
  breadcrumb do
    ['admin', 'metrics']
  end
   
  controller do
    include ActionView::Helpers::NumberHelper
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def index
      @total_companies = Company.all.count
      @trial_companies = trial_companies_counts
      @paid_companies = paid_companies_counts
      @daily_active_companies = active_companies "daily"
      @weekly_active_companies = active_companies "weekly"
      @monthyl_active_companies = active_companies "monthly"
    end

    def trial_companies_counts
      Company.where("(CAST (TO_CHAR(companies.created_at, 'J') AS INTEGER) + free_trial_days) - CAST(TO_CHAR(now(), 'J') AS INTEGER) > 0").count
    end

    def paid_companies_counts
      Company.joins(:subscriptions)
             .where(subscriptions: { status: ["active", "canceled"] })
             .where("subscriptions.end_date IS NULL OR subscriptions.end_date > ?", Date.today)
             .distinct
             .count
    end

    def active_companies value
      Company.all.where('last_logged_in_at > ?', duration(value)).count
    end

    def duration(value)
      case value
      when "daily"
        DateTime.now.in_time_zone('America/Chicago').beginning_of_day
      when "weekly"
        DateTime.now.in_time_zone('America/Chicago').beginning_of_day - 7.days
      when "monthly"
        DateTime.now.in_time_zone('America/Chicago').beginning_of_day - 1.month
      end
    end
  end

  content title: "Companies Status" do
    render partial: "admin/metrics/companies_status"
  end
end
