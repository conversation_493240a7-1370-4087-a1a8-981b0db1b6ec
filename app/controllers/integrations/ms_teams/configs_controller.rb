class Integrations::MsTeams::ConfigsController < AuthenticatedController
  def index
    render json: ms_teams_configs, status: :ok
  end

  def destroy
    if Integrations::MsTeams::Config.find(params[:id]).destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: 'MS Teams configuration not found' }, status: :not_found
  end

  def send_connector_status_update
    @config = Integrations::MsTeams::Config.find(params['id'].to_i)
    @config.active ? send_channel_deactivation_message : send_channel_activation_message
    render json: {}, status: :ok
  rescue ActiveRecord::RecordNotFound => e
    render json: { message: 'Channel not found. Please refresh the page and try again.' }, status: :not_found
  end

  private

  def ms_teams_configs
    current_company.ms_teams_configs.as_json
  end

  def send_channel_activation_message
    ms_teams_service.call('channel_activation_message', { message: 'This channel has been activated from Genuity web app.', config_id: @config.id })
  end

  def send_channel_deactivation_message
    ms_teams_service.call('channel_activation_message', { message: 'This channel has been deactivated from Genuity web app.', config_id: @config.id })
  end

  def ms_teams_service
    Integrations::MsTeams::Client.new(@config)
  end
end
