<template>
  <div>
    <logs-sub-menu 
      module="Company"
      module-link="/event_logs"
      entity-name="User"
      entity-link="/user_activities"
    />
    <div class="row position-relative align-items-end mb-4">
      <div class="col-lg-5 col-md-4 col-sm-12 mb-md-0 mb-sm-2">
        <div class="search-wrap">
          <i
            class="nulodgicon-ios-search-strong search-input-icon"
            @click.prevent="$refs.searchInput.focus"
          />
          <input
            ref="searchInput"
            v-model="searchTerms"
            type="text"
            class="form-control search-input mt-1"
            placeholder="Search logs by username"
            data-tc-activity-search
            @input="searchActivities"
          >
        </div>
      </div>
      <div class="col-lg-7 col-md-8">
        <div class="d-flex justify-content-end">
          <div
            v-if="pageCount > 1"
            class="clearfix d-inline ml-3"
          >
            <paginate
              ref="paginate"
              class="float-right mb-0"
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="indexPage"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="activities && activities.length == 0"
      class="mt-5 text-center"
    >
      <h3 class="text-secondary mb-4">
        No activities present.
      </h3>
    </div>
    <div
      v-else-if="activities && activities.length > 0"
      class="my-1"
    >
      <div class="row py-2 align-items-center font-weight-bold mt-2">
        <div class="col-12 pr-sm-2 pr-md-3 d-flex">
          <header class="col-2">
            User Name
          </header>
          <header class="col-3 ml-4">
            Actions
          </header>
          <header class="col-5 ml-2">
            What Changed
          </header>
          <header class="col-2 pl-2 text-center">
            Performed By
          </header>
        </div>
      </div>
      <div
        v-for="activity in activities"
        :key="activity.id"
        class="activity w-100"
      >
        <div class="row py-2 align-items-center d-flex w-100">
          <div
            class="col-12 d-flex"
            @click="goToUser(activity)"
          >
            <td class="col-2 mt-1">
              <span class="font-weight-bold text-capitalize"> {{ activity.userName }} </span>
            </td>
            <td
              v-if="activity.activityType == 'created' || activity.activityType == 'imported'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="mt-1 font-weight-bold">              
                    <span class="action-icon action-icon-edit position-relative align-middle mr-2">
                      <i
                        class="position-absolute nulodgicon-edit"
                        style="bottom: -1px"
                      />
                    </span>
                    Created
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    User was created
                    <span 
                      v-if="activity.activityType == 'created'"
                    >
                      manually
                    </span>
                    <span 
                      v-else-if="activity.activityType == 'imported'"
                    >
                      through integration
                    </span>
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'added'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="mt-1 font-weight-bold">              
                    <span class="action-icon action-icon-edit position-relative align-middle mr-2">
                      <i
                        class="position-absolute nulodgicon-edit"
                        style="bottom: -1px"
                      />
                    </span>
                    {{ snakeToHumanize(activity.data["activityLabel"]) }} Added
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    <span class="font-weight-bold">{{ activity.data["currentValue"] }}</span> 
                    was added as the
                    {{ snakeToHumanize(activity.data["activityLabel"]) }}
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'removed'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span class="action-icon action-icon-edit position-relative align-middle mr-2">
                      <i
                        class="position-absolute nulodgicon-edit"
                        style="bottom: -1px"
                      />
                    </span>
                    {{ snakeToHumanize(activity.data["activityLabel"]) }} Removed
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    <span class="font-weight-bold">{{ activity.data["previousValue"] }}</span> 
                    was removed as the
                    {{ snakeToHumanize(activity.data["activityLabel"]) }}
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'updated'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span
                      class="action-icon action-icon-edit position-relative align-middle mr-2"
                    >
                      <i
                        class="position-absolute nulodgicon-edit"
                        style="bottom: -1px"
                      />
                    </span>
                    {{ snakeToHumanize(activity.data["activityLabel"]) }} Updated
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    <span class="font-weight-bold">
                      {{ snakeToHumanize(activity.data["activityLabel"]) }}
                    </span> 
                    was updated 
                    <span v-if="activity.data['previousValue']"> 
                      from 
                      <span class="font-weight-bold"> {{ activity.data["previousValue"] }}  </span>
                    </span> 
                    to 
                    <span class="font-weight-bold">
                      {{ activity.data["currentValue"] }}
                    </span>
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'permission'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span
                      class="action-icon action-icon-permission position-relative align-middle mr-2"
                    >
                      <i
                        class="genuicon-permissions"
                        style="bottom: -1px"
                      />
                    </span>
                    Permission Updated
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    <span class="font-weight-bold">{{ humanizeWord(activity.data["activityLabel"]) }}</span> Permissions was updated to <b>{{ toTitle(activity.data["currentValue"]) }}</b>
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'invited'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <div
                    class="font-weight-bold"
                  >              
                    <span class="action-icon action-icon-email position-relative align-middle mr-2">
                      <i
                        class="position-absolute nulodgicon-email"
                        style="bottom: -1px"
                      />
                    </span>
                    Invited
                  </div>
                </div>
                <div class="col-8">
                  <div class="mt-2">
                    User was <span class="font-weight-bold"> {{ activity.data["currentValue"] }} </span> to this company.
                  </div>
                </div>
              </div>
            </td>
            <td
              v-else-if="activity.activityType == 'archived'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span class="action-icon action-icon-archive position-relative align-middle mr-2">
                      <i
                        v-if="activity.data['currentValue'] == 'archived'"
                        class="position-absolute nulodgicon-archive"
                        style="bottom: -1px"
                      />
                      <i
                        v-else
                        class="position-absolute nulodgicon-unarchive"
                        style="bottom: -1px"
                      />
                    </span>
                    <span class="text-capitalize">{{ activity.data["currentValue"] }}</span>
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if=" activity.activityType == 'access'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span
                      class="action-icon action-icon-delete position-relative align-middle mr-2"
                    >
                      <i
                        v-if="activity.data['currentValue'] == 'revoked'"
                        class="nulodgicon-trash-b"
                        style="bottom: -1px"
                      />
                    </span>
                    Access <span class="text-capitalize">{{ activity.data["currentValue"] }}</span>
                  </span>
                </div>
              </div>
            </td>
            <td
              v-else-if=" activity.activityType == 'email_requested'"
              class="col-8"
            > 
              <div class="row">
                <div class="col-4">
                  <span class="font-weight-bold">              
                    <span
                      class="action-icon action-icon-email position-relative align-middle mr-2"
                    >
                      <i
                        class="nulodgicon-email"
                        style="bottom: -1px"
                      />
                    </span>
                    Email Requested
                  </span>
                </div>
                <div class="col-8">
                  <span class="mt-2">
                    Email is requested to change
                    <span v-if="activity.data['previousValue']"> 
                      from 
                      <span class="font-weight-bold"> {{ activity.data["previousValue"] }}  </span>
                    </span> 
                    to 
                    <span class="font-weight-bold">
                      {{ activity.data["currentValue"] }}
                    </span>
                  </span>
                </div>
              </div>
            </td>
            <td class="col-2 pr-0 small text-right text-muted p--responsive">
              <span
                v-if="activity.activityType == 'imported'"
              > 
                {{ sourceName(activity) }}
              </span>
              <span
                v-else
              > 
                {{ owner(activity) }}
              </span>
              <br>
              <span class="small">{{ createdAt(activity) }}</span>
            </td>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="my-5 clearfix"
    >
      <clip-loader
        :loading="true"
        class="ml-3 mt-1"
        color="#0d6efd"
        size="1.5rem"
      />
    </div>
  </div>
</template>
<script>
  import { mapGetters, mapMutations } from 'vuex';
  import _debounce from 'lodash/debounce';
  // eslint-disable-next-line no-unused-vars
  import _get from 'lodash/get';
  // eslint-disable-next-line no-unused-vars
  import _filter from 'lodash/filter';
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import ClipLoader from 'vue-spinner/src/ClipLoader.vue';
  import search from 'mixins/search';
  import momentTimezone from 'mixins/moment-timezone';
  import customFormFields from 'mixins/custom_forms/fields';
  import companyUser from 'mixins/company_user';
  import permissionsHelper from 'mixins/permissions_helper';
  import strings from 'mixins/string';
  import LogsSubMenu from '../../shared/logs_sub_menu.vue';

  export default {
    components: {
      Paginate,
      ClipLoader,
      LogsSubMenu,
    },
    mixins: [
      companyUser, 
      customFormFields,
      momentTimezone, 
      permissionsHelper,
      search, 
      strings,
    ],
    data() {
      return {
        activities: null,
        offsetRow: false,
        searchTerms: "",
        perPage: 30,
        pageCount: 0,
        indexPage: 0,
        activityTypes: [],
        activityType: null,
        changesArr: [],
      };
    },
    computed: {
      ...mapGetters(['loadingStatus']),
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),

      searchActivities: _debounce(
        function () {
          this.fetchActivitiesAndSetPage();
        }, 
        500
      ),

      onWorkspaceChange() {
        this.fetchActivityTypes();

        const params = {archived : true};
        this.$store.dispatch("fetchCompanyUserOptions", params);
        this.fetchActivities();
      },

      fetchActivityTypes() {
        http
          .get('/activity_types.json')
          .then(res => {
            this.activityTypes = res.data.activityTypes;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error fetching activity types.`);
          });
      },
      fetchActivitiesAndSetPage() {
        this.indexPage = 0;
        this.fetchActivities();
      },
      fetchActivities() {
        const params = {
          search_terms: this.searchTerms,
          per_page: this.perPage,
          page: this.indexPage + 1,
          activity_type: this.activityType,
        };
        const url = '/user_activities.json';
        http
          .get(url, { params })
          .then(res => {
            this.activities = res.data.activities;
            this.pageCount = res.data.pageCount;
            this.setLoadingStatus(false);
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error fetching activities.`);
          });
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.setLoadingStatus(true);
        this.activities = null;
        this.fetchActivities();
      },
      createdAt(activity) {
        return this.timezoneMoment(activity.createdAt, Vue.prototype.$timezone);
      },
      owner(activity) {
        if (!activity.ownerName) {
          return "System";
        }
        return activity.ownerName;
      },
      goToUser(activity){
        const url = `/company/users/${activity.companyUserId}`;
        window.open(url, '_blank');
      },
      sourceName(activity) {
        if (!activity.source) {
          return this.owner(activity);
        }
        if (activity.source === "microsoft_active_directory") {
          return "Microsoft Entra ID";
        } else if (activity.source === "Gsuite active directory") {
          return "Gsuite cloud directory";
        } 
        return activity.source.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '); 
      },
    },
    watch: {
      searchTerms() {
        this.indexPage = 0;
      },
    },
  };
</script>

<style lang="scss" scoped>
.activity {
  border-top: 1px solid $themed-fair;
  .activity-item {
    color: $themed-dark;
  }
  &:hover {
    background-color: $themed-lighter;
  }
}

.d-flex--responsive {
  @media($max: $medium) {
    display: flex !important;
    flex-direction: row;
    justify-content: flex-end;
  }
  @media($max: $small) {
    flex-direction: column;
    align-items: flex-end;
  }
}
.action-icon-edit { background-color: $yellow }
.action-icon-archive { background-color: $themed-secondary; }
.action-icon-email { background-color: $green; }
.action-icon-permission { background-color: $cyan }
.action-icon-delete { background-color: $red }
</style>
