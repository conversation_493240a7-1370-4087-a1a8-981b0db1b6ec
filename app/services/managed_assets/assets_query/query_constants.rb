module ManagedAssets
  module AssetsQuery
    module QueryConstants

      ASSET_SOURCES_JOIN = """
        INNER JOIN asset_sources
          ON asset_sources.managed_asset_id = managed_assets.id
      """

      ASSET_LOCATION_JOIN = """
        LEFT JOIN locations
          ON locations.id = managed_assets.location_id
      """

      ASSIGNMENT_JOINS = """
        LEFT OUTER JOIN assignment_informations
          ON assignment_informations.managed_asset_id = managed_assets.id
      """

      ASSIGNMENT_DEPARTMENT_JOINS = """
        LEFT JOIN assignment_informations AS assignment_department
          ON assignment_department.managed_asset_id = managed_assets.id
      """

      ASSET_INFORMATION = """
        managed_assets.id,
        managed_assets.name,
        managed_assets.source as source,
        managed_assets.location_id,
        NULLIF (managed_assets.description, '') as description,
        managed_assets.warranty_expiration,
        managed_assets.manufacturer,
        NULLIF (managed_assets.model, '') as model,
        managed_assets.impact,
        managed_assets.install_date,
        managed_assets.archived,
        managed_assets.asset_tag,
        managed_assets.mac_addresses,
        managed_assets.ip_address,
        managed_assets.machine_serial_number,
        managed_assets.acquisition_date,
        managed_assets.vendor_id,
        managed_assets.department,
        managed_assets.hardware_detail_type,
        managed_assets.hardware_detail_id,
        managed_assets.company_asset_type_id,
        managed_assets.company_asset_status_id,
        managed_assets.agent_location_id,
        managed_assets.product_number,
        managed_assets.company_id,
        NULLIF (managed_assets.firmware, '') as firmware
      """

      OPERATING_SYSTEM = """
        (
          SELECT COALESCE(json_agg(
            json_build_object(
              'id', id::int,
              'name', name::text
            )
          ), '[]') as operating_systems
          FROM asset_softwares
          where managed_asset_id = managed_assets.id AND software_type = 'Operating System'
        )
      """

      ASSET_TAGS = """
        (
          SELECT COALESCE(json_agg(
            json_build_object(
              'id', managed_asset_tags.id::int,
              'name', comp_asset_tags.name::text
            )
          ), '[]') as tags
          FROM managed_asset_tags AS managed_asset_tags
          INNER JOIN company_asset_tags AS comp_asset_tags ON comp_asset_tags.id = managed_asset_tags.company_asset_tag_id
          where managed_asset_id = managed_assets.id
        )
      """

      TAG_FILTER_SUBQUERY = """
        SELECT DISTINCT managed_asset_tags.managed_asset_id
        FROM managed_asset_tags
        INNER JOIN company_asset_tags ON company_asset_tags.id = managed_asset_tags.company_asset_tag_id
        WHERE company_asset_tags.id IN (?)
      """

      ASSET_WARRANTY = """
        (
          CASE
          WHEN managed_assets.warranty_expiration > CURRENT_DATE + INTERVAL '3 months'
            THEN 'in_warranty'
          WHEN managed_assets.warranty_expiration > CURRENT_DATE
            AND managed_assets.warranty_expiration <= CURRENT_DATE + INTERVAL '3 months'
            THEN 'expiring_warranty'
          WHEN managed_assets.warranty_expiration <= CURRENT_DATE
            THEN 'expired_warranty'
          ELSE
            'no_warranty'
          END
        ) warranty_status
      """

      ASSET_SOURCES = """
        (
          SELECT COALESCE(array_agg(source), array[]::integer[]) as sources
          FROM asset_sources ass_src
          where managed_asset_id = managed_assets.id
        )
      """

      def self.help_tickets_clause
        """
          (
            SELECT COALESCE(json_agg(
              json_build_object(
                'id', cfv.module_id::int
              )
            ), '[]') as help_tickets
            FROM custom_form_values cfv
            INNER JOIN custom_form_fields AS cff ON cff.id = cfv.custom_form_field_id
            INNER JOIN help_tickets ht ON ht.id = cfv.module_id
            WHERE cfv.value_int = managed_assets.id AND cfv.module_type = 'HelpTicket'
            AND cff.field_attribute_type = #{CustomFormFieldTemplate.field_attribute_types['asset_list']}
            AND ht.archived = false
            AND ht.status <> 'Closed'
          )
        """
      end

      SEARCH_CLAUSE_JOIN = """
        LEFT OUTER JOIN assignment_informations
          ON assignment_informations.managed_asset_id = managed_assets.id
      """

      COMPUTER_DETAILS_JOIN = """
        INNER JOIN computer_details
          ON computer_details.id = managed_assets.hardware_detail_id
      """

      ASSET_SOFTWARE_JOIN = """
        LEFT JOIN asset_softwares
          ON asset_softwares.managed_asset_id = managed_assets.id
      """

      OPERATING_SYSTEM_JOIN = """
        LEFT JOIN asset_softwares AS aso
          ON aso.managed_asset_id = managed_assets.id
          AND aso.software_type = 'Operating System'
          AND aso.archived_at IS NULL
      """

      def self.search_by_field(params)
        """
          managed_assets.name ILIKE '%\\#{params[:search]}%'
          OR managed_assets.description ILIKE '%\\#{params[:search]}%'
          OR managed_assets.machine_serial_number ILIKE '%\\#{params[:search]}%'
          OR managed_assets.ip_address ILIKE '%\\#{params[:search]}%'
          OR managed_assets.manufacturer ILIKE '%\\#{params[:search]}%'
          OR array_to_string(managed_assets.mac_addresses, '||') ILIKE '%\\#{params[:search]}%'
          OR managed_assets.asset_tag ILIKE '%\\#{params[:search]}%'
          OR managed_assets.model ILIKE '%\\#{params[:search]}%'
        """
      end
      def self.search_used_by_user_name(params, scoped_companies)
        """
          OR assignment_informations.used_by_contributor_id  = ANY (
            SELECT contributors.id
            FROM contributors
            INNER JOIN company_users
            ON contributors.id = company_users.contributor_id
            INNER JOIN users
            ON users.id = company_users.user_id
            WHERE company_users.type IN ('CompanyMember')
            AND company_users.archived_at IS NULL
            AND company_users.company_id IN (#{scoped_companies.join(',')})
            AND (users.first_name ILIKE '%\\#{params[:search]}%'
            OR users.last_name ILIKE '%\\#{params[:search]}%'
            OR users.email ILIKE '%\\#{params[:search]}%'
            OR CONCAT(users.first_name, ' ', users.last_name) ILIKE '%\\#{params[:search]}%')
          )
        """
      end

      def self.search_used_by_group_name(params, scoped_companies)
        """
          OR assignment_informations.used_by_contributor_id  = ANY (
            SELECT contributors.id
            FROM contributors
            INNER JOIN groups
            ON contributors.id = groups.contributor_id
            AND groups.company_id IN (#{scoped_companies.join(',')})
            AND groups.name ILIKE '%\\#{params[:search]}%'
          )
        """
      end

      def self.search_managed_by_user_name(params, scoped_companies)
        """
          OR assignment_informations.managed_by_contributor_id  = ANY (
            SELECT contributors.id
            FROM contributors
            INNER JOIN company_users
            ON contributors.id = company_users.contributor_id
            INNER JOIN users
            ON users.id = company_users.user_id
            WHERE company_users.type IN ('CompanyMember')
            AND company_users.archived_at IS NULL
            AND company_users.company_id IN (#{scoped_companies.join(',')})
            AND (users.first_name ILIKE '%\\#{params[:search]}%'
            OR users.last_name ILIKE '%\\#{params[:search]}%'
            OR users.email ILIKE '%\\#{params[:search]}%'
            OR CONCAT(users.first_name, ' ', users.last_name) ILIKE '%\\#{params[:search]}%')
          )
        """
      end

      def self.search_managed_by_group_name(params, scoped_companies)
        """
          OR assignment_informations.managed_by_contributor_id  = ANY (
            SELECT contributors.id
            FROM contributors
            INNER JOIN groups
            ON contributors.id = groups.contributor_id
            AND groups.company_id IN (#{scoped_companies.join(',')})
            AND groups.name ILIKE '%\\#{params[:search]}%'
          )
        """
      end
    end
  end
end
