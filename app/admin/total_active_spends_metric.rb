ActiveAdmin.register_page "Total Active Spends" do
  menu parent: "Metrics", label: "Total Active Spends", priority: 2
  breadcrumb do
    ['admin', 'metrics']
  end
  controller do
    include ActionView::Helpers::NumberHelper
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def index
      @fixed_term_contract_spends = active_companies_fixed_term_contract_spends
      @fixed_term_contract_spends = "$#{number_to_human(@fixed_term_contract_spends)}"
      @open_ended_contract_spends = active_companies_open_ended_contract_spends
      @open_ended_contract_spends = "$#{number_to_human(@open_ended_contract_spends)}"

      @vendors_spends = active_companies_vendors_spends
      @vendors_spends = "$#{number_to_human(@vendors_spends)}"

      @transactions_sum = active_companies_transactions_spends
      @transactions_sum = "$#{number_to_human(@transactions_sum)}"

      @telecom_services_sum = active_companies_telecom_services_spends
      @telecom_services_sum = "$#{number_to_human(@telecom_services_sum)}"
    end

    def active_companies_fixed_term_contract_spends
      Contract.left_outer_joins(company: :subscriptions)
        .where(active_company_where_condition)
        .where(contract_type: "fixed_term")
        .sum(:contract_value_amount)
    end

    def active_companies_open_ended_contract_spends
      Contract.left_outer_joins(company: :subscriptions)
        .where(active_company_where_condition)
        .where(contract_type: "open_ended")
        .sum(:monthly_cost)
    end

    def active_companies_vendors_spends
      GeneralTransaction.active.joins(:vendor)
        .left_outer_joins(company: :subscriptions)
        .where(active_company_where_condition)
        .sum(:amount)
    end
    
    def active_companies_transactions_spends
      GeneralTransaction.active.left_outer_joins(company: :subscriptions)
        .where(active_company_where_condition)
        .sum(:amount)
    end

    def active_companies_telecom_services_spends
      TelecomService.active.left_outer_joins(company: :subscriptions)
        .where(active_company_where_condition)
        .sum(:monthly_cost)
    end

    def active_company_where_condition
      "subscriptions.company_id = companies.id AND subscriptions.end_date IS NULL OR (CAST (TO_CHAR(companies.created_at, 'J') AS INTEGER) + free_trial_days) - CAST(TO_CHAR(now(), 'J') AS INTEGER) > 0"
    end
  end

  content title: "Total Active spends" do
    render partial: "admin/metrics/total_active_spends_metric"
  end
end
