<template>
  <div>
    <!-- Show blank metric card state if no metric card data 
      This was logically set to false but should probably be re-included as a helpful user aid
    -->
    <span
      v-if="displaySampleDataWithoutAgent"
      class="box--disabled-overlay t-0"
    />
    <div
      v-if="displaySampleDataWithoutAgent"
      class="position-absolute b-0 mb-6 w-100 pr-4 z-index-100"
    >
      <div
        class="bg-themed-modal-sticky-footer w-auto2 mx-auto text-center px-3 py-2 mt-6 rounded text-secondary d-flex align-items-center sample-metric-label-width"
      >
        Sample metric data
      </div>
    </div>
    <!-- END Show blank metric card state if no metric card data -->

    <div
      class="box box--with-title box--natural-height bg-very-light widget-card"
      :class="{
        'pb-4': moduleName !== 'assetConnector',
        'h-100': hideClientStats
      }"
    >
      <div class="d-flex w-100">
        <div 
          class="pl-0"
          :class="moduleName === 'assetConnector' ? 'col-12' : 'col-6'"
        >
          <h6
            :class="{
              'text-muted': !chartHasData,
              'd-flex': cardIconClass,
              'font-weight-semi-bold': moduleName === 'assetConnector',
              'font-weight-normal my-1' : moduleName !== 'assetConnector'
            }"
          >
            <i
              v-if="cardIconClass"
              class="ml-n1 mr-1 mt-0.5 mb-n0.5"
              :class="cardIconClass"
            />
            {{ insightCardName }}
          </h6>
        </div>
        <div class="col-6 pr-0 ">
          <div class="row align-items-center justify-content-end mt-n1 pr-1">
            <div
              v-if="displayData && moduleName != 'SavedReport'"
            >
              <chart-type-button
                :show-horizontal-bar="showHorizontalBar"
                :default-type="chartType"
                @set-chart-filter="setChartType"
              />
            </div>
            <div
              v-if="cardIconClass"
              class="ml-3"
            >
              <button
                v-if="displayData"
                v-tooltip="`Move Metric`"
                class="handle icon-button shadow-none ml-n1 text-dark" 
              >
                <i class="genuicon-arrows mt-1" />
              </button>
              <button
                v-if="displayData && isWrite"
                v-tooltip="`Customize Metric`"
                class="icon-button shadow-none ml-n1 text-dark"
                @click="openCustomizeDataModal"
              >
                <i class="nulodgicon-edit mt-1" />
              </button>
              <button
                v-if="displayData && isCustomWidgetCard"
                v-tooltip="`Delete Metric`"
                class="icon-button ml-n1"
                @click="removeWidget"
              >
                <i class="nulodgicon-android-close shadow-none" />
              </button>
              <Teleport to="body">
                <sweet-modal
                  :key="data.id"
                  ref="customizeDataModal"
                  v-sweet-esc
                  modal-theme="right"
                  :width="'60%'"
                  @close="handleModalClose"
                >
                  <template slot="title">
                    <div class="d-flex align-items-center h-100">
                      <h4 class="mb-0">
                        <span
                          v-if="data.icon"
                          class="h5 mr-0.5 pr-1 pl-2 mb-0 ml-0 pt-1 mt-0.5"
                          :class="data.icon"
                        /> 
                        {{` ${cardTitle}` }}
                      </h4>
                    </div>
                  </template>
                  <CustomizeMetricModal
                    ref="customizeMetricModal"
                    :data="data"
                    :type="cardTitle"
                    @widget-updated="$emit('widget-updated', $event)"
                    @close-modal="closeCustomizeDataModal"
                  />
                </sweet-modal>
              </Teleport>
            </div>
            <div
              v-if="isHelpTicketOrReportModule"
              class="ml-2"
            >
              <button
                v-if="chartHasData"
                class="icon-button shadow-none ml-n1"
                @click="openDetailModel"
              >
                <i class="genuicon-expand mt-1" />
              </button>
              <button
                v-if="moduleName != 'SavedReport'"
                class="handle icon-button shadow-none ml-n1"
              >
                <i class="genuicon-arrows mt-1" />
              </button>
              <button
                v-if="moduleName != 'SavedReport' && moduleName == 'tickets'"
                v-tooltip="`Customize Analytics Card`"
                class="icon-button shadow-none ml-n1 text-dark"
                @click="openEditCardModel"
              >
                <i class="nulodgicon-edit mt-1" />
              </button>
              <button
                v-if="moduleName != 'SavedReport'"
                class="icon-button ml-n1"
                @click="removeWidget"
              >
                <i class="nulodgicon-android-close shadow-none" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="isHelpTicketOrReportModule"
        class="d-flex w-100 mt-n1 card-description-height"
      >
        <span class="small text-secondary mt-0.5">
          {{ cardDescription }}
        </span>
      </div>
      <div
        v-if="isHelpTicketOrReportModule"
        class="d-flex w-auto mt-2"
      >
        <span
          v-tooltip.top="`Customize Analytics Card Metric`"
          class="text-themed-link d-inline-block bg-primary rounded-pill btn btn-xs bg-blue-subtle text-primary px-2 ml-n0.5 mb-3"
          @click="openEditCardModel"
        >
          <span class="icon nulodgicon-stats-bars" />
          {{insightCardTitle}} metric
        </span>
        
        <div v-if="metricInfo.filters.length">
          <span
            v-tooltip.top="`Customize Analytics Card Filters`"
            class="bg-cyan-subtle text-info rounded-pill btn btn-xs mb-3 px-2 d-inline-flex align-items-center ml-2" 
            @click="openEditCardModel"
          >
            <i class="genuicon-android-funnel mr-1" />
            <span class="align-items-center badge badge-info d-inline-flex justify-content-center ml-1 rounded-circle">
              {{metricInfo.filters.length}}
            </span>
          </span>
        </div>
      </div>


      <div
        v-if="moduleName !== 'assetConnector'"
        class="d-flex w-100 mt-n1 card-description-height"
      >
        <span
          v-if="data.description"
          class="small text-secondary"
        >
          {{ data.description }}
        </span>
      </div>
      <div 
        v-if="moduleName !== 'assetConnector'"
        class="d-flex w-100 mt-2 card-options-height" 
      >
        <div
          v-if="truncatedSelectedOptions && truncatedSelectedOptions.length > 0"
          @click="openCustomizeDataModal"
        >
          <span
            v-tooltip.center="`Customize Metric`"
            class="text-themed-link d-inline-block bg-primary cursor-pointer rounded-pill btn btn-xs bg-blue-subtle text-primary px-2 ml-n0.5"
          >
            {{ truncatedSelectedOptions }}
          </span>
        </div>
      </div>
      <div
        v-if="(!data.data || isDataEmpty(data.data)) && !isAssetRiskCenter"
        class="w-100 chart-placeholder-wrapper"
      >
        <div class="chart-placeholder"/>
        <div class="text-muted small text-center mt-5 pt-3 mx-3">
          <span>
            Nothing to show yet for this Insight. Please add more <span class="text-lowercase">{{ cardTitle }}</span> information to your {{ moduleName }}. 
          </span>
        </div>
      </div>

      <div
        v-else-if="displayData"
        class="w-100 position-relative"
        :class="[hideClientStats ? 'd-flex' : '']"
      > 
        <span
          v-if="displaySampleDataWithAgent"
          class="box--disabled-overlay t-0"
        />
        <div
          v-if="displaySampleDataWithAgent"
          class="position-absolute mt-7 w-100 z-index-100"
        >
          <div
            class="bg-themed-modal-sticky-footer w-auto2 mx-auto text-center px-3 py-2 mb-1 mt-6 rounded text-secondary d-flex align-items-center sample-metric-label-width"
          >
            Sample metric data
          </div>
          <span
            class="text-muted small text-center p-2 mb-1 blank-state-alert"
          >
            Please customize your 
            <a 
              class="text-lowercase font-weight-semi-bold alert-link"
              @click="openCustomizeDataModal"
            >
              {{ cardTitle }} settings
            </a> 
            for your {{ moduleName }}.
          </span>
        </div>
        <insights-horizontal-bar
          v-if="chartType == 'horizontalBar'"
          :data="data"
          :custom-colors="customColors"
        />

        <insights-donut-chart
          v-if="chartType == 'donut'"
          :class="[hideClientStats ? '' : 'mt-4']"
          :small-font="true"
          :inner-text-content="getModuleName"
          :set-inner-text-postion="'50'"
          :show-inner-text="true"
          :data="chartData"
          :donut-height="'162px'"
          :inner-text-header="innerTextHeader"
          :custom-colors="customColors"
        />

        <insights-bar-chart
          v-if="chartType == 'bar'"
          :data="data"
          :bar-height="154"
          :custom-colors="customColors"
        />

        <insights-chart-list
          class="w-100"
          :data="dataWithColors"
          :is-time-spent="isTimeSpent"
          :is-avg-response-time="isAvgResponseTime"
          :is-modal-list="moduleName !== 'assetConnector'"
          :active-label="filterName"
          :is-asset-connector="moduleName === 'assetConnector'"
          @option-filter="getOptionData"
        />
      </div>

      <div
        v-else
        class="w-100 chart-placeholder-wrapper"
      >
        <div class="chart-placeholder"/>
        <div class="text-muted small text-center mt-5 pt-3 mx-3">
          <span>
            Nothing to show yet for this Insight. Please add more <span class="text-lowercase">{{ cardTitle }}</span> information to your {{ moduleName }}. 
          </span>
        </div>
      </div>
      <Teleport to="body">
        <sweet-modal
          ref="viewDetail"
          v-sweet-esc
          modal-theme="right theme-wide"
          :title="moduleName === 'tickets' ? cardFriendlyName : '' "
          :width="modalWidth"
        >
          <template slot="title">
            <div class="d-flex align-items-center h-100">
              <span
                v-if="data.icon"
                class="h5 mr-1 ml-n3 mb-0 mt-1"
                :class="data.icon"
              />
              <h4 class="mb-0">{{ insightCardTitle }}</h4>
              <div
                v-if="data.description"
                class="not-as-small mb-0 ml-4 h4 mt-2"
              >
                {{ data.description }}
              </div>
            </div>
          </template>
          <template slot="box-action">
            <div
              v-if="!isFirewallOrEncryptionMetric && isAssetRiskCenter && isWrite"
              v-tooltip="'Customize Metric'"
              class="sweet-action-close mr-1"
              @click.stop.prevent="openCustomizeMetricModal"
            >
              <i class="nulodgicon-edit base-font-size" />
            </div>
          </template>
          <component
            :is="detailComponent"
            ref="insightDetail"
            :data="data"
            :custom-colors="customColors"
            :is-asset-risk-center="isAssetRiskCenter"
            :is-metric-data-list="isAssetRiskCenter"
            :widget-id="metricInfoId"
            :insight-type="insightType"
            :widget-filters="metricInfoFilters"
            :module-type="getModuleName"
            :is-helpdesk-insights="isHelpdeskInsights"
            :is-avg-response-time="isAvgResponseTime"
            @open-customize-metric-modal="openCustomizeMetricModal"
          />
        </sweet-modal>
      </Teleport>
      <button
        v-if="showViewDetailsButton"
        class="view-all-btn bg-very-light btn btn-outline-light border-right-0 border-left-0 border-bottom-0 position-absolute"
        @click.prevent="openDetailModel"
      >
        <span class="smallest">View All Details</span>
      </button>
      <button
        v-if="showSetupButton"
        class="view-all-btn bg-very-light btn btn-outline-light border-right-0 border-left-0 border-bottom-0 position-absolute z-index-1"
        @click.prevent="openCustomizeDataModal"
      >
        <span class="icon nulodgicon-stats-bars mr-1 ml-0 mb-0 align-middle" />
        <span class="smallest">Setup {{ data.label }}</span>
      </button>
    </div>
    <div
      class="inactive-card drag-placeholder ghost"
      v-html="cardTitle"
    />
    <Teleport to="body">
      <sweet-modal
        v-if="moduleName =='tickets'"
        ref="editCardModal"
        v-sweet-esc
        modal-theme="right theme-dark-header theme-sticky-footer"
        :title="`Edit ${cardFriendlyName}`"
        :width="'55%'"
      >
        <customize-analytics-card
          :data="{}"
          :create-card="false"
          :metric-info="metricInfo"
          @close-modal="closeEditCardModel"
        />
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
import vClickOutside from 'v-click-outside';
import { SweetModal } from 'sweet-modal-vue';
import { mapGetters } from 'vuex';
import ReportsTicketsDetail from 'components/help_tickets/reports/reports_tickets_details.vue';
import CustomizeAnalyticsCard from 'components/help_tickets/reports/customize_analytics_card.vue';
import _sum from 'lodash/sum';
import assetHelper from 'mixins/assets/asset_helper';
import insights from 'mixins/help_ticket_insights';
import InsightsBarChart from './custom_bar_chart.vue';
import InsightsDonutChart from './custom_donut_chart.vue';
import InsightsHorizontalBar from './custom_horizontal_bar.vue';
import CustomizeMetricModal from './customize_metric.vue';
import InsightsChartList from './chart_list.vue';
import InsightAssetDetail from '../assets/insights_asset_details.vue';
import ChartTypeButton from './chart_type_button.vue';
import assetRiskCenter from '../../mixins/asset_risk_center';
import mockRiskCenterData from '../../mixins/mock_risk_center_data';
import MetricFilter from '../help_tickets/reports/metric_filter.vue';

const insightTypeMap = {
  'Agent': 'Assignee(s)',
  'Requester': 'Requester(s)',
  'SLA Avg Response': 'Average',
  'Ticket Avg Response': 'Average',
  'Time Spent': 'Technician(s)',
};
export default {
  directives: {
    clickOutside: vClickOutside.directive,
  },
  components: {
    InsightsBarChart,
    InsightsDonutChart,
    InsightsHorizontalBar,
    InsightsChartList,
    SweetModal,
    InsightAssetDetail,
    ReportsTicketsDetail,
    CustomizeAnalyticsCard,
    ChartTypeButton,
    CustomizeMetricModal,
    MetricFilter,
  },
  mixins: [insights, assetRiskCenter, mockRiskCenterData, assetHelper],
  props: {
    metricInfo: {
      type: Object,
      default: () => {},
    },
    intgDetails: {
      type: Object,
      default: () => {},
    },
    moduleName: {
      type: String,
      default: '',
    },
    cardTitle: {
      type: String,
      default: '',
    },
    isAssetRiskCenter: {
      type: Boolean,
      default: false,
    },
    cardIconClass: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => {},
    },
    detailComponent: {
      type: String,
      default: '',
    },
    defaultChartType: {
      type: String,
      default: 'donut',
    },
    isHelpdeskInsights: {
      type: Boolean,
      default: false,
    },
    showHorizontalBar: {
      type: Boolean,
      default: false,
    },
    customColors: {
      type: Array,
      default: () => [],
    },
    isWrite: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chartType: this.defaultChartType,
      filterName: '',
    };
  },
  computed: {
    ...mapGetters([
      'hasAgentInstalled',
    ]),

    // eslint-disable-next-line vue/no-dupe-keys
    moduleName() {
      return this.$props.moduleName;
    },
    insightCardName() {
      return this.isHelpTicketOrReportModule ? this.cardFriendlyName : this.cardTitle;
    },
    insightCardTitle() {
      return this.isHelpTicketOrReportModule ? this.metricInfo.title : this.cardTitle;
    },
    insightType() {
      if (this.isAssetRiskCenter) return this.data.name;
      return this.insightCardTitle;
    },
    cardFriendlyName() {
      return this.metricInfo.friendlyName || "";
    },
    cardDescription() {
      return this.metricInfo.description || "";
    },
    chartHasData() {
      let hasSomeNonzeroElements;
      const hasElements = this.data && this.data.data && this.data.data.length > 0;
      if (hasElements) {
        hasSomeNonzeroElements = this.data.data.some(val => val !== 0);
      }
      return hasElements && hasSomeNonzeroElements;
    },
    isHelpTicketOrReportModule() {
      return this.moduleName === 'tickets' || this.moduleName === 'SavedReport';
    },
    isFirewallOrEncryptionMetric() {
      return ['Firewall', 'Data Encryption'].includes(this.data.name);
    },
    isBlankStateMetric() {
      return !this.data.enabled && this.moduleName === 'risk center' && !this.isFirewallOrEncryptionMetric;
    },
    shouldDisplayChartData() {
      return this.chartHasData && !this.isBlankStateMetric;
    },
    shouldDisplaySampleData() {
      return this.isBlankStateMetric;
    },
    displaySampleDataWithAgent() {
      return this.shouldDisplaySampleData && this.hasAgentInstalled;
    },
    displaySampleDataWithoutAgent() {
      return this.shouldDisplaySampleData && !this.hasAgentInstalled;
    },
    displayData() {
      return this.shouldDisplayChartData || this.shouldDisplaySampleData;
    },
    showSetupButton() {
      return this.isBlankStateMetric && this.hasAgentInstalled && this.moduleName !=='tickets' && this.moduleName !== 'SavedReport';
    },
    showViewDetailsButton() {
      return this.displayData && this.moduleName !=='tickets' && this.moduleName !== 'SavedReport' && this.moduleName !== 'assetConnector';
    },
    innerTextHeader() {
      if (['SLA Avg Response', 'Ticket Avg Response'].includes(this.insightCardTitle)) {
        return this.formatTime(Math.ceil(this.data.totalTime[0]/this.data.count[0]));
      } else if (this.insightCardTitle === 'Time Spent') {
        return this.data.totalUsers[0];
      }
      if (this.insightCardTitle === 'Agent' || this.insightCardTitle === 'Requester') {
        return this.data.labels.length;
      }
      return _sum(this.chartData.data.flat());
    },
    getModuleName() {
      if (this.moduleName === 'assetConnector') {
        return 'Discovered Assets';
      }
      if (this.cardTitle === "Operating System") {
        return "OS";
      }
      else if (this.cardTitle === "Applications") {
        return this.cardTitle;
      }
      return (this.moduleName === 'assets' || this.moduleName === 'risk center') ? 'Assets' : insightTypeMap[this.insightCardTitle] || 'Tickets';
    },
    isCustomWidgetCard() {
      return this.data?.isCustom;
    },
    isTimeSpent() {
      return this.insightCardTitle === 'Time Spent';
    },
    isAvgResponseTime() {
      return ['SLA Avg Response', 'Ticket Avg Response'].includes(this.insightCardTitle);
    },
    modalWidth() {
      return (this.moduleName === 'assets' || this.moduleName === 'risk center') ? '90%' : '80%';
    },
    chartData() {
      const mockData = mockRiskCenterData.data().fullData;
      return this.shouldDisplaySampleData ? mockData[this.cardTitle] : this.data;
    },
    dataWithColors() {
      return {
        ...this.chartData,
        colors: this.customColors,
      };
    },
    truncatedSelectedOptions() {
      const numToShow = 3;
      const mockData = mockRiskCenterData.data().fullData;
      const options = this.isBlankStateMetric ? mockData[this.cardTitle].filterItems : this.data?.selectedOptions;
      if (options && options.length > 0) {
        let truncatedItems = options.slice(0, numToShow).join(", ");
        if (options.length > numToShow) {
          truncatedItems += ` + ${options.length - numToShow} more`;
        }
        return truncatedItems;
      }

      return null;
    },
    metricInfoId() {
      return this.isHelpTicketOrReportModule ? this.metricInfo.id : null;
    },
    metricInfoFilters() {
      return this.isHelpTicketOrReportModule ? this.metricInfo.filters : [];
    },
  },
  mounted() {
    const { group, option } = this.$route.query;
    if (group && group === this.data.name) {
      this.openGroupDetailModel(option);
    };
  },
  methods: {
    openGroupDetailModel( option) {
      this.$refs.viewDetail.open();
      this.$refs.insightDetail.setFilterName(option);
    },
    openDetailModel() {
      this.$refs.viewDetail.open();
      this.$refs.insightDetail.setFilter();
    },
    openEditCardModel() {
      this.$refs.editCardModal.open();
    },
    closeEditCardModel() {
      this.$refs.editCardModal.close();
    },
    setChartType(activeType) {
      this.chartType = activeType;
      this.$emit('set-chart-type', activeType);
    },
    getOptionData(option) {
      this.$refs.viewDetail.open();
      const widgetId = (this.moduleName === 'tickets' || this.moduleName === 'SavedReport') ? this.metricInfo.id : null;
      this.$refs.insightDetail.setFilterName(option, widgetId);
    },
    removeWidget() {
      return this.moduleName === 'tickets' ? this.$emit('widget-removed') : this.$emit('widget-removed', this.cardTitle);;
    },
    openCustomizeMetricModal() {
      this.$refs.viewDetail.close();
      this.openCustomizeDataModal();
    },
    isDataEmpty(dataArray) {
      return dataArray.every(item => item === 0);
    },
  },
};
</script>

<style lang="scss" scoped>
  $box-padding-spacing: 1.125rem;

  .dot-wrapper {
    font-size: 1.125rem;
    height: 2.5rem;
    line-height: 2.5rem;
    width: 2.5rem;
  }

  .chart-placeholder-wrapper {
    // Setting height to common card inner block min height for now. TODO: Debugging the more ideal card flex heights accounting for more general data/headers/placeholders
    min-height: 346px;
  }

  .chart-placeholder { 
    background: radial-gradient(circle, transparent 55%, $light 55%);
    border-radius: 100%;
    height: 170px;
    margin: .75rem auto 0;
    position: relative;
    width: 170px;
  }

  .view-all-btn {
    bottom: 0;
    left: 0;
    right: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
  }

  .icon-button {
    cursor: pointer;
    border: none;
    border-radius: 50%;
    background-color: transparent;
    height: 2.125rem;
    padding-left: 6px;
    padding-left: 6px;
    width: 2.125rem;

    &:focus {
      outline: none;
    }

    &:hover {
      background-color: $light;
    }
  }

  .nulodgicon-android-close {
    font-size: 22px;

    &:before {
      color: $dark;
    }
  }

  .drag-overlay {
    background-color: rgba(33, 37, 41, 0.1);
    border-radius: 0.50rem 0 0 0.50rem;
    color: $themed-base;
    content: "";
    cursor: move;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    visibility: hidden;
    width: 1.25rem;
    z-index: 2;

    &:hover {
      background-color: rgba(33, 37, 41, 0.3);
    }
  }

  .widget-card:hover {
    .drag-overlay {
      opacity: 1;
      visibility: visible;
    }
  }

  .handle {
    cursor: move;
  }

  .genuicon-expand, .genuicon-arrows  {
    &:before {
      color: $dark;
    }
  }
  .inactive-card.ghost {
    background: 2.25rem center no-repeat;
    background-color: color.adjust($maastricht-blue, $alpha: -0.5);
    background-size: 1.25rem auto;
    border-radius: $border-radius;
    color: white;
    width: 50% !important;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    padding: 0.5rem;
    padding-left: 2rem;
    position: relative;
    cursor: move;
    &:after {
      content: "\22EE\22EE";
      font-size: 1.25rem;
      left: 0.5rem;
      letter-spacing: -3px;
      opacity: 0.5;
      position: absolute;
      top: 50%;
      transform: translateY(-55%);
    }
    &.drag-placeholder {
      position: fixed;
      z-index: 99999;
      left: -100vw;
      top: -100vw;
    }
  }
  .ghost:not(.drag-placeholder) {
    background-color: color.adjust($primary, $alpha: -0.9) !important;
    background-image: none !important;
    border: 2px dashed $gray-500;
    border-radius: calc(#{$border-radius} * 2);
    margin-top: -2px;
    opacity: 1;
    > *, &:after, &:before {
      opacity: 0 !important;
      visibility: hidden;
      transition: none;
    }
  }
  .skeleton-bar-box--first {
    min-height: 9.375rem;
  }
  .skeleton-bar-box--second {
    min-height: 7.5rem;
  }
  .skeleton-bar-box--third {
    min-height: 5rem;
  }

  .blank-state-alert {
    background: var(--themed-light);
    border-left: 4px solid $color-caution;
    display: block;
  }

  .z-index-1 {
    z-index: 1;
  }

  .z-index-100 {
    z-index: 100;
  }
  .sample-metric-label-width {
    width: fit-content;
  }

  .card-description-height {
    min-height: 1.1875rem;
  }

  .card-options-height {
    min-height: 1.5625rem;
  }

  .alert-link {
    color: blue !important;
  }
</style>
