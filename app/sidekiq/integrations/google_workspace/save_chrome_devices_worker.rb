class Integrations::GoogleWorkspace::SaveChromeDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include GoogleWorkspaceCommonMethods
  include IntegrationAlertsHelper

  require 'google/apis/admin_directory_v1'

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, discovered_asset_count = 0, managed_asset_count = 0)
    @starting_time = Time.current
    @config = Integrations::GoogleWorkspace::Config.find_by(id: config_id)
    return if @config.nil?

    @import_target = @config.import_type
    @new_discovered_assets_count = discovered_asset_count
    @new_managed_assets_count = managed_asset_count
    @first_time_flag = first_time_flag
    pusher(@config, true, 0.6)
    refresh_access_token
    save_chrome_devices
    pusher(@config, true, 0.9)
    company_integration.assign_attributes(sync_status: :successful,
                                          status: true,
                                          last_synced_at: DateTime.now,
                                          active: true,
                                          error_message: nil,
                                          notified: nil,
                                          failure_count: 0,
                                          alert_info: IntegrationAlertsHelper::SUCCESS_STATE)
    company_integration.save!
    pusher(@config, true, 1, {
      new_google_workspace_discovered_assets: @new_discovered_assets_count,
      new_google_workspace_managed_assets: @new_managed_assets_count
    })
    intg_duration_analysis(@company.id, @first_time_flag)
  rescue Exception => e
    company_integration.assign_attributes(sync_status: :failed,
                                          status: false,
                                          last_synced_at: DateTime.now,
                                          error_message: e.message,
                                          failure_count: company_integration.failure_count + 1,
                                          alert_info: IntegrationAlertsHelper::ERROR_STATE)
    company_integration.save!
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'google_workspace_integration').to_json)
    pusher(@config, false, 0)
  end

  def company
    @company ||= @config.company
  end

  def company_integration
    @company_integration ||= @config.company_integration
  end

  def chromeos_devices
    next_page_token = nil
    all_devices = []
    page_count = 0
    max_retries = 3

    loop do
      page_count += 1
      retries = 0

      begin
        result = client.fetch_all_chrome_os_devices(next_page_token)

        # Validate result structure
        if result.nil?
          Rails.logger.warn("Google Workspace API returned nil result for page #{page_count}")
          break
        end

        # Log pagination progress
        devices_in_page = result.chromeosdevices&.count || 0
        Rails.logger.info("Google Workspace Chrome devices - Page #{page_count}: #{devices_in_page} devices")

        # Add devices from current page
        if result.chromeosdevices.present?
          all_devices.concat(result.chromeosdevices)
        else
          Rails.logger.warn("No Chrome devices found in page #{page_count}")
        end

        next_page_token = result.next_page_token
        break unless next_page_token.present?

      rescue => e
        retries += 1
        Rails.logger.error("Error fetching Chrome devices page #{page_count}, attempt #{retries}: #{e.message}")

        if retries <= max_retries
          sleep(2 ** retries) # Exponential backoff
          retry
        else
          Rails.logger.error("Failed to fetch Chrome devices page #{page_count} after #{max_retries} retries")
          # Log the error but continue with what we have
          LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, "chromeos_pagination_failure_page_#{page_count}", 'google_workspace_integration').to_json)
          break
        end
      end
    end

    Rails.logger.info("Google Workspace Chrome devices sync completed - Total devices: #{all_devices.count}, Pages processed: #{page_count}")
    return all_devices
  end

  def save_chrome_devices
    total_devices = chromeos_devices.count
    processed_count = 0
    skipped_count = 0
    created_count = 0

    Rails.logger.info("Starting to process #{total_devices} Chrome devices from Google Workspace")

    chromeos_devices.each_with_index do |device, index|
      @discovered_asset = nil
      @managed_asset = nil
      mac_addresses = []
      display_name = device.model.presence || "ChromeOS #{device.os_version}"
      serial_number = device.serial_number
      model = device.model
      operating_system = device.try(:chrome_os_type) || "ChromeOS #{device.os_version}"
      os_name = "ChromeOS"
      os_version = device.os_version
      mac_address = device.mac_address.try(:upcase)

      # Enhanced MAC address validation with logging
      if mac_address.present? && mac_address_valid?(mac_address)
        mac_addresses << mac_address
      elsif mac_address.present?
        Rails.logger.warn("Invalid MAC address for Chrome device #{serial_number}: #{mac_address}")
      end

      source = 'google_workspace'
      last_check_in_time = device.last_sync

      # Log device processing for debugging
      if (index + 1) % 100 == 0 || index == 0
        Rails.logger.info("Processing Chrome device #{index + 1}/#{total_devices} - Serial: #{serial_number}")
      end

      @discovered_asset = find_discovered_asset(company, false, serial_number, mac_addresses, display_name, nil, nil, false)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: company.id)

      mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      ip_address_data = @discovered_asset.ip_address
      serial_data = serial_number.present? ? serial_number : @discovered_asset.machine_serial_no

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        machine_serial_no: serial_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        asset_type: get_asset_type("Laptop"),
        os: operating_system,
        os_name: os_name,
        os_version: os_version,
        model: model,
        source: source,
        discovered_asset_type: :device,
        lower_precedence: @is_lower_precedence,
        last_check_in_time: last_check_in_time
      )

      @discovered_asset.asset_softwares.find_or_initialize_by(
        software_type: 'Operating System',
        name: os_name || ""
      )

      managed_asset = @discovered_asset.managed_asset || find_managed_asset(company, serial_data, mac_addresses_data, display_name_data, nil)

      if managed_asset.present?
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset_id = managed_asset.id
      end

      begin
        assign_discovered_assets_hardware_details(device)
        is_new = @discovered_asset.new_record?

        if @discovered_asset.save
          @new_discovered_assets_count += 1 if is_new
          created_count += 1 if is_new
          processed_count += 1

          asset_source_data = {
            display_name: display_name,
            machine_serial_no: serial_data,
            mac_addresses: mac_addresses_data,
            ip_address: ip_address_data,
            os_name: os_name,
            source: source
          }

          add_source(asset_source_data)
          @discovered_asset.reload

          if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
            BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
            @new_managed_assets_count += 1
          end
        else
          Rails.logger.error("Failed to save Chrome device #{serial_number}: #{@discovered_asset.errors.full_messages.join(', ')}")
          skipped_count += 1
        end
      rescue => e
        Rails.logger.error("Error processing Chrome device #{serial_number}: #{e.message}")
        skipped_count += 1
      end
    end

    # Log final summary
    Rails.logger.info("Chrome devices sync completed - Total: #{total_devices}, Processed: #{processed_count}, Created: #{created_count}, Skipped: #{skipped_count}")

    # Alert if significant number of devices were skipped
    if skipped_count > (total_devices * 0.05) # More than 5% skipped
      Rails.logger.warn("High skip rate detected: #{skipped_count}/#{total_devices} devices skipped (#{(skipped_count.to_f/total_devices*100).round(2)}%)")
    end
  end

  def assign_discovered_assets_hardware_details(device)
    hardware_detail = @discovered_asset.discovered_assets_hardware_detail ||= DiscoveredAssetsHardwareDetail.new
    hardware_detail.assign_attributes(
      memory: memory_in_gb(device.system_ram_total)
    )
  end

  def memory_in_gb(bytes)
    gb = bytes.to_f / (1024 * 1024 * 1024)
    gb.round
  end

  def client
    Integrations::GoogleWorkspace::FetchData.new(company.id)
  end
end
