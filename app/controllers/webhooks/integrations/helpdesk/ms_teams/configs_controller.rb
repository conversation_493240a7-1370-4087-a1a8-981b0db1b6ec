class Webhooks::Integrations::Helpdesk::MsTeams::ConfigsController < Webhooks::Integrations::Helpdesk::BaseController
  before_action :if_already_linked?, only: [:create]
  before_action :authenticate_user, only: [:create]
  skip_before_action :authenticate_user, only: [:update]
  skip_before_action :set_resources, only: [:update]
  skip_before_action :set_current_user, only: [:update]

  def create
    ms_teams_config = scoped_company.ms_teams_configs.find_or_initialize_by(team_id: team_id, channel_id: channel_id, workspace_id: scoped_company.default_workspace.id, tenant_id: tenant_id)
    ms_teams_config.assign_attributes(team_name: team_name, channel_name: channel_name, active: true)

    if ms_teams_config.save
      res = { subdomain: company_subdomain }
      log_event(res, 'create', :success, { company_id: scoped_company.id })
      render json: res, status: :ok
    else
      res = { message: 'An error occured while linking your channel.' }
      log_event(res, 'create', :error, { company_id: scoped_company.id })
      render json: res, status: :bad_request
    end
  rescue => e
    res = { message: 'An error occured while linking your channel.' }
    log_event(res, 'create', :error, { company_id: scoped_company&.id }, e)
    render json: res, status: :unprocessable_entity
  end

  def show
    res = { subdomain: scoped_company.subdomain }
    log_event(res, 'show', :success)
    render json: res, status: :ok
  end

  def destroy
    ms_teams_config = ::Integrations::MsTeams::Config.find_by(channel_id: channel_id, team_name: team_name)

    if ms_teams_config&.destroy
      res = { message: 'This channel is successfully unlinked.' }
      log_event(res, 'destroy', :success)
      render json: res, status: :ok
    else
      res = { message: 'An error occured while unlinking your channel.' }
      log_event(res, 'destroy', :error)
      render json: res, status: :unprocessable_entity
    end
  end

  def update
    config = ::Integrations::MsTeams::Config.find_by_id(params['id'].to_i)
    if params['is_bot_installed']
      data = if config.active && config.update(active: false)
               { is_app_deacitvated: true }
             elsif !config.active && config.update(active: true)
               { is_app_activated: true }
             else
               { message: 'Sorry, something went wrong. Please try again.' }
             end
      Pusher.trigger("ms_teams=#{config.company.guid}", 'ms_teams_configs', data)
    else
      Pusher.trigger("ms_teams=#{config.company.guid}", 'ms_teams_configs', { message: "It seems like Genuity app is not installed in your Microsoft Teams or connected channel doesn't exist anymore. You can delete this connector instead." })
    end
  end

  private

  def if_already_linked?
    is_already_linked = @config.present? && @config.company.subdomain.eql?(company_subdomain)
    if is_already_linked
      res = { message: 'Sorry, this channel is already linked with another company.' }
      log_event(res, 'if_already_linked?', :success)
      render json: res, status: :bad_request
    end
  end

  def authenticate_user
    if scoped_company_user.blank?
      res = { message: "Sorry, you are not a member of #{scoped_company.subdomain}. Please contact your admin." }
      log_event(res, 'authenticate_user', :success)
      render json: res, status: :unauthorized
    end
  end

  def integration
    'MsTeams'
  end
end
