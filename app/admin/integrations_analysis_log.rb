ActiveAdmin.register Logs::ApiEvent, :as => 'Integrations Duration' do
  menu parent: 'Logs', label: 'Integration Benchmark Analysis', priority: 7
  breadcrumb do
    ['admin', 'logs']
  end

  controller do
    def scoped_collection
      Logs::ApiEvent.where(api_type: 'integration_duration')
    end
  end

  config.sort_order = 'response_dsec'

  filter :duration, label: 'Response Time', as: :numeric, filters: [:equals, :greater_than, :less_than]
  filter :company, label: 'Company', as: :select, collection: Company.order('name ASC').map { |company| [company.name, company.id] }
  filter :class_name, label: 'Class Name', as: :select

  scope :all do |events|
    events.all
  end

  scope :less_than_5_min do |events|
    events.where("response -> 'duration' < '5'")
  end

  scope :greater_than_5_min do |events|
    events.where("response -> 'duration' > '5'")
  end

  scope :sort_by_most_time_consuming do |events|
    events.order(Arel.sql("response -> 'duration'").desc)
  end

  actions :all, except: [:new, :edit]

  index do
    selectable_column
    id_column
    column ("Company") { |event| "#{event.company&.name}" }
    column ("Status") { |event| "#{event.status}" }
    column ("Integration") { |event| "#{event.class_name}" }
    column ("Duration") { |event| "#{event.response["duration"]} #{'minute'.pluralize(event.response["duration"])}" }
    column ("Time") { |event| "#{event.created_at.to_written_time}" }
    column ("First Time") { |event| event.first_time ? 'YES' : 'NO'}
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
