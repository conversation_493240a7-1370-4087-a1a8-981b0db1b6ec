module AutomatedTasks
  module Actions
    class SendMsTeamsMessage < HelpTicketAction
      include AutomatedTasks::Concerns::Activity
      include MsTeamsOptions
      include Utilities::Domains

      def call
        return if root.class.name == 'HelpTicketComment' && root.private_flag
        send_message
        create_recepients_activity("Ms team message", recipients_name) if object.class.name == 'HelpTicket'
      end

      def ticket
        object
      end

      def custom_form
        ticket.custom_form
      end

      def send_message
        ms_teams_service.call('automated_message', { message: message, object_id: object.id, help_ticket: ticket_json })
      end

      def recipients_name
        [ms_teams_config.channel_name+" #{ms_teams_config.team_name == 'Group Chat' ? 'Group Chat' : 'channel'}"]
      end

      def params
        @params = ActionController::Parameters.new(archived: '')
      end

      def help_ticket_data
        {}
      end

      def scoped_company
        object.company
      end

      def ms_teams_service
        @ms_teams_service ||= Integrations::MsTeams::Client.new(ms_teams_config)
      end

      def ms_teams_config_id
        json_value['ms_teams_config']['id']
      end

      def ms_teams_config
        Integrations::MsTeams::Config.find(ms_teams_config_id)
      end

      def json_value
        @json_data ||= JSON.parse(action.value)
      end

      def message
        @message ||= begin
          body = StringInterpolate.new(root).call(json_value['message']).gsub('<div><!--block-->', '').gsub('</div>', '')
          HtmlToMarkdownConverter.new(parse_type: 'teams', content: body).call
        end
      end
    end
  end
end
