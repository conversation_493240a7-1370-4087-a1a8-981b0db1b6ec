class Integrations::MsTeams::GraphClient
  include Utilities::Domains
  BASE_URL = "https://graph.microsoft.com/v1.0".freeze
  attr_reader :team_id , :ms_teams_config

  def initialize(ms_teams_config:, team_id: nil)
    @team_id = team_id
    @ms_teams_config = ms_teams_config
    @base_url = BASE_URL
    @access_token = fetch_access_token
  end

  # post requests 
  def send_chat_message(message, channel_id)
    response = post_request("/teams/#{@team_id}/channels/#{channel_id}/messages", {
      body: {
        content: message
      }
    })
    handle_response(response)
  end

  def base_url
    if Rails.env.development?
      # Replace it with your Ngrock path for development
      'https://846792f1ec77.ngrok-free.app/'
    else
      build_secure_url
    end
  end

  def create_channel_subscription(channel_id)
    # 3 days max as written here: https://learn.microsoft.com/en-us/graph/change-notifications-overview#notification-endpoint-validation
    # Replace the notificationUrls with ngrok path for development
    expiration_time = (Time.now + 3.days).utc.iso8601
    response = post_request("/subscriptions", {
      changeType: 'created',
      notificationUrl: "#{base_url}/webhooks/integrations/helpdesk/ms_teams/notifications",
      resource: "teams/#{@team_id}/channels/#{channel_id}/messages",
      expirationDateTime: expiration_time,
      clientState: 'secretClientValue',
      lifecycleNotificationUrl:  "#{base_url}/webhooks/integrations/helpdesk/ms_teams/notifications"
    })
    handle_response(response)
  end

  def renew_subscription(subscription_id)
    expiration_time = (Time.now + 3.days).utc.iso8601
    response = patch_request("/subscriptions/#{subscription_id}", {
      expirationDateTime: expiration_time,
    })
    handle_response(response)
  end

  def create_channel(channel_name, members)
    response = post_request("/teams/#{@team_id}/channels", {
      displayName: channel_name,
      membershipType: "shared",
      members: members
    })
    handle_response(response)
  end

  def add_member_to_channel(user_id, channel_id)
    response = post_request("/teams/#{@team_id}/channels/#{channel_id}/members", {
      "@odata.type": "#microsoft.graph.aadUserConversationMember",
      "roles": [],
      "<EMAIL>": "#{@base_url}/users/#{user_id}"
    })
    handle_response(response)
  end

  # Get Requests

  def list_members
    response = get_request("/teams/#{@team_id}/members")
    handle_response(response)
  end

  def get_user_by_email(email)
    response = get_request("/users?$filter=mail eq '#{email}'")
    handle_response(response)
  end
  

  def fetch_channel_info(channel_id)
    response = get_request("/teams/#{@team_id}/channels/#{channel_id}?$select=displayName,id,webUrl")
    handle_response(response)
  end

  def get_chat_message(channel_id, message_id)
    response = get_request("/teams/#{@team_id}/channels/#{channel_id}/messages/#{message_id}")
    handle_response(response)
  end
  
  def fetch_request_status(location)
    response = get_request(location)
    handle_response(response)
  end

  def fetch_user_email(user_id)
    response = get_request("/users/#{user_id}")
    handle_response(response)
  end
  private

  def fetch_access_token
    token_response = HTTParty.post(
      oauth_url,
      headers: { 'Content-Type' => 'application/x-www-form-urlencoded' },
      body: oauth_params
    )
    raise StandardError, token_response unless token_response.success?

    token_response['access_token']
  end

  def post_request(endpoint, body)
    HTTParty.post(
      "#{@base_url}#{endpoint}",
      body: body.to_json,
      headers: headers
    )
  end

  def patch_request(endpoint, body)
    HTTParty.patch(
      "#{@base_url}#{endpoint}",
      body: body.to_json,
      headers: headers
    )
  end

  def get_request(endpoint)
    HTTParty.get(
      "#{@base_url}#{endpoint}",
      headers: headers
    )
  end

  def handle_response(response)
    # Extracts the caller label
    calling_method = caller_locations(1,1)[0].label
    case response.code
    when 200..299
      log_event(response.parsed_response, calling_method, 'success', { config: ms_teams_config })
      response
    else
      log_event(response.parsed_response, calling_method, 'error', { config: ms_teams_config })
      raise StandardError, response
    end
  end
  
  def headers
    {
      'Authorization' => "Bearer #{@access_token}",
      'Content-Type' => 'application/json'
    }
  end

  def oauth_url
    "https://login.microsoftonline.com/#{ms_teams_config.tenant_id}/oauth2/v2.0/token"
  end

  def oauth_params
    {
      client_id: Rails.application.credentials[:teams][:bot_app_id],
      client_secret: Rails.application.credentials[:teams][:client_secret],
      scope: 'https://graph.microsoft.com/.default',
      grant_type: 'client_credentials'
    }
  end

  def log_event(response, api_type, status, fur_detail={})
    api_response = { body: response }.to_s if response
    log_params = {
      api_type: api_type,
      class_name: self.class.name,
      company_id: ms_teams_config.company_id,
      status: status,
      detail: fur_detail,
      activity: :action,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
    }
    Logs::ApiEvent.create(log_params)
  end
end
