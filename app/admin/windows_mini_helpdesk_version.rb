ActiveAdmin.register WindowsMiniHelpdeskVersion do
  menu parent: 'Mini Helpdesk', priority: 2, label: 'Windows Mini Helpdesk'
  breadcrumb do
    ['admin', 'mini helpdesk']
  end

  filter :name
  filter :version
  permit_params :name, :version, :description, :installer_link, :old_blockmap, :new_blockmap, :latest_yml

  form do |f|
    f.inputs 'Details' do
      f.input :name
      f.input :version
      f.input :description
      f.input :installer_link
      f.input :old_blockmap
      f.input :new_blockmap
      f.input :latest_yml
    end
    f.actions
  end

  index do
    id_column
    column :version
    column :name
    column :description
    column "actions" do |resource|
      links = ''.html_safe
      links += link_to I18n.t('active_admin.view'), resource_path(resource), :class => "member_link edit_link"
      links += link_to I18n.t('active_admin.delete'), resource_path(resource), :method => :delete, :confirm => I18n.t('active_admin.delete_confirmation'), :class => "member_link delete_link"
      links
    end
  end

  action_item :download, only: :index do
    @version = WindowsMiniHelpdeskVersion.order("created_at").last
    if @version.present?
      link_to 'Download Latest Installer', @version.installer_link.url
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
