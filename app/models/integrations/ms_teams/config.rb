class Integrations::MsTeams::Config < ApplicationRecord
  include Ms<PERSON><PERSON>msHelper
  self.table_name = 'ms_teams_configs'

  include CompanyCache
  attr_accessor :skip_automated_tasks_creation

  has_many :generated_configs, class_name: "Integrations::MsTeams::Config", foreign_key: "authorized_config_id", dependent: :destroy

  belongs_to :company
  belongs_to :workspace

  validates_presence_of :channel_id, :company_id
  validates_presence_of :team_id, if: :is_not_group_chat?
  validates_uniqueness_of :channel_id, scope: [:team_id], if: :is_not_group_chat?

  after_update :destroy_associated_automated_tasks, if: Proc.new { |config| config.saved_change_to_active? && !config.active && config.authorized_config_id.nil? }
  after_destroy :destroy_associated_automated_tasks, if: Proc.new { |config| config.authorized_config_id.nil? }
  after_commit :unlink_ms_teams_channel, on: :destroy
  after_update :create_default_automated_tasks, if: Proc.new { |config| config.saved_change_to_active? && config.active && config.authorized_config_id.nil? }
  after_create :create_default_automated_tasks, unless: :skip_automated_tasks_creation
  
  def destroy_associated_automated_tasks
    # TODO: MS teams: remove env check 
    workspace = ms_teams_feature_access_pending?(company.id) ? company.default_workspace : company.workspaces.find_by_id(self.workspace_id)
    teams_tasks = workspace.automated_tasks
                           .joins(task_actions: :action_type)
                           .where("automated_tasks_action_types.action_class = 'SendMsTeamsMessage'")
                           .uniq
    
    teams_tasks.each do |task|
      actions = ms_teams_actions(task)
      if task.task_actions.count > 1
        actions.each do |action|
          value_hash = JSON.parse(action.value)
          action.destroy if value_hash['ms_teams_config']['id'].to_i == id  
        end
      elsif actions.present?
        value_hash = JSON.parse(actions.first.value)
        task.destroy if value_hash['ms_teams_config']['id'].to_i == id
      end
      task.destroy if task.task_actions.count == 0 && !task.destroyed?
    end
  end

  def create_default_automated_tasks
    actions = ['TicketCreated', 'CommentAdded', 'StatusUpdated']
    actions.each do |action|
      "AutomatedTasks::Generators::HelpdeskConnectors::#{action}".constantize.new(nil, nil, self).build_task
    end
  end

  def unlink_ms_teams_channel
    @ms_teams_client = Integrations::MsTeams::Client.new(self)
    @ms_teams_client.call('textmessage', { message: 'This channel has been unlinked from Genuity' })
  end

  def ms_teams_actions(task)
    task.task_actions.includes(:action_type).where(action_type: { action_class:'SendMsTeamsMessage'})
  end

  def is_not_group_chat?
    team_name != 'Group Chat'
  end
end
