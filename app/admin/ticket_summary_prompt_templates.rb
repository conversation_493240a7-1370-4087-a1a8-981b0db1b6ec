ActiveAdmin.register PromptsTemplate, as: 'ticket_summary_prompt_templates' do
  menu parent: 'Manage Services', priority: 8
  breadcrumb do
    ['admin', 'manage services']
  end

  actions :all
  config.batch_actions = false

  index do
    column :name
    column :created_at
    column :updated_at
    actions defaults: false do |prompt|
      item 'View', admin_ticket_summary_prompt_template_path(prompt), class: 'member_link'
      item 'Edit', edit_admin_ticket_summary_prompt_template_path(prompt), class: 'member_link'
      item 'Delete', admin_ticket_summary_prompt_template_path(prompt),
           method: :delete,
           data: { confirm: 'Are you sure you want to delete this prompt template?' },
           class: 'member_link delete_link'
    end
  end

  show do
    attributes_table do
      row :name
      row :template_text
      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs 'Prompt Template Details' do
      f.input :name
      f.input :template_text, input_html: { class: 'handle-textarea-height' }
    end
    f.actions
  end

  controller do
    def destroy
      template = PromptsTemplate.find(params[:id])
      if template.destroy
        redirect_to admin_ticket_summary_prompt_templates_path, notice: "Prompt Template was successfully deleted."
      else
        redirect_to admin_ticket_summary_prompt_template_path(template), alert: template.errors.full_messages.to_sentence
      end
    end

    def permitted_params
      params.permit(prompts_template: [
        :name,
        :is_active,
        :template_text
      ])
    end
  end
end
