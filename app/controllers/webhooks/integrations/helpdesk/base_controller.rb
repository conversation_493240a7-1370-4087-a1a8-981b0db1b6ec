class Webhooks::Integrations::Helpdesk::BaseController < ApplicationController
  include MultiCompany::HelpTicketScoping

  before_action :set_resources
  before_action :set_current_user

  private

  def set_resources
    if company_subdomain
      @scoped_company ||= Company.find_by_cache(subdomain: company_subdomain)
      
      if @scoped_company.blank?
        return render json: { message: "Company with subdomain '#{company_subdomain}' not found." }, status: :not_found
      end
    end

    @config = "::Integrations::#{integration}::Config".constantize.find_by(team_id: team_id, channel_id: channel_id)
    @scoped_company ||= @config&.company

    if @config.blank? && company_subdomain.blank?
      res = { message: 'Please link this channel with Genuity to perform this action. You can use _@Genuity link_ command to link this channel to Genuity.' }
      log_event(res, 'set_resources', :success)
      render json: res, status: :not_found
    elsif @config.present? && !@config.active?
      res = { message: 'This channel is deactivated from Genuity web app.', is_channel_deactivated: true }
      log_event(res, 'set_resources', :success)
      render json: res, status: :not_found
    end

    update_missing_tenant_id if should_update_tenant_id?
  end

  def update_missing_tenant_id
    @config.update_column(:tenant_id, tenant_id)
  end

  def scoped_workspace
    ticket.workspace
  end

  def authorize_user
    if expanded_privilege.blank?
      res = { message: 'Sorry, you\'re not authorized to perform this action.' }
      log_event(res, 'authorize_user', :success)
      render json: res, status: :unauthorized
    end
  end

  def owned_by
    [ ticket_creator&.id ].compact
  end

  def company_subdomain
    params[:subdomain]
  end

  def channel_id
    return params[:channel] if params[:channel].blank?
    params[:channel].is_a?(String) ? JSON.parse(params[:channel])['id'] : params[:channel][:id]
  end

  def channel_name
    return params[:channel] if params[:channel].blank?
    chnl_name = params[:channel].is_a?(String) ? JSON.parse(params[:channel])['name'] : params[:channel][:name]
    chnl_name || (is_a_group_chat? ? chat_name : 'General')
  end

  def is_a_group_chat?
    return false if params[:channel].blank?
    chat_type = params[:channel].is_a?(String) ? JSON.parse(params[:channel])['channel_type'] : params[:channel][:channel_type]
    chat_type == 'groupChat'
  end

  def chat_name
    return false if !member_names
    return member_names.join(' - ') if member_names.count <= 2
    "#{member_names.first(2).join(' - ')} + #{member_names.count - 2} more"
  end

  def member_names
    return false if params[:channel].blank?
    params[:channel].is_a?(String) ? JSON.parse(params[:channel])['member_names'] : params[:channel][:member_names]
  end

  def team_id
    return params[:team] if params[:team].blank?
    params[:team].is_a?(String) ? JSON.parse(params[:team])['id'] : params[:team][:id]
  end

  def team_name
    return 'Group Chat' if is_a_group_chat?
    return params[:team] if params[:team].blank?
    params[:team].is_a?(String) ? JSON.parse(params[:team])['name'] : params[:team][:name]
  end

  def current_user_email
    params[:currentUser] || params[:current_user]
  end

  def set_current_user
    @current_user = User.where("lower(email) = lower(?)", current_user_email).first
  end

  def tenant_id
    @tenant_id ||= begin
      team_data = params[:team]
      channel_data = params[:channel]
      
      tenant_id_from_data(team_data) || tenant_id_from_data(channel_data)
    end
  end

  def tenant_id_from_data(data)
    return JSON.parse(data)['tenantId'].presence if data.is_a?(String)
  
    data&.dig(:tenant_id)
  end

  def should_update_tenant_id?
    @config.present? && @config.tenant_id.blank? && tenant_id.present?
  end

  def guest
    @guest ||= Guest.find_by("lower(email) = lower(?) AND company_id = ?", current_user_email, scoped_company.id)
  end

  def log_event(response, api_type, status, fur_detail={}, excep = nil)
    webhook_response = { response: response }.to_s if response.present?
    log_params = {
      api_type: api_type,
      class_name: self.class.name,
      company_id: @config&.company_id || fur_detail[:company_id],
      status: status,
      detail: { params: params, path: path_info }.merge(fur_detail),
      activity: :action,
      response: webhook_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil,
    }
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end

  def path_info
    request.path
  end
end
