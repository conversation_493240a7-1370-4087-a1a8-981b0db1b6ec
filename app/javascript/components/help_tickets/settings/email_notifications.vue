<template>
  <div class="clearfix row mx-1">
    <div class="col mt-5">
      <workspace-settings-banner/>
      <h4 class="font-weight-normal mb-3 h5--responsive">
        Email Settings
      </h4>

      <inbound-emails />

      <div class="box box--block box--natural-height p-5 mb-5">
        <div class="box-inner w-100">
          <h5 class="font-weight-normal mb-1 h5--responsive">
            Notify me when&hellip;
          </h5>
          <p class="text-secondary p--responsive mb-3">
            Emails notifications are driven by automated tasks.
            You can create more custom notifications in 
            <a href="/help_tickets/automated_tasks">
              Automated Tasks
            </a>.
          </p>
          <table
            v-if="emailSettings"
            class="table"
          >
            <tbody>
              <tr
                v-for="(setting) in emailSettingsSorted"
                :key="setting.id"
              >
                <td>
                  <p class="not-as-small font-weight-bold mb-1">
                    {{ toTitle(setting.event) }}
                    <a
                      v-tooltip="`View Automated Task Details`"
                      :href="`/help_tickets/automated_tasks/${setting.automatedTaskId}/edit`"
                      class="ml-2"
                    >
                      <i class="genuicon-nav-automation small" />
                    </a>
                  </p>
                  <p class="small mb-0 p--responsive">
                    {{ setting.description }}
                  </p>
                </td>

                <td
                  class="text-right"
                >
                  <material-toggle
                    ref="sampleToggle"
                    class="mt-1"
                    :init-active="setting.optedIn"
                    :data-tc-email-toggle="setting.event"
                    @toggle-sample="onToggle(setting)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import _sortBy from 'lodash/sortBy';
  import { mapMutations } from 'vuex';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import * as HelpdeskSettings from './index';
  import MaterialToggle from '../../shared/material_toggle.vue';
  import InboundEmails from "./email/inbound.vue";
  import workspaceSettingsBanner from './workspace_settings_banner.vue';

  export default {
    components: {
      MaterialToggle,
      InboundEmails,
      ...HelpdeskSettings,
      workspaceSettingsBanner,
    },
    mixins: [ strings, permissionsHelper ],
    data() {
      return {
        emailSettings: null,
      };
    },
    computed: {
      emailSettingsSorted() {
        if (!this.emailSettings) {
          return this.emailSettings;
        }
        return _sortBy(this.emailSettings, (setting) => this.toTitle(setting.event));
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),
      onWorkspaceChange() {
        this.fetchCompanyMailers();
      },
      fetchCompanyMailers() {
        this.setLoadingStatus(true);
        http
          .get('/company_mailers.json?module=Helpdesk')
          .then(res => {
            this.emailSettings = res.data.mailers;
          })
          .catch(() => {
            this.emitError('There was an issue fetching your email settings. Please refresh the page and try again.');
          })
          .finally(() => {
            this.setLoadingStatus(false);
          });
      },
      onToggle(setting) {
        const url =  `/company_mailers/${setting.id}.json?module=Helpdesk`;

        this.setLoadingStatus(true);
        http
          .put(url, { 'opted_in': !setting.optedIn, 'module': 'Helpdesk' })
          .then(res => {
            this.emitSuccess('Successfully updated your email notification settings');
            this.emailSettings = res.data.mailers;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error updating your email notification settings`);
          })
          .finally(() => {
            this.setLoadingStatus(false);
          });
      },
    },
  };
</script>

