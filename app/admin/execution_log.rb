ActiveAdmin.register AutomatedTasks::ExecutionLog do
  menu parent: 'Logs', priority: 10
  breadcrumb do
    ['admin', 'logs']
  end

  filter :company
  filter :workspace
  filter :entity_name, label: "Module Name", as: :select
  filter :message, label: "Status"

  actions :all, except: [:new, :edit]

  index do
    selectable_column
    id_column
    column ("Module Name") { |event| "#{event.entity_name}" }
    column ("Entitiy ID") { |event| "#{event.entity_id}" }
    column ("Company") { |event| "#{event.company&.name}" }
    column ("Workspace") { |event| "#{event.workspace&.name}" }
    column ("Status") { |event| truncate(event.message, length: 250) }
    column :created_at
    column :updated_at
    column :completed_at
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
