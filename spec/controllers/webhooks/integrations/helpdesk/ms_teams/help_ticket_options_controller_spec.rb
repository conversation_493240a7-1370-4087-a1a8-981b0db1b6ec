require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON>per

describe Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketOptionsController, type: :controller do
  create_company_and_user

  let!(:config) { create(:ms_teams_config, company: company) }
  let!(:helpdesk_custom_email) { create(:helpdesk_custom_email, email: first_company_user.email) }
  let!(:custom_form) { create(:custom_form, company: company, company_module: 'helpdesk', form_name: 'Helpdesk email', default: true, custom_form_fields_attributes: custom_form_fields_attributes) }
  let!(:create_and_set_default_custom_form) do
    custom_form.update_columns(default: false)
    cf = FactoryBot.create(:custom_form, company: company, company_module: 'helpdesk',  default: true, custom_form_fields_attributes: custom_form_fields_attributes)
    cf.helpdesk_custom_form.update_columns(helpdesk_custom_email_id: helpdesk_custom_email.id)
    cf
  end

  let!(:first_user) do
    user = FactoryBot.create(:user, first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  end
  let!(:first_company_user) { company.company_users.create(user: first_user) }

  let!(:params) do
    {
      team: { id: config.team_id },
      channel: { id: config.channel_id },
      current_user: user.email,
      options_type: 'custom_fields',
      workspace_id: workspace.id
    }
  end
  let(:custom_form_fields_attributes) {
    [
      {
        field_attribute_type: "priority",
        label: "Priority",
        name: "priority",
        options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS
      },
      {
        field_attribute_type: "status",
        label: "Status",
        name: "status",
        options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS
      },
      {
        field_attribute_type: "people_list",
        label: "Assigned To",
        name: "assigned_to",
        options: "[{\"id\":#{company_user.id},\"name\":\"#{company_user.name}\",\"contributorId\":#{company_user.contributor_id}}]"
      }
    ]
  }

  describe '#index' do
    it 'will return custom fields options' do
      get :index, params: params

      expect(response).to have_http_status(:ok)
      options_keys = ['priority_options', 'status_options', 'contributor_options']
      expect(JSON.parse(response.body).keys - options_keys).to eq([])
    end

    it 'will not return that contributor whose email is set as custom email in custom form' do
      get :index, params: params

      expect(response).to have_http_status(:ok)
      expect(company.contributors.find_by_id(first_company_user.contributor_id)).to be_present
      contributors = JSON.parse(response.body)['contributor_options']
      expect(contributors.find { |c| c['value'] == first_company_user.contributor_id }).to be_nil
    end
  end
end
