ActiveAdmin.register Logs::AssetDiscoveryLog, as: 'asset_discovery_log' do
  menu parent: 'Logs', priority: 4
  breadcrumb do
    ['admin', 'logs']
  end

  filter :by_company_id_in, as: :string, :label => 'Company Id'
  filter :by_company_name_in, as: :string, :label => 'Company Name'
  filter :by_company_subdomain_in, as: :string, :label => 'Company Subdomain'
  filter :source, as: :select, collection: Logs::AssetDiscoveryLog.sources.map {|key, value| [key, value]}
  filter :created_at
  sidebar "Custom Filters", only: :index do
    active_admin_form_for :os_filter, url: admin_asset_discovery_logs_path, method: :get do |f|
      f.inputs do
        f.input :os_type, as: :select, collection: [['MAC OS', 'MAC OS'], ['Windows', 'Windows']], selected: params.dig(:os_filter, :os_type)
      end
      f.actions do
        f.submit "Filter"
      end
    end
  end

  index do
    column :id
    column :company
    column :response do |log|
      log.response == nil ? "Empty" : log.response.to_s.truncate(250)
    end
    column :attachment do |log|
      log.attachment.url.present? ? link_to('download', log.attachment.url) : 'No File'
    end
    column ("created_at") { |event| "#{event.created_at.to_written_time}" }
    column :source
    column :locationable
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa

    def scoped_collection
      logs = super
      os_type = params.dig(:os_filter, :os_type)
      if os_type.present? && os_type != ''
        log_ids = logs.select do |log|
          case log.source
          when 'probe'
            os_type == 'Windows'
          when 'agent'
            agent_location = AgentLocation.find_by(id: log.locationable_id)
            agent_location&.os == os_type
          when 'selfonboarding'
            log.response.present? && JSON.parse(log.response).dig(0, 'managedAsset', 'os') == os_type
          else
            false
          end
        end.map(&:id)
        logs = Logs::AssetDiscoveryLog.where(id: log_ids)
      end
      logs
    end
  end
end
