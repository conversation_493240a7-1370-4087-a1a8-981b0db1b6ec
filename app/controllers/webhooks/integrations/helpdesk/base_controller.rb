class Webhooks::Integrations::Helpdesk::BaseController < ApplicationController
  include MultiCompany::HelpTicketScoping
  include MsTeamsHelper

  before_action :set_current_user
  before_action :set_resources

  private

  def authenticate_user
    if scoped_company_user.blank?
      res = { message: "Sorry, you are not a member of #{scoped_company.subdomain}. Please contact your admin." }
      log_event(res, 'authenticate_user', :success)
      render json: res, status: :unauthorized
    end
  end

  def set_resources
    if company_subdomain
      @scoped_company ||= Company.find_by_cache(subdomain: company_subdomain)
      
      if @scoped_company.blank?
        return render json: { message: "Company with subdomain '#{company_subdomain}' not found." }, status: :not_found
      end
    end

    @config = fetch_config
    @scoped_company ||= @config&.company

    if @config.blank? && company_subdomain.blank?
      pronoun = is_a_group_chat? ? 'a' : 'this'
      res = { message: "Please link #{pronoun} channel with Genuity to perform this action. You can use _@Genuity link_ command to link #{pronoun} channel to Genuity." }
      log_event(res, 'set_resources', :success)
      render json: res, status: :not_found
    elsif @config.present? && !@config.active?
      res = { message: 'This channel is deactivated from Genuity web app.', is_channel_deactivated: true }
      log_event(res, 'set_resources', :success)
      render json: res, status: :not_found
    end

    update_missing_tenant_id if should_update_tenant_id?
  end

  def update_missing_tenant_id
    @config.update_column(:tenant_id, tenant_id)
  end

  def scoped_workspace
    ticket.workspace
  end

  def authorize_user
    if expanded_privilege.blank?
      res = { message: 'Sorry, you\'re not authorized to perform this action.' }
      log_event(res, 'authorize_user', :success)
      render json: res, status: :unauthorized
    end
  end

  def owned_by
    [ ticket_creator&.id ].compact
  end

  def company_subdomain
    params[:subdomain]
  end

  def channel_id
    return params[:channel] if params[:channel].blank?
    params[:channel].is_a?(String) ? JSON.parse(params[:channel])['id'] : params[:channel][:id]
  end

  def channel_name
    return params[:channel] if params[:channel].blank?
    chnl_name = params[:channel].is_a?(String) ? JSON.parse(params[:channel])['name'] : params[:channel][:name]
    chnl_name || (is_a_group_chat? ? chat_name : 'General')
  end

  def is_a_group_chat?
    return false if params[:channel].blank?
    chat_type = params[:channel].is_a?(String) ? JSON.parse(params[:channel])['channelType'] : params[:channel][:channel_type]
    chat_type == 'groupChat'
  end

  def chat_name
    return false if !member_names
    return member_names.join(' - ') if member_names.count <= 2
    "#{member_names.first(2).join(' - ')} + #{member_names.count - 2} more"
  end

  def member_names
    return false if params[:channel].blank?
    params[:channel].is_a?(String) ? JSON.parse(params[:channel])['memberNames'] : params[:channel][:member_names]
  end

  def team_id
    return params[:team] if params[:team].blank?
    params[:team].is_a?(String) ? JSON.parse(params[:team])['id'] : params[:team][:id]
  end

  def team_name
    return 'Group Chat' if is_a_group_chat?
    return params[:team] if params[:team].blank?
    params[:team].is_a?(String) ? JSON.parse(params[:team])['name'] : params[:team][:name]
  end

  def current_user_email
    params[:currentUser] || params[:current_user]
  end

  def set_current_user
    @current_user = User.where("lower(email) = lower(?)", current_user_email).first
  end

  def tenant_id
    @tenant_id ||= begin
      team_data = params[:team]
      channel_data = params[:channel]
      
      tenant_id_from_data(team_data) || tenant_id_from_data(channel_data)
    end
  end

  def tenant_id_from_data(data)
    return JSON.parse(data)['tenantId'].presence if data.is_a?(String)
  
    data&.dig(:tenant_id)
  end

  def should_update_tenant_id?
    @config.present? && @config.tenant_id.blank? && tenant_id.present?
  end

  def guest
    @guest ||= Guest.find_by("lower(email) = lower(?) AND company_id = ?", current_user_email, scoped_company.id)
  end

  # This is critical for security, make sure no config is created for any outside user in compnay
  # Even if we allow as Guest it can have spam message issues
  def fetch_config
    # TODO: MS teams: remove env check 
    if is_a_group_chat? && is_active_company_user? && !ms_teams_feature_access_pending?(@current_user.companies.first.id)
      dm_config = "::Integrations::#{integration}::Config".constantize.find_by(channel_id: channel_id) 
      # if config is present but not active return explicit nil
      return nil if dm_config.present? && !dm_config.active
      
      return dm_config if dm_config.present?

      # If a user is in two companies and he DMS then we will pick the first company where his teams has integration
      authorized_config = "::Integrations::#{integration}::Config".constantize.find_by(company_id:  @current_user.companies, authorized_config_id: nil)
      generate_config(authorized_config)
    elsif is_a_group_chat? && !is_active_company_user?
      dm_config = "::Integrations::#{integration}::Config".constantize.find_by(channel_id: channel_id) 
      
      # if config is present but not active return explicit nil
      return nil if dm_config.present? && !dm_config.active

      return dm_config if dm_config.present?

      # For the guest user caser we will use tenant id to find the company
      config = "::Integrations::#{integration}::Config".constantize.find_by(tenant_id: tenant_id, authorized_config_id: nil, active: true)
      generate_config(config)
    else
      "::Integrations::#{integration}::Config".constantize.find_by(team_id: team_id, channel_id: channel_id)
    end
  end

  def generate_config(config)
    return if config.nil?

    bot_dm_config = config.dup
    # The member_names will only have one person in case for BOT DM        
    bot_dm_config.channel_name = "@Genuity - #{member_names.first}" 
    bot_dm_config.channel_id = channel_id
    bot_dm_config.authorized_config_id = config.id
    bot_dm_config.skip_automated_tasks_creation = true
    # as the user can select out of multiple workspaces so its not binded to any workspace 
    bot_dm_config.workspace_id = nil
    bot_dm_config.save!
    bot_dm_config
  end

  def is_active_company_user?
     !CompanyUser.find_by(user_id: @current_user)&.granted_access_at.nil?
  end

  def log_event(response, api_type, status, fur_detail={}, excep = nil)
    webhook_response = { response: response }.to_s if response.present?
    log_params = {
      api_type: api_type,
      class_name: self.class.name,
      company_id: @config&.company_id || fur_detail[:company_id],
      status: status,
      detail: { params: params, path: path_info }.merge(fur_detail),
      activity: :action,
      response: webhook_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil,
    }
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end

  def path_info
    request.path
  end
end
