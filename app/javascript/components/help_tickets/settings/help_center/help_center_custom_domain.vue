<template>
  <div class="clearfix row mx-1">
    <div class="col mt-5">
      <h4 class="font-weight-normal mb-3 h5--responsive">Setup Custom Domain For Help Center</h4>
      <div class="row">
        <div class="col-lg-6">
          <div class="box box--with-heading box--natural-height readable-length--large mb-4">
            <h6 class="box__heading">
              Custom Domain
            </h6>
            <div class="box__inner setting-height">
              <form>
                <div class="form-group text-muted small">Please enter the domain name for which you have obtained an SSL certificate to ensure secure connection.</div>
                <div class="row col-12 form-group justify-content-between">
                  <label>Activate?
                    <i
                      v-if="!customDomain.verified"
                      v-tooltip.right="'First, you need to verify CNAME record, then you will be allowed to activate this custom domain'"
                      class="nulodgicon-information-circled"
                    />
                  </label>
                  <material-toggle
                    :init-active="customDomain.activeDomain"
                    :disabled="!customDomain.verified"
                    @toggle-sample="toggleActiveDomain"
                  />
                </div>
                <div class="form-group">
                  <div>Domain Name <span class="required"/></div>
                  <div class="text-muted smallest">Enter the name of your domain. Ensure that the SSL certificate is a wildcard certificate or matches the specified domain.</div>
                  <input
                    v-model="customDomain.name"
                    type="text"
                    class="form-control mt-1"
                  >
                  <span class="text-muted smallest"><b>Example:</b> helpdesk.{{ companyDomain }}.com</span><br>
                </div>
                <div class="form-group">
                  <label>CNAME Status
                    <small
                      v-if="customDomain.verified"
                      class="text-success ml-1"
                    >
                      Verified <i class="nulodgicon-android-checkmark-circle" />
                    </small>
                    <span 
                      v-else
                      class="text-muted small ml-2"
                    >
                      <a
                        href="#"
                        @click.prevent.stop="verifyCNAME"
                      >
                        Verify
                      </a>
                    </span>
                  </label>
                  <div v-if="!customDomain.verified">
                    <div 
                      v-if="!isNewDomain"
                      class="text-muted smallest"
                    >
                      We'll send you an email after saving the custom domain, including steps on how to create a CNAME record. Please verify your custom domain by adding a CNAME record in your domain provider's DNS settings as mentioned in the email.
                    </div>
                    <div 
                      v-else
                      class="text-muted smallest"
                    >
                      We've sent you an email including the steps on how to create a CNAME record. Please verify your custom domain by adding a CNAME record in your domain provider's DNS settings as mentioned in the email.
                      <div class="text-muted smallest">Didn't received the email yet? 
                        <a
                          href="#"
                          class="smallest"
                          @click.stop.prevent="resendCustomDomainEmail"
                        >
                          Resend email
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
              <span
                v-if="loading"
                class="ml-3 d-inline-block"
              >
                <pulse-loader
                  loading
                  class="ml-3 mt-1"
                  color="#0d6efd"
                  size="0.5rem"
                />
              </span>
              <div v-if="customDomain.verified">
                <label>Help Center URL</label>
                <a
                  class="d-block w-fit-content text-secondary align-middle btn-light rounded btn-flat py-0 px-2 not-as-small"
                  :href="helpCenterPath"
                  target="_blank"
                >
                  {{ helpCenterPath }}
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-6">
          <div class="box box--with-heading box--natural-height readable-length--large mb-4">
            <h6 class="box__heading">
              SSL Certificate
            </h6>
            <div class="box__inner setting-height">
              <form>
                <div class="form-group">
                  <div class="form-group text-muted small">Open your Primary Certificate, Intermediary Certificate and Primary Key using a Text Editor and copy paste the contents below.</div>
                  <!-- Hiding type field for now, will display it when we have more than one types -->
                  <multi-select
                    id="certificate"
                    placeholder="Select Certificate Type"
                    class="domain-field-width hidden"
                    taggable
                    :options="['Custom Certificate']"
                    :value="customDomain.certificateType"
                    @select="selectType"
                  />
                </div>
                <div class="form-group">
                  <label>Primary Key<span class="required" /></label>
                  <textarea
                    v-model="customDomain.primaryKey"
                    type="text"
                    class="form-control cert-keys-height"
                  />
                </div>
                <div class="form-group">
                  <label>Primary Certificate<span class="required"/></label>
                  <textarea
                    v-model="customDomain.primaryCertificate"
                    type="text"
                    class="form-control cert-keys-height"
                  />
                </div>
                <div class="form-group">
                  <label>Intermediary Certificate<span class="required"/></label>
                  <textarea
                    v-model="customDomain.intermediaryCertificate"
                    type="text"
                    class="form-control cert-keys-height"
                  />
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="sticky-btn-holder bg-lighter text-center border-top border-light">
        <submit-button
          :btn-classes="'px-4'"
          :btn-content="`${isNewDomain ? 'Update' : 'Save'} Settings`"
          :is-saving="loading"
          :saving-content="`${isNewDomain ? 'Updating' : 'Saving'}} Custom Domain`"
          @submit="confirmationModal"
        />
      </div>
    </div>
    <sweet-modal
      ref="emailSentModal"
      title="Email has been sent to you successfully."
    >
      <template slot="default">
        <h6>
          We've sent you an email at {{ companyEmail }}. Please follow the steps mentioned in the email to complete the setup of your custom domain.
        </h6>
      </template>
    </sweet-modal>
    <hd-settings-confirmation-modal
      ref="helpdeskSettings"
      @update="saveOrUpdateDomain"
    />
  </div>
</template>

<script>
  import http from 'common/http';
  import strings from 'mixins/string';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import MultiSelect from 'vue-multiselect';
  import { SweetModal } from 'sweet-modal-vue';
  import MaterialToggle from '../../../shared/material_toggle.vue';
  import SubmitButton from '../../../shared/submit_button.vue';
  import * as HelpdeskSettings from '../index';
  import hdSettingsConfirmationModal from '../../../shared/hd_settings_confirmation_modal.vue';

  export default {
    components: {
      PulseLoader,
      MultiSelect,
      MaterialToggle,
      SubmitButton,
      SweetModal,
      ...HelpdeskSettings,
      hdSettingsConfirmationModal,
    },
    mixins: [strings, permissionsHelper],
    data() {
      return {
        companyEmail: '',
      };
    },
    computed: {
      ...mapGetters([
        'tickets',
        'customDomain',
        'loading',
      ]),
      isNewDomain() {
        return !!this.customDomain?.id;
      },
      companyDomain() {
        return getCompanyFromStorage().subdomain;
      },
      helpCenterPath() {
        return `https://${this.customDomain.name}/help_center`;
      },
    },
    methods: {
      ...mapMutations(['setLoading', 'setCustomDomain']),
      ...mapActions(['fetchCustomDomain', 'updateCustomDomain']),
      onWorkspaceChange() {
        this.fetchCustomDomain();
      },
      toggleActiveDomain() {
        this.customDomain.activeDomain = !this.customDomain.activeDomain;
        http
          .put(`/custom_domains/${this.customDomain.id}/toggle_active`, { custom_domain: this.customDomain })
          .then(res => {
            this.setCustomDomain(res.data.domain);
            if (this.customDomain.activeDomain)
              this.emitSuccess('Activated custom domain successfully.');
            else
              this.emitSuccess('Deactivated custom domain successfully.');
          })
          .catch(() => {
            this.emitError('Sorry, there was an error while activating/deactivating custom domain.');
          });
      },
      verifyCNAME() {
        if (!this.customDomain.id) {
          this.emitError("Please save the custom domain first, then verify the CNAME record.");
          return;
        }

        http
          .get(`/custom_domains/${this.customDomain.id}/verify_name`)
          .then(res => {
            this.setCustomDomain(res.data.domain);
            if (this.customDomain.verified)
              this.emitSuccess('CNAME has been verified successfully');
            else
              this.emitError("Sorry, there was an error while verifying CNAME. Please, make sure the CNAME is correctly configured on your side.");
          })
          .catch(() => {
            this.emitError('Sorry, there was an error while verifying CNAME. Please refresh the page and try again.');
          });
      },
      selectType(certificateType) {
        this.customDomain.certificateType = certificateType.id;
      },
      confirmationModal() {
        this.$refs.helpdeskSettings?.open();
      },
      saveOrUpdateDomain() {
        if (this.customDomain?.id) {
          this.updateCustomDomain().then(() => {
            this.emitSuccess('Custom Domain has been updated successfully');
          });
        } else {
          this.saveDomain();
        }
      },
      saveDomain() {
        this.setLoading(true);
        http
          .post('/custom_domains.json', { custom_domain: this.customDomain })
          .then((res) => {
            this.setCustomDomain(res.data.body.domain);
            this.companyEmail = res.data.body.companyEmail;
            this.emitSuccess('Custom Domain has been created successfully');
            this.setLoading(false);
            this.$refs.emailSentModal.open();
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error creating custom domain. ${error.response.data.message}`);
            this.setLoading(false);
          });
      },
      resendCustomDomainEmail() {
        http
          .get(`/custom_domains/${this.customDomain.id}/resend_email.json`)
          .then(() => {
            this.emitSuccess('Email has been sent to the user successfully.');
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error resending email. ${error.response.data.message}`);
          });
      },
    },
  };
</script>

<style scoped lang="scss">
  .domain-field-width {
    width: 18rem;
  }

  .setting-height {
    height: 30rem;
  }

  .cert-keys-height {
    min-height: 4rem;
    max-height: 4rem;
  }
</style>
