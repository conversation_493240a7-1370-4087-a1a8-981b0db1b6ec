<template>
  <div>
    <kanban-preview
      v-if="customSelectedFields"
      :column-data="customSelectedFields"
    />

    <div class="row">
      <div class="col-6">
        <h5 class="font-weight-normal mb-3">
          Inactive Fields
        </h5>

        <div class="box h-auto bg-very-light">
          <draggable
            v-model="customUnselectedFields"
            class="w-100"
            group="columns"
            v-bind="dragOptions"
            handle=".inactive-items--movable"
            @end="endSelectedSort"
          >
            <div
              v-if="hasNoCustomUnselectedFields"
              class="w-100"
            >
              <list-column-skeleton
                v-for="index in numUnselectedSkeletons"
                :key="`unselected-colums-skeleton-${index}`"
              />
            </div>
            <div
              v-for="(column, index) in customUnselectedFields"
              v-else
              :key="column.id"
              class="col-12 px-0"
            >
              <div class="box box--flat mb-2 p-2 pl-3 text-secondary not-as-small justify-content-between inactive-items--movable active-items--movable">
                <span class="pl-2 pt-1">
                  {{ columnLabel(column) }}
                </span>
                <span class="handle genuicon-draggable"/>
                <span
                  v-if="canManage"
                  v-tooltip="`Activate ${columnLabel(column)}`"
                  class="btn d-block mr-0 ml-auto p-0 pr-1"
                  @click.stop.prevent="activateColumn(column, index)"
                >
                  <i
                    class="nulodgicon-android-add-circle clickable h5 text-blue-dark"
                    :data-tc-list-view-column-activate-btn="columnLabel(column)"
                  />
                </span>
              </div>
            </div>
          </draggable>
        </div>
      </div>

      <div class="col-6">
        <h5 class="font-weight-normal mb-3">
          Active Fields
        </h5>

        <div class="box h-auto bg-very-light">
          <div
            v-if="hasNoCustomSelectedFields"
            class="w-100"
          >
            <list-column-skeleton
              v-for="index in numSelectedSkeletons"
              :key="`selected-colums-skeleton-${index}`"
            />
          </div>

          <draggable
            v-model="customSelectedFields"
            class="w-100"
            group="columns"
            v-bind="dragOptions"
            handle=".active-items--movable"
            @end="endSelectedSort"
          >
            <div
              v-for="(column, index) in customSelectedFields"
              :key="`selected-colums-${index}`"
              class="col-12 px-0"
              :class="{
                'active-items--movable': !isDefaultColumn(columnLabel(column)),
                'column--disabled': isDefaultColumn(columnLabel(column))
              }"
            >
              <div class="box box--flat mb-2 py-2 pr-2 text-secondary not-as-small justify-content-between">
                <span class="pl-1">
                  {{ columnLabel(column)}}
                </span>
                <span v-if="(isWrite || isScoped) && !isDefaultColumn(columnLabel(column))">
                  <span class="handle genuicon-draggable"/>
                  <span
                    v-tooltip="`Deactivate ${columnLabel(column)}`"
                    class="btn d-block mr-0 ml-auto p-0 pr-1"
                    @click.stop.prevent="deactivateColumn(column, index)"
                  >
                    <i class="genuicon-minus-circle clickable h5 text-red-dark" />
                  </span>
                </span>
              </div>
            </div>
          </draggable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import ListColumnSkeleton from "components/shared/skeletons/list_column_skeleton.vue";
  import permissionsHelper from "mixins/permissions_helper";
  import Draggable from 'vuedraggable';
  import strings from 'mixins/string';
  import common from 'components/shared/module_onboarding/common';
  import http from 'common/http';
  import kanbanPreview from 'components/shared/kanban_preview.vue';

  export default {
    components: {
      ListColumnSkeleton,
      Draggable,
      kanbanPreview,
    },
    mixins: [permissionsHelper, strings, common],
    props: {
      selectedFields: {
        type: Array,
        default: () => [],
      },
      unselectedFields: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        drag: true,
        dragOptions: {
          animation: 200,
          ghostClass: "ghost",
        },
        numUnselectedSkeletons: 6,
        numSelectedSkeletons: 5,
        localUnselectedFields: [],
        localSelectedFields: [],
        defaultColumns: ['Subject', 'Created by'],
      };
    },
    computed: {
      customUnselectedFields: {
        get() {
          return this.localUnselectedFields;
        },
        set(value) {
          this.localUnselectedFields = value;
        },
      },
      customSelectedFields: {
        get() {
          return this.localSelectedFields;
        },
        set(value) {
          this.localSelectedFields = value;
        },
      },
      hasNoCustomUnselectedFields() {
        return !this.customUnselectedFields?.length;
      },
      hasNoCustomSelectedFields() {
        return !this.customSelectedFields?.length;
      },
      isDefaultColumn() {
        return (name) => this.defaultColumns.includes(name);
      },
    },
    watch: {
      unselectedFields() {
        this.localUnselectedFields = [...this.unselectedFields];
      },
      selectedFields() {
        this.localSelectedFields = [...this.selectedFields];
      },
    },
    methods: {
      columnLabel(column){
        return column.name;
      },
      endSelectedSort() {
        this.customSelectedFields = this.localSelectedFields;
        this.customUnselectedFields = this.localUnselectedFields;
        this.saveSelectedData();
      },
      saveSelectedData() {
        this.localSelectedFields.forEach(column => { column.active = true; });
        this.localUnselectedFields.forEach(column => { column.active = false; });

        const allColumns = [...this.localSelectedFields, ...this.localUnselectedFields];
        http.put('/ticket_kanban_fields.json', { fields: allColumns })
        .then(() => {
          this.emitSuccess("Saved selected data");
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error saving selected data.${error}`);
        });
      },
      deactivateColumn(column, index) {
        if (index !== -1) {
          this.localSelectedFields.splice(index, 1);
          this.localUnselectedFields.push({
            ...column,
            active: false,
          });
        }
        this.saveSelectedData();
      },
      activateColumn(column, index) {
        if (index !== -1) {
          this.localUnselectedFields.splice(index, 1);
          this.localSelectedFields.push({
            ...column,
            active: true,
          });
        }
        this.saveSelectedData();
      },
    },
  };
</script>
<style lang="scss" scoped>
  .active-items--movable {
    cursor: move;

    .box {
      padding-left: 1.5rem;
    }
  }

  .ghost {
    opacity: 0.5;
    background-color: #E3F2FD;
  }

  .column--disabled {
    cursor: default;
    pointer-events: none;

    .box {
      background-color: $themed-light;
      border-color: $themed-very-fair;
      padding-left: 1rem;
    }

    .genuicon-add-circle,
    .genuicon-minus-circle {
      visibility: hidden;
    }
  }

  .genuicon-add-circle,
  .genuicon-minus-circle {
    &:before {
      line-height: 1.5rem;
    }
  }

  .handle {
    cursor: move;
    font-size: 1.125rem;
    left: 0.275rem;
    position: absolute;
    top: 30%;
  }
</style>
