<template>
  <div v-if="syncingConnectors.length > 0">
    <div
      v-if="minimized"
      class="minimized-box p-2 text-white"
      :class="{ 'bg-success': connectorSyncedSucessfully }"
    >
      <div class="box-top d-flex justify-content-between">
        <span
          class="cursor-pointer"
          @click="openMaxModal"
        >
          <i class="icon genuicon-expand" />
        </span>
        <span class="small">
          {{ `${syncedConnectors}/${syncingConnectors.length}` }}
        </span>
      </div>
      <div class="box-bottom d-flex justify-content-between">
        <div class="text-left font-weight-bold">
          {{ header }}
        </div>
        <div>
          <clip-loader
            v-if="!connectorSyncedSucessfully"
            color="#0d6efd"
            size="0.9rem"
            :loading="true"
          />
        </div>
      </div>
    </div>
    <div>
      <sweet-modal
        ref="maximizedModal"
        width="37%"
        class="sync-connector"
        @close="closeMaxModal"
      >
        <template>
          <div>
            <div class="pb-3">
              <h4 class="text-left font-weight-bold text-secondary">
                {{ header }}
              </h4>
              <p class="text-left text-secondary">
                {{ subHeader }}
              </p>
            </div>
          </div>
          <div>
            <table class="table">
              <tbody
                v-for="(connector, index) in syncingConnectors"
                :key="index"
              >
                <tr>
                  <td class="col-2 align-right">
                    <img
                      :src="connector.image"
                      height="30px"
                    >
                    <div class="connector-name font-weight-bold mt-1">
                      {{ connector.name }}
                    </div>
                  </td>
                  <td class="col-8">
                    <integration-progress-bar :value="connector.loadingValue"/>
                  </td>
                  <td class="col-2 align-middle">
                    <sync-loader
                      v-if="connector.status == 'In Progress'"
                      color="#0d6efd"
                      size="0.4rem"
                      :loading="true"
                    />
                    <span
                      v-else-if="connector.status == 'Connected'"
                      class="connector-status bg-success text-white"
                    >
                      {{ connector.status }}
                    </span>
                    <span
                      v-else-if="connector.status == 'Failed'"
                      class="connector-status bg-danger text-white"
                    >
                      {{ connector.status }}
                    </span>
                  </td>
                </tr>
                <tr 
                  v-if="connector.status === 'Connected' && showAssetCounts(connector.name)"
                  class="text-left not-as-small"
                >
                  <td 
                    colspan="3" 
                    class="count-heading"
                  >
                    <ul v-if="hasAssetCounts(connector.name)">
                      <li>Imported Discovered Assets: <strong>{{ getDiscoveredAssetsCount(connector.name) }}</strong></li>
                      <li>Imported Managed Assets: <strong>{{ getManagedAssetsCount(connector.name) }}</strong></li>
                    </ul>
                    <div v-else>
                      No<strong> New Managed/Discovered Assets </strong>Found
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </sweet-modal>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import IntegrationProgressBar from 'components/shared/module_onboarding/integration_progress_bar.vue';
  import { SweetModal } from 'sweet-modal-vue';
  import ClipLoader from 'vue-spinner/src/ClipLoader.vue';

  export default {
    components: {
      ClipLoader,
      SyncLoader,
      SweetModal,
      IntegrationProgressBar,
    },
    props: ['connectorType'],
    data() {
      return {
        minimized: true,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', [
        'syncingConnectors', 
        'importedUbiquitiDiscoveredAssets', 
        'importedUbiquitiManagedAssets', 
        'importedKaseyaDiscoveredAssets', 
        'importedKaseyaManagedAssets',
        'importedMerakiDiscoveredAssets', 
        'importedMerakiManagedAssets',
        'importedSophosDiscoveredAssets',
        'importedSophosManagedAssets',
        'importedAzureAdDiscoveredAssets',
        'importedAzureAdManagedAssets',
        'importedKandjiDiscoveredAssets',
        'importedKandjiManagedAssets',
        'importedGoogleWorkspaceDiscoveredAssets',
        'importedGoogleWorkspaceManagedAssets',
        'importedMsIntuneDiscoveredAssets',
        'importedMsIntuneManagedAssets',
        'importedJamfProDiscoveredAssets',
        'importedJamfProManagedAssets',
        'importedAzureDiscoveredAssets',
        'importedAzureManagedAssets',
        'importedAwsDiscoveredAssets',
        'importedAwsManagedAssets',
        'importedGoogleDiscoveredAssets',
        'importedGoogleManagedAssets',
      ]),

      syncedConnectors() {
        return this.syncingConnectors.filter((conn) => conn.status === 'Connected').length;
      },
      connectorSyncedSucessfully() {
        return this.syncedConnectors === this.syncingConnectors.length;
      },
      header() {
        return this.connectorSyncedSucessfully ? `${this.connectorType} integrated` : `${this.connectorType} integrating`;
      },
      subHeader() {
        return  this.connectorSyncedSucessfully ? "Thank you for your patience and cooperation." :
          `Take a break and get fresh air while we sync your ${this.connectorType.toLowerCase()},
            we'll let you know when it's done. You can now minimize this modal.`;
      },
    },
    methods: {
      openMaxModal() {
        this.minimized = false;
        this.$refs.maximizedModal.open();
      },
      closeMaxModal() {
        this.minimized = true;
      },
      showAssetCounts(name) {
        return [
          'Ubiquiti', 'Kaseya', 'Cisco Meraki', 'Sophos', 'Azure AD Devices','Kandji', 'Google Workspace', 'Microsoft Intune',
          'Jamf Pro', 'Azure', 'AWS', 'GCP',
        ].includes(name);
      },
      hasAssetCounts(name) {
        return (
          this.getDiscoveredAssetsCount(name) > 0 ||
          this.getManagedAssetsCount(name) > 0
        );
      },
      getDiscoveredAssetsCount(name) {
        if (name === 'Ubiquiti') return this.importedUbiquitiDiscoveredAssets;
        if (name === 'Kaseya') return this.importedKaseyaDiscoveredAssets;
        if (name === 'Cisco Meraki') return this.importedMerakiDiscoveredAssets;
        if (name === 'Sophos') return this.importedSophosDiscoveredAssets;
        if (name === 'Azure AD Devices') return this.importedAzureAdDiscoveredAssets;
        if (name === 'Kandji') return this.importedKandjiDiscoveredAssets;
        if (name === 'Google Workspace') return this.importedGoogleWorkspaceDiscoveredAssets;
        if (name === 'Microsoft Intune') return this.importedMsIntuneDiscoveredAssets;
        if (name === 'Jamf Pro') return this.importedJamfProDiscoveredAssets;
        if (name === 'Azure') return this.importedAzureDiscoveredAssets;
        if (name === 'AWS') return this.importedAwsDiscoveredAssets;
        if (name === 'GCP') return this.importedGoogleDiscoveredAssets;
        return 0;
      },
      getManagedAssetsCount(name) {
        if (name === 'Ubiquiti') return this.importedUbiquitiManagedAssets;
        if (name === 'Kaseya') return this.importedKaseyaManagedAssets;
        if (name === 'Cisco Meraki') return this.importedMerakiManagedAssets;
        if (name === 'Sophos') return this.importedSophosManagedAssets;
        if (name === 'Azure AD Devices') return this.importedAzureAdManagedAssets;
        if (name === 'Kandji') return this.importedKandjiManagedAssets;
        if (name === 'Google Workspace') return this.importedGoogleWorkspaceManagedAssets;
        if (name === 'Microsoft Intune') return this.importedMsIntuneManagedAssets;
        if (name === 'Jamf Pro') return this.importedJamfProManagedAssets;
        if (name === 'Azure') return this.importedAzureManagedAssets;
        if (name === 'AWS') return this.importedAwsManagedAssets;
        if (name === 'GCP') return this.importedGoogleManagedAssets;
        return 0;
      },
    },
  };
</script>

<style lang="scss">
  $loader-height: 20px;

  .sync-connector {
    .sweet-content {
      padding-top: 1.5rem !important;
      padding-bottom: 2rem !important;
    }
  }

  .minimized-box {
    position: fixed;
    width: 10rem;
    height: 3.1rem;
    z-index: 9999;
    border: none;
    border-radius: 5px;
    background-color: $dark-drawer;
    padding: 4px;
    transform: translate(0, 0);
    bottom: 18px;
    right: 75px
  }

  .box-top {
    font-size: 12px;
  }

  .box-bottom {
    font-size: 14px;
  }

  .connector-name {
    font-size: 9px;
  }

  .connector-status {
    color: white;
    font-size: 8px;
    font-weight: 700;
    height: 16px;
    padding: 4px 6px;
    border-radius: 5px;
  }

  .sync-connector {
    .sweet-modal {
      .sweet-box-actions {
        z-index: 99;
        .sweet-action-close {
          background-image: url('https://nulodgic-static-assets.s3.amazonaws.com/images/resize-down.svg');
          background-size: 1.25rem;
          background-repeat: no-repeat;
          background-position: 0.62rem;
          svg {
            display: none;
          }
        }
      }
    }
  }
  
  .count-heading {
    border: none !important;
    padding-left: 2rem !important;
  }
</style>
