<template>
  <div class="clearfix row">
    <sub-menu />
    <div class="col mt-5">
      <div class="box box--block box--natural-height readable-length--large p-5 mb-5">
        <div class="box-inner w-100">
          <div
            v-if="!loading"
          >
            <workspace-settings-banner />
            <table class="table">
              <template v-for="(section, index) in sections">
                <tbody :key="section">
                  <h5
                    class="font-weight-normal mb-3 h5--responsive"
                    :class="{'mt-4': index !== 0}"
                    :data-tc-header="capitalizeText(section)"
                  >
                    {{ capitalizeText(section) }}
                  </h5>
                </tbody>
                <template v-for="setting in helpdeskSettings[section]">
                  <component
                    :is="`${toHyphenCase(setting.settingType)}-setting`"
                    :key="setting.id"
                    :setting="setting"
                    @fetch-helpdesk-settings="fetchHelpdeskSettings"
                  />
                </template>
              </template>
              <tbody v-if="isAdmin">
                <h5 class="font-weight-normal mt-4 mb-3 h5--responsive">
                  Work Schedule
                </h5>
                <tr>
                  <td colspan="2">
                    <div class="font-weight-bold mb-0 not-as-small p--responsive">
                      Hop over to
                      <a
                        :href="`${currentOrigin}/company/business_settings`"
                        target="_blank"
                      >
                        Work Schedule Settings
                      </a>
                      to set your business days and hours.
                    </div>
                  </td>
                </tr>
              </tbody>
              <tbody v-if="isSuperAdmin">
                <h5
                  class="font-weight-normal mt-4 mb-3 h5--responsive"
                  data-tc-heading="delete settings"
                >
                  Delete Tickets
                </h5>
                <tr>
                  <td colspan="2">
                    <div class="align-items-center d-flex font-weight-bold justify-content-between mb-0 not-as-small p--responsive">
                      Delete all help tickets
                      <button
                        class="btn btn-xs btn-outline-danger mb-1 ml-2"
                        data-tc-btn="delete all tickets"
                        @click="deleteAllTickets"
                      >
                        Delete all Tickets
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <span
            v-else
            class="ml-3 d-inline-block"
          >
            <pulse-loader
              loading
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </div>
      </div>
    </div>
    <sweet-modal
      ref="deleteModal"
      v-sweet-esc
      title="Before you delete all ticket..."
    >
      <template slot="default">
        <div v-if="!isDeleting">
          <h6 class="mb-4">
            All tickets of workspace {{ currentWorkspaceName }} will be permanently deleted.
          </h6>
          <div class="mb-2">
            To proceed type <span class="font-weight-bold text-danger">DELETE</span> in the input field below, then click ' Yes delete it '
          </div>
          <div class="form-group">
            <input
              v-model="deleteStatus"
              type="text"
              class="form-control"
            >
          </div>
        </div>
        <div v-else>
          <div class="d-flex justify-content-between">
            All tickets will be deleted, in a short while.
            <pulse-loader
              v-if="isDeleting"
              :loading="true"
              class="text-right"
              color="#0d6efd"
              size="0.5rem"
            />
          </div>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary mr-2"
        @click.stop="$refs.deleteModal.close"
      >
        No, keep it.
      </button>
      <button
        slot="button"
        class="btn btn-primary"
        data-tc-modal-delete-btn
        :disabled="!(deleteStatus == 'DELETE' && !isDeleting)"
        @click.stop="okDelete"
      >
        <span v-if="!isDeleting">
          Yes, delete it.
        </span>
        <span v-else-if="isDeleting">
          Deleting Tickets...
        </span>
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import strings from 'mixins/string';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import adminUser from 'mixins/admin_specific';
  import Pusher from 'common/pusher';
  import * as HelpdeskSettings from './index';
  import SubMenu from './sub_menu.vue';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';

  export default {
    components: {
      SubMenu,
      PulseLoader,
      SweetModal,
      ...HelpdeskSettings,
      workspaceSettingsBanner,
    },
    mixins: [strings, Pusher, permissionsHelper, adminUser],
    data() {
      return {
        workspaceId: null,
        helpdeskSettings: null,
        currentOrigin: null,
        loading: true,
        deleteStatus: "",
        isDeleting: false,
        sections: ['accessSettings', 'automationSettings', 'displaySettings'],
      };
    },
    computed: {
      ...mapGetters('multiCompany', [
        'companyFilterId',
        'workspaceFilterId',
      ]),
      ...mapGetters(['tickets']),

      currentWorkspaceName() {
        const workspace = getWorkspaceFromStorage();
        return workspace ? workspace.name : '';
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus', 'setTickets']),
      onWorkspaceChange() {
        this.setupPusherListeners();
        this.fetchHelpdeskSettings();
      },
      addName() {
        this.names.push(this.newName);
        this.newName = "";
      },
      fetchHelpdeskSettings() {
        http
          .get('/helpdesk_settings.json')
          .then(res => {
            this.currentOrigin = document.location.origin;
            this.helpdeskSettings = res.data.settings;
            this.loading = false;
            this.setLoadingStatus(false);
          })
          .catch(() => {
            this.emitError('There was an issue fetching your helpdesk settings. Please refresh the page and try again.');
            this.loading = false;
            this.setLoadingStatus(false);
          });
      },
      capitalizeText(text) {
        const words = text.split(/(?=[A-Z])/);
        return words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('  ');
      },
      setupPusherListeners() {
        Pusher.then(() => {
          if (this.$pusher) {
            const channel = this.$pusher.subscribe(this.$currentCompanyGuid);
            channel.unbind('delete-help-tickets');
            channel.bind('delete-help-tickets', data => {
              if (!data) {
                this.setTickets({});
                this.closeDeleteAllTicketsModal();
                this.emitSuccess("Help tickets successfully deleted");
              } else {
                this.closeDeleteAllTicketsModal();
                this.emitError("Some of the tickets were not deleted due to technical issues, please contact Genuity support for further help.");
              }
            });
          }
        });
      },
      deleteAllTickets() {
        this.$refs.deleteModal.open();
      },
      okDelete() {
        this.isDeleting = true;
        http
          .post(`/bulk_help_tickets/bulk_delete.json`)
          .catch(() => {
            this.closeDeleteAllTickets();
            this.emitError('Sorry, there was an error deleting help tickets.');
          });
      },
      closeDeleteAllTicketsModal() {
        if (this.$refs.deleteModal){
          this.$refs.deleteModal.close();
        }
        this.isDeleting = false;
        this.deleteStatus = "";
      },
    },
  };
</script>
