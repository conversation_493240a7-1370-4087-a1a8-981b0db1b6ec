module ManagedAssets
  module AssetsQuery
    class TagsClause < BaseClause
      def call(base)
        sort_by = @params[:sort_by]
        sort_order = @params[:sort_order]
        is_people_view = @params[:is_people] != 'false' if @params[:is_people]

        if (is_grid_view || (is_list_view && @selected_columns&.include?("tags"))) || is_people_view
          base = base.select(ManagedAssets::AssetsQuery::QueryConstants::ASSET_TAGS)
        end

        if @params[:active_tags].present? || @params[:tag].present?
          tag_ids = Array(@params[:active_tags])
          tag_ids << @params[:tag] if @params[:tag].present?
          base = base.where("managed_assets.id IN (#{ManagedAssets::AssetsQuery::QueryConstants::TAG_FILTER_SUBQUERY})", tag_ids)
        end

        if sort_order && sort_by == "asset_tag" && is_list_view
          base = base.order("managed_assets.asset_tag #{sort_order} NULLS LAST")
        end

        base
      end
    end
  end
end
