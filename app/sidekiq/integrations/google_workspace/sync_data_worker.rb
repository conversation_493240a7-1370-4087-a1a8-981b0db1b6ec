class Integrations::GoogleWorkspace::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include GoogleWorkspaceCommonMethods
  
  require 'google/apis/admin_directory_v1'

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @starting_time = Time.current
    @config = Integrations::GoogleWorkspace::Config.find_by(id: config_id)
    return if @config.nil?

    @import_target = @config.import_type
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    pusher(@config, true, 0.2)
    refresh_access_token
    save_mobile_devices
    pusher(@config, true, 0.5)
    Integrations::GoogleWorkspace::SaveChromeDevicesWorker.perform_async(@config.id, true, @new_discovered_assets_count, @new_managed_assets_count)
  rescue Exception => e
    company_integration.assign_attributes(sync_status: :failed,
                                          status: false,
                                          last_synced_at: DateTime.now,
                                          error_message: e.message,
                                          failure_count: company_integration.failure_count + 1)
    company_integration.save!
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'google_workspace_integration').to_json)
    pusher(@config, false, 0)
  end

  def company
    @company ||= @config.company
  end

  def company_integration
    @company_integration ||= @config.company_integration
  end

  def mobile_devices
    next_page_token = nil
    all_devices = []

    loop do 
      result = client.fetch_all_mobile_devices(next_page_token)
      all_devices.concat(result.mobiledevices) if result.mobiledevices
      next_page_token = result.next_page_token
      break unless next_page_token
    end
    return all_devices
  end

  def save_mobile_devices
    mobile_devices.each do |device|
      @discovered_asset = nil
      @managed_asset = nil
      mac_addresses = []
      display_name = device.model.presence || device.os
      serial_number = device.serial_number
      model = device.model
      operating_system = device.os
      os_name = device.os.split.first
      os_version = device.os.split.last
      mac_address = device.wifi_mac_address.try(:upcase)
      mac_addresses << mac_address if mac_address_valid?(mac_address)
      manufacturer = get_manufacturer(device)
      source = 'google_workspace'

      @discovered_asset = find_discovered_asset(company, false, serial_number, mac_addresses, display_name, nil, manufacturer, false)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: company.id)

      mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      ip_address_data = @discovered_asset.ip_address
      serial_data = serial_number.present? ? serial_number : @discovered_asset.machine_serial_no

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        machine_serial_no: serial_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        asset_type: device_asset_type(device),
        os: operating_system,
        os_name: os_name,
        os_version: os_version,
        model: model,
        source: source,
        discovered_asset_type: :device,
        lower_precedence: @is_lower_precedence
      )

      @discovered_asset.asset_softwares.find_or_initialize_by(
        software_type: 'Operating System',
        name: os_name || ""
      )

      managed_asset = @discovered_asset.managed_asset || find_managed_asset(company, serial_data, mac_addresses_data, display_name_data, manufacturer)

      if managed_asset.present?
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset_id = managed_asset.id
      end

      assign_discovered_assets_hardware_details(device)
      is_new = @discovered_asset.new_record?
      @discovered_asset.save
      @new_discovered_assets_count += 1 if is_new

      asset_source_data = {
        display_name: display_name,
        machine_serial_no: serial_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        os_name: os_name,
        manufacturer: manufacturer,
        source: source
      }

      add_source(asset_source_data)
      @discovered_asset.reload

      if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
        BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
        @new_managed_assets_count += 1
      end 
    end
  end

  def get_manufacturer(device)
    if device.manufacturer.present?
      return device.manufacturer
    else
      if device.os.downcase.include?("ios")
        return "Apple"
      end
    end
  end

  def assign_discovered_assets_hardware_details(device)
    hardware_detail = @discovered_asset.discovered_assets_hardware_detail ||= DiscoveredAssetsHardwareDetail.new
    hardware_detail.assign_attributes(
      imei: device.imei
    )
  end

  def device_asset_type(device)
    asset_type_name = ""

    if device.os.downcase.include?("ios")
      if device.model.downcase.include?("iphone")
        asset_type_name = get_asset_type("iPhone")
      else
        asset_type_name = get_asset_type("iPad")
      end
    else
      asset_type_name = get_asset_type("Mobile")
    end

    return asset_type_name
  end

  def client
    Integrations::GoogleWorkspace::FetchData.new(company.id)
  end
end
