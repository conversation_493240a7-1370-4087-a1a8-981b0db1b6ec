ActiveAdmin.register TemporaryCompanySubscription do
  menu parent: 'Metrics', priority: 11
  breadcrumb do
    ['admin', 'metrics']
  end

  filter :company_subdomain, as: :select

  actions :index, :show

  index do
    column :company_subdomain
    column :desired_modules
    column "Old plan interval", :current_interval
    column "New plan interval", :desired_interval
    column "Want 60 days on old plans", :do_not_change_plan
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
