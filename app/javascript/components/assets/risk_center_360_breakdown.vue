<template>
  <div v-if="readytoLoad">
    <div class="row">
      <!-- Blank state when agent not installed -->
      <span
        v-if="!hasAgentInstalled"
        class="box--disabled-overlay t-0"
      />
      <div
        v-if="!hasAgentInstalled"
        class="position-absolute w-100 z-index-1"
      >
        <div
          class="bg-themed-modal-sticky-footer w-50 mx-auto px-4 py-3 mt-6 rounded text-secondary d-flex align-items-center z-index-1"
        >
          <router-link
            to="/risk_center"
            class="icon-button shadow-none ml-n1 text-dark"
          >
            <span class="icon genuicon-device-health mr-3 ml-0 h2 mb-0" />
          </router-link>
          <span>
            Risk center metrics give you a quick and easy overview of the security health of your desktops, laptops, and servers.  Install the
            <router-link
              to="/discovery_tools/connectors#asset-connectors?setup=risk_center"
              class=""
            >
              Genuity Agent
            </router-link>
            and customize these metrics to best fit your company.
          </span>
        </div>
      </div>
      <!-- E<PERSON> Blank state when agent not installed -->
      
      <div class="col-md-3 mb-3">
        <span
          v-if="!hasAgentInstalled"
          class="position-absolute b-0 pt-2 mt-0.5"
        >
          <i class="icon genuicon-refresh smallest text-muted" />
          <span class="small text-secondary">Agent not installed</span>
        </span>
        <div class="donut-wrapper mb-5 mt-4 text-center">
          <div class="inner-donut-data text-center">
            <div>
              <div class="h5">
                {{ innerTextHeader }}
              </div>
              <div class="small text-secondary">
                {{ chartInnerText }}
              </div>
            </div>
          </div>
          <donut-chart
            v-if="chartDataPresent"
            :chart-data="chartData"
            :custom-opts="{ legend: { display: false } }"
            donut-height="9rem"
          />
          <donut-chart
            v-else
            :chart-data="noDataPresent"
            :custom-opts="{ legend: { display: false } }"
            :show-spend-breakdown="false"
            :show-tooltips="false"
            donut-height="180px"
          />
        </div>
      </div>
      <div class="col-md-9 mb-4">
        <div class="row">
          <div
            v-for="(group, index) in riskItemSummaries"
            :key="group.name"
            class="col-md-6 not-as-small mb-5"
          >
            <!-- Blank state -->
            <span
              v-if="!group.enabled && hasAgentInstalled"
              class="box--disabled-overlay"
            />
            <span
              class="w-100 text-center risk-group"
            >
              <button
                v-if="!group.enabled && hasAgentInstalled"
                class="text-themed-link d-inline-block bg-primary cursor-pointer rounded-pill btn btn-xs bg-purple-subtle text-secondary px-2 ml-n0.5 has-tooltip z-index-1"
                @click.prevent="openCustomizeDataModal(index)"
              >
                <span class="icon nulodgicon-stats-bars mr-1 ml-0 mb-0 align-middle" />
                <span class="smallest">Setup {{ group.name }} Metric</span>
              </button>
            </span>
            <!-- End Blank state -->

            <h6 class="d-flex align-items-center mb-0.5">
              <span
                v-if="group.iconClass"
                class="icon h5 mr-2 mb-0"
                :class="group.iconClass"
              />
              {{ group.name }}
            </h6>
            <div
              v-for="(item, itemIndex) in getGroupItems(group)"
              :key="item.name"
              class="category-label px-2"
              @click="toggleButtonVisibility(item.name)"
            >
              <span
                class="custom-legend"
                :style="{ backgroundColor: getColor(itemIndex) || 'bg-themed-muted'}"
              />
              <span>{{ item.name }}</span>
              <div
                class="float-right"
                :data-tc-view-count="item.name"
              >
                <button
                  v-if="item.count > 0 && buttonVisibility[item.name]"
                  class="btn btn-xs btn-primary py-0"
                  @click="goToRiskCenter(item.name, group.name)"
                >
                  See {{ item.count }} {{ pluralize("asset", item.count) }}
                </button>
                <strong v-else >{{ item.count }}</strong>
              </div>
            </div>
            <Teleport to="body">
              <sweet-modal
                ref="customizeDataModal"
                v-sweet-esc
                modal-theme="right"
                :width="'60%'"
              >
                <template slot="title">
                  <div class="d-flex align-items-center h-100">
                    <h4 class="mb-0">
                      <span
                        v-if="group.iconClass"
                        class="h5 mr-0.5 pr-1 pl-2 mb-0 ml-0 pt-1 mt-0.5"
                        :class="group.iconClass"
                      /> 
                      {{ group.name }}
                    </h4>
                  </div>
                </template>
                <CustomizeMetricModal
                  :data="group"
                  :type="group.name"
                  @widget-updated="handleGroupUpdated"
                  @close-modal="closeCustomizeDataModal(index)"
                />
              </sweet-modal>
            </Teleport>
          </div>
        </div>
      </div>
    
    </div>
  </div>
</template>

<script>
import dates from 'mixins/dates';
import math from 'mixins/math';
import colorCalcs from 'mixins/color_calculations';
import DonutChart from 'components/shared/donut_chart.vue';
import { SweetModal } from 'sweet-modal-vue';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { colorWheel, riskCenter360CardColors } from 'common/chart_colors';
import pluralize from 'pluralize/pluralize';
import permissionsHelper from 'mixins/permissions_helper';
import mockRiskCenterData from '../../mixins/mock_risk_center_data';
import assetRiskCenter from '../../mixins/asset_risk_center';
import CustomizeMetricModal from '../shared/customize_metric.vue';

let i = 0;
while (i < 10) {
  const first = colorWheel.shift();
  colorWheel.push(first);
  i += 1;
}

export default {
  components: { DonutChart, CustomizeMetricModal, SweetModal },
  mixins: [ math, dates, colorCalcs, permissionsHelper, assetRiskCenter, mockRiskCenterData],
  props: {
    isUsedForMsp: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      assetTypeColors: colorWheel,
      activeAssetType: null,
      activeAssetTypeIndex: null,
      assetTypeDataCount: null,
      buttonVisibility: {},
    };
  },

  computed: {
    ...mapGetters([
      'assetTypes',
      'assetTypeData',
      'reloadTypes',
      'currentLocation',
      'riskItemSummaries',
      'hasAgentInstalled',
    ]),

    chartData() {
      const { percentArr, labelsArr, colorArr } = this.processDataSet();
      return {
        datasets: [
          {
            backgroundColor: colorArr,
            data: percentArr,
          },
        ],
        labels: labelsArr,
      };
    },
    noDataPresent() {
      return {
        datasets: [
          {
            backgroundColor: "#f8f9fa",
            data: [1], // Leaving as 1 so chart shows up, but hiding tooltip.
          },
        ],
      };
    },
    readytoLoad() {
      return this.assetTypeData && this.assetTypes && this.loadAssetTypeDataCount;
    },
    loadAssetTypeDataCount() {
      this.updateAssetTypeDataCount();
      return this.assetTypeDataCount;
    },
    riskCenterChartCustomColors() {
      return riskCenter360CardColors;
    },
  },
  methods: {
    ...mapMutations(['setAssetType']),

    ...mapActions([
      'getRiskCenterSummaries',
      'fectchAgentStatus',
    ]),

    onWorkspaceChange() {
      this.getRiskCenterSummaries();
      this.fectchAgentStatus();
    },
    handleGroupUpdated() {
      this.getRiskCenterSummaries();
    },
    getColor(index) {
      return this.riskCenterChartCustomColors[index];
    },
    goToRiskCenter(option, group) {
      const queryParams = { option , group };
      if (this.currentLocation) {
        queryParams.location_id = this.currentLocation.id;
      }
      this.$router.push({
        path: '/risk_center',
        query: queryParams,
      });
    },
    openCustomizeDataModal(index) {
      this.$refs.customizeDataModal[index].open();
    },
    closeCustomizeDataModal(index) {
      this.$refs.customizeDataModal[index].close();
    },
    updateAssetTypeDataCount() {
      if (this.assetTypeData && this.reloadTypes) {
        this.assetTypeDataCount = this.assetTypeData;
        this.activeAssetType = null;
      }
    },
    pluralize(str, count) {
      return pluralize(str, count);
    },
    isActive(name) {
      return this.activeAssetType && this.activeAssetType.name === name ? 'is-active' : '';
    },
    filteredAssetTypes(index) {
      let colSize = Math.ceil(this.assetTypes.length / 3);
      if (colSize > 17)
        colSize = 17;

      const start = index * colSize;
      const end = (index + 1) * colSize;
      return this.assetTypes.slice(start, end);
    },
    updateActiveChartType(type) {
      if (this.activeAssetType === type) {
        this.activeAssetType = null;
        this.filterType(null);
      } else {
        this.activeAssetType = type;
        this.filterType(type.id);
      }
    },
    assetTypeCount(type) {
      let ret = 0;
      if (type === null) {
        this.assetTypeDataCount.forEach((data) => {
          ret += data.count;
        });
      } else {
        this.assetTypeDataCount.forEach((data) => {
          if (data.name === type.name) {
            ret = data.count;
          }
        });
      }
      return ret;
    },
    updateAssetTypeCounts() {
      this.assetTypes.forEach((type) => {
        const updatedType = { ...type };
        updatedType.count = this.assetTypeCount(type);
      });
    },
    filterType(assetTypeId) {
      if (assetTypeId) {
        this.assetTypes.forEach((type) => {
          if (type.id === assetTypeId) {
            this.setAssetType(type);
          }
        });
      } else {
        this.setAssetType(null);
      }

      this.$emit("type-updated");
    },
    getGroupItems(group) {
      if (group.enabled) {
        return group.items;
      }
      const mockData = mockRiskCenterData.data().dashboardFullData;
      return mockData.find(g => g.group === group.name).items;
    },
    toggleButtonVisibility(itemName) {
      if (this.buttonVisibility[itemName]) {
        this.$delete(this.buttonVisibility, itemName);
        return;
      }
      this.buttonVisibility = { [itemName]: true };
    },
  },
};
</script>

<style lang="scss" scoped>
  .category-label {
    line-height: 2rem;
  }

  .box--disabled-overlay {
    height: calc(100% - 2rem);
    top: 2rem;
  }

  .z-index-1 {
    z-index: 1;
  }

  .risk-group {
    z-index: 1;
    position: absolute;
    top: 48%;
  }

  :deep(.sweet-title) {
    background-color: $themed-dark-drawer-bg;
    color: white;
  }

  :deep(.sweet-action-close) {
    color: white !important;
  }

  :deep(.sweet-content-content) {
    width: 100%;
  }
</style>
