require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::MsTeams::ConfigsController, type: :controller do
  create_company_and_user

  let(:config) { create(:ms_teams_config, company: company, workspace_id: workspace.id) }
  let(:config2) { create(:ms_teams_config, company: company, active: false, channel_id: 'xYahawer', workspace_id: workspace.id) }
  let(:user2) { create(:user) }

  describe '#create' do
    context 'with a logged in user' do
      it 'will not create config without teams info' do
        params = { subdomain: company.subdomain, current_user: user.email, privilege_name: 'HelpTicket' }
        post :create, params: params
        expect(company.ms_teams_configs.count).to eq(0)
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('An error occured while linking your channel.')
      end

      it 'will not create new config because it already exists' do
        params = { subdomain: company.subdomain,
                  current_user: user.email,
                  privilege_name: 'HelpTicket',
                  team: { id: config.team_id, name: config.team_name },
                  channel: { id: config.channel_id, name: config.channel_name }
                }
        post :create, params: params

        expect(company.ms_teams_configs.count).to eq(1)
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Sorry, this channel is already linked with another company.')
      end

      it 'will create new config' do
        params = { subdomain: company.subdomain,
                  current_user: user.email,
                  team: { id: '0cfd462a-4af0-4bd0-9bca-e3020d6c1cf8', name: 'Help desk alerts' },
                  channel: { id: '19:b03c397dfa60487ebe62e51244069441@thread.tacv2', name: 'helpdesk' },
                  workspace_id: workspace.id
                }
        post :create, params: params

        expect(company.ms_teams_configs.count).to eq(1)
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['subdomain']).to eq(company.subdomain)
      end
    end
  end

  describe '#show' do
    it 'will return config if exists' do
      params = { current_user: user.email,
                 team: { id: config.team_id, name: config.team_name },
                 channel: { id: config.channel_id, name: config.channel_name }
               }
      get :show, params: params

      expect(company.ms_teams_configs.count).to eq(1)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['subdomain']).to eq(company.subdomain)
    end
  end

  describe '#destroy' do
    it 'will not destroy config because config doesn\'t exist' do
      params = { current_user: user.email,
                 team: { id: '19:b03c397dfa60487ebe62e51244069441@thread.tacv2' },
                 channel: { id: '0cfd462a-4af0-4bd0-9bca-e3020d6c1cf8' },
                 id: 0
               }
      delete :destroy, params: params

      expect(response).to have_http_status(:not_found)
    end

    it 'will destroy config if exists' do
      params = { current_user: user.email,
                 team: { id: config.team_id, name: config.team_name },
                 channel: { id: config.channel_id, name: config.channel_name },
                 id: config.id
               }
      delete :destroy, params: params

      expect(company.ms_teams_configs.count).to eq(0)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['message']).to eq('This channel is successfully unlinked.')
    end
  end

  describe "#update" do
    it 'will deactivate the active integrated channel of Microsoft Teams' do
      params = { 'id' => config.id, 'is_bot_installed' => true }
      put :update, params: params

      config.reload
      expect(config.active).to eq(false)
    end

    it 'will activate the deactive integrated channel of Microsoft Teams' do
      params = { 'id' => config2.id, 'is_bot_installed' => true }
      put :update, params: params

      config2.reload
      expect(config2.active).to eq(true)
    end
  end
end
