ActiveAdmin.register_page "SES Suppression List" do
  content title: "SES Suppression List" do
    render "admin/suppression_list/show"
  end

  page_action :search, method: :post do
    search_email = params[:search_email]

    if search_email.present?
      begin
        client = Aws::SESV2::Client.new(
          region: Rails.application.credentials.aws[:region],
          access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
          secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
        )

        client.get_suppressed_destination(email_address: search_email)
        flash[:success] = "The email address #{search_email} exist."
      rescue StandardError => e
        search_email = nil
        flash[:error] = "#{e.message}"
      end
    else
      flash[:error] = "Please provide a valid email address for the search."
    end

    redirect_to admin_ses_suppression_list_path(search_email: search_email)
  end

  page_action :delete, method: :post do
    search_email = params[:search_email]

    if search_email.present?
      begin
        client = Aws::SESV2::Client.new(
          region: Rails.application.credentials.aws[:region],
          access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
          secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
        )

        client.delete_suppressed_destination(email_address: search_email)
        flash[:success] = "The email address #{search_email} has been removed from the suppression list."
      rescue Aws::SESV2::Errors::NotFoundException
        flash[:error] = "The email address #{search_email} is not found in the suppression list."
      rescue StandardError => e
        flash[:error] = "#{e.message}"
      end
    else
      flash[:error] = "Please provide a valid email address."
    end

    redirect_to admin_ses_suppression_list_path(search_email: nil)
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
