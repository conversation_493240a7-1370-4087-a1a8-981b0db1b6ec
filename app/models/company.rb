class Company < ActiveRecord::Base
  include PgTools
  include States
  include Validations
  include Logos
  include CompanyIntegrations
  include MspTemplates
  include UniquelyIdentified
  include HandleCompanyCacheKeys
  include FormIconAndColor
  include CompanyCache
  include DefaultAssetsAtCreation
  extend HandleCompanyCacheKeys

  has_many  :workspaces, dependent: :destroy
  has_many  :system_users, class_name: 'SystemUser', dependent: :destroy
  has_many  :company_users, class_name: 'CompanyMember', dependent: :destroy
  has_one   :email_format, dependent: :destroy
  has_many  :helpdesk_custom_emails, dependent: :destroy
  has_many  :email_templates, dependent: :destroy
  has_many  :valid_email_extensions, dependent: :destroy
  has_many  :helpdesk_email_formats, dependent: :destroy

  belongs_to :reseller_company, class_name: "Company", foreign_key: "reseller_company_id", optional: true

  has_many  :users,             through: :company_users
  has_many  :actionable_alerts, dependent: :destroy
  has_many :user_pinned_companies, dependent: :destroy
  has_many :users_pinning, through: :user_pinned_companies, source: :user
  accepts_nested_attributes_for :users, allow_destroy: true
  accepts_nested_attributes_for :company_users, allow_destroy: true

  has_many  :locations,         dependent: :destroy
  has_many  :contracts,         dependent: :destroy
  has_many  :managed_assets,    dependent: :destroy
  has_many  :help_tickets,      dependent: :destroy
  has_many  :telecom_services,  dependent: :destroy
  has_many  :invoices,          dependent: :destroy
  has_many  :phone_numbers,     dependent: :destroy
  has_many  :ip_addresses,      dependent: :destroy
  has_many  :discovered_assets, dependent: :destroy
  has_many  :discovered_users,  dependent: :destroy
  has_many  :discovered_user_histories, dependent: :destroy
  has_many  :discovered_asset_histories, dependent: :destroy
  has_many  :discovered_vendors,  dependent: :destroy
  has_many  :vendors,           dependent: :destroy
  has_many  :telecom_providers, dependent: :destroy
  has_many  :helpdesk_settings, dependent: :destroy
  has_many  :time_spents,       through: :help_tickets
  has_many  :discovered_data_services,           dependent: :destroy
  has_many  :discovered_voice_services, dependent: :destroy
  has_many  :plaid_accounts, dependent: :destroy
  has_many  :general_transactions, dependent: :destroy
  has_many  :cloud_usage_transactions, dependent: :destroy
  has_many  :predicted_cloud_transactions, dependent: :destroy
  has_many  :ticket_emails, dependent: :destroy
  has_many  :blocked_entities, dependent: :destroy
  has_many  :general_transaction_tags, dependent: :destroy
  has_many  :module_alerts, dependent: :destroy
  has_many  :vendor_spend_alerts, dependent: :destroy
  has_many  :module_alert_notifications, dependent: :destroy
  has_many  :module_alert_contributors, dependent: :destroy
  has_many  :probe_locations, dependent: :destroy
  has_many  :agent_locations, dependent: :destroy
  has_many  :company_trackings, dependent: :destroy
  has_many  :managed_asset_tags, dependent: :destroy
  has_many  :contract_tags, dependent: :destroy
  has_many  :transaction_products_mapping, dependent: :destroy
  has_many  :apps, class_name: 'Integrations::App', dependent: :destroy
  has_many  :api_events, dependent: :destroy, class_name: 'Logs::ApiEvent'
  has_many :depreciations, dependent: :destroy
  has_many :costs, through: :managed_assets
  has_many :asset_types, class_name: "CompanyAssetType", dependent: :destroy
  has_many :asset_statuses, class_name: 'CompanyAssetStatus', dependent: :destroy
  has_many :asset_tags, class_name: 'CompanyAssetTag', dependent: :destroy
  has_many :asset_lifecycles, dependent: :destroy
  has_many :asset_discovery_logs, dependent: :destroy, class_name: 'Logs::AssetDiscoveryLog'
  has_many :snippets, dependent: :destroy
  has_many :tasks, dependent: :destroy
  has_many :task_checklists, dependent: :destroy
  has_many :articles, dependent: :destroy
  has_many :article_tags, dependent: :destroy
  has_many :groups, dependent: :destroy
  has_many :contributors, dependent: :destroy
  has_many :linkables, dependent: :destroy
  has_many :project_tasks, dependent: :destroy
  has_many :automated_tasks, class_name: "AutomatedTasks::AutomatedTask", dependent: :destroy
  has_many :assets_automated_tasks, class_name: "Assets::AutomatedTask", dependent: :destroy
  has_many :execution_logs, class_name: "AutomatedTasks::ExecutionLog", dependent: :destroy
  has_many :execution_date, class_name: "AutomatedTasks::ExecutionDate", dependent: :destroy
  has_many :telecom_service_price_changes
  has_many :ticket_email_logs, dependent: :destroy, class_name: "Logs::TicketEmailLog"
  has_many :attachment_uploads, dependent: :destroy
  has_many :custom_form_values, dependent: :destroy
  has_many :custom_forms, dependent: :destroy
  has_many :custom_form_attachments, dependent: :destroy
  has_many :custom_reports, dependent: :destroy
  has_many :it_reports, dependent: :destroy
  has_one :it_report_preference, dependent: :destroy
  has_many :msp_asset_types, class_name: "Msp::Templates::AssetType", dependent: :destroy
  has_many :company_mailers, dependent: :destroy
  has_many :guests, dependent: :destroy
  has_many :parent_invitations, class_name: "CompanyInvitation", foreign_key: "parent_company_id", dependent: :destroy
  has_many :child_invitations, class_name: "CompanyInvitation", foreign_key: "child_company_id", dependent: :destroy
  has_many :parent_association_logs, class_name: "CompanyAssociationEventLog", foreign_key: "parent_company_id", dependent: :destroy
  has_many :child_association_logs, class_name: "CompanyAssociationEventLog", foreign_key: "child_company_id", dependent: :destroy
  has_many :company_builds, dependent: :destroy
  has_many :help_ticket_drafts, dependent: :destroy
  has_many :automated_task_groups, dependent: :destroy

  has_one :alert_default, dependent: :destroy
  has_one :company_credit_card, dependent: :destroy
  has_many :subscriptions, dependent: :destroy
  has_many :subscription_activities, dependent: :destroy
  has_many :stripe_transactions, dependent: :destroy
  has_one :asset_preference, dependent: :destroy
  has_one :discovered_asset_preference, dependent: :destroy
  has_many :business_hours, dependent: :destroy
  has_many :ticket_list_columns, dependent: :destroy
  has_many :ticket_kanban_fields, dependent: :destroy
  has_one :mfa_setting, dependent: :destroy
  has_many :probe_configs, dependent: :destroy
  has_one :nmap_option, dependent: :destroy
  has_one :apps_logs_option, dependent: :destroy
  has_many :products, dependent: :destroy
  has_many :categories, -> { where(module_type: 'company') }, class_name: "Category", dependent: :destroy
  has_many :helpdesk_categories, -> { where(module_type: 'helpdesk') }, class_name: "Category", dependent: :destroy
  has_many :departments, dependent: :destroy
  has_many :expanded_privileges, dependent: :destroy
  has_one :contract_preference, dependent: :destroy
  has_one :custom_domain, dependent: :destroy
  has_one :help_center_logo, dependent: :destroy
  has_many :quick_view_filters, dependent: :destroy
  has_many :scheduled_tasks, dependent: :destroy
  has_many :company_domains, dependent: :destroy

  has_many :library_documents, dependent: :destroy
  has_many :helpdesk_faqs, dependent: :destroy
  has_many :customer_companies, class_name: "Company", foreign_key: "reseller_company_id", dependent: :nullify
  has_many :discovered_asset_types, dependent: :destroy
  has_many :email_logs, dependent: :destroy, class_name: 'Logs::EmailLog'
  has_many :event_logs, dependent: :destroy
  has_many :msp_event_logs, dependent: :destroy
  has_many :company_cache_keys, dependent: :destroy
  has_many :policies, class_name: "Sla::Policy", dependent: :destroy
  has_many :integrated_vendors, dependent: :destroy
  has_many :xls_imports, foreign_key: "company_id", dependent: :destroy
  has_many :custom_surveys, dependent: :destroy
  has_many :responses, class_name: 'CustomSurvey::Response', dependent: :destroy
  has_many :reports, dependent: :destroy
  has_many :asset_connector_logs, dependent: :destroy
  has_many :ai_usage_costs, dependent: :destroy

  has_many :admin_overview_customizations, dependent: :destroy
  has_one :temporary_company_subscription, dependent: :destroy

  has_many :asset_risk_center_widgets, dependent: :destroy
  has_many :risk_center_widget_options, dependent: :destroy

  after_create :create_system_user_for_company
  after_create :create_alert_default
  after_create :create_sample_depreciations
  after_create :create_default_preference
  after_create :create_default_discovered_asset_preference
  after_create :create_nmap_option
  after_create :create_apps_logs_option
  after_create :create_default_business_hour
  after_create :create_default_groups
  after_create :create_default_location_custom_base_form, unless: Proc.new { |c| c.is_sample_company? }
  after_create :create_staff_custom_forms, unless: Proc.new { |c| c.is_sample_company? }
  after_create :create_company_departments
  after_create :create_default_risk_center_widgets
  after_create -> { delete_options_cache_keys('company_options', nil, true) }, if: -> { self.reseller_company_id.present? }
  after_create :create_company_domain
  after_create :create_default_assets_automated_tasks

  after_commit :update_monitoring_remote_company, on: :update, if: :valid_to_sync?
  after_update -> { delete_options_cache_keys('company_options', nil, true) }, if: -> { self.saved_changes[:reseller_company_id] }
  after_save -> { delete_options_cache_keys('show_company_user') }
  after_save -> { delete_options_cache_keys('company_options') }
  after_save -> { delete_options_cache_keys(nil, 'general_company_options') }
  after_save :delete_company_cache_keys

  after_commit :create_monitoring_remote_company, on: :create
  after_commit :create_categories, on: :create, unless: Proc.new { |c| c.is_sample_company? }
  after_commit :create_company_asset_types, on: :create, unless: Proc.new { |c| c.is_sample_company? }
  after_commit :create_company_asset_statuses, on: :create, unless: Proc.new { |c| c.is_sample_company? }
  after_commit :create_company_asset_tags, on: :create, unless: Proc.new { |c| c.is_sample_company? }
  after_destroy :delete_monitoring_remote_company
  after_destroy -> { delete_options_cache_keys('company_options') }
  after_destroy -> { delete_options_cache_keys(nil, 'general_company_options') }
  after_destroy -> { delete_options_cache_keys('company_options', nil, true) }, if: -> { self.reseller_company_id.present? }
  after_destroy :delete_company_cache_keys

  PRTG_USER = "nutelx"
  PRTG_PASSHASH = 3840034204
  RESERVE_WORDS = ["www", "nutelx", "secure", "api", "staging", "genuity", "nulodgix", "localhost", "try", "docs"]
  MONITORING_COMMON_ATTRIBUTES = ['guid', 'name', 'subdomain', 'original_logo_url', 'default_logo_url', 'free_trial_days', 'created_at', 'timezone']

  accepts_nested_attributes_for :locations, reject_if: :all_blank
  validates_associated :locations, on: :create

  validates :name, presence: true, length: { minimum: 3 }
  validates :subdomain, presence: true, uniqueness: { case_sensitive: false }
  validates :default_logo_url, presence: true
  validate :subdomain_validator
  validates :subdomain, exclusion: { in: %w(public), message: "%{value} is reserved." }
  validate :phone_number_format_validation
  validate :url_format_validation
  validate :email_format_validation
  validates_with ::DomainValidator
  validates_uniqueness_of :name, message: 'already exists' , case_sensitive: false 
  validate :validate_company_linkage

  before_validation :create_subdomain_from_name
  before_validation :downcase_subdomain
  before_validation :format_number

  attr_accessor :logo_url, :logo_image_data, :ip
  before_save :decode_and_attach_base64_image
  before_save :delete_subscription_cache_keys, if: -> { free_trial_days_changed? }

  has_one_attached :logo, service: Rails.application.credentials.active_storage_public_service.to_sym do |attachable|
    attachable.variant :thumb, resize_to_limit: [100, 100]
    attachable.variant :medium, resize_to_limit: [640, 640]
  end
  validates :logo, content_type: ['image/png', 'image/jpeg'],
                   size: { less_than: 5.megabytes, message: 'File size is too large' }

  scope :sample_companies, -> { where(is_sample_company: true) }
  scope :not_sample, -> { where(is_sample_company: false) }
  scope :with_subscription, -> { includes(:subscriptions).joins(:subscriptions).where('subscriptions.company_id = companies.id') }
  scope :resellers, -> {where(is_reseller_company: true)}
  scope :not_resellers, -> {where(is_reseller_company: false)}
  scope :active_companies, -> { left_outer_joins(:subscriptions).where("subscriptions.company_id = companies.id AND subscriptions.end_date IS NULL OR (CAST (TO_CHAR(companies.created_at, 'J') AS INTEGER) + free_trial_days) - CAST(TO_CHAR(now(), 'J') AS INTEGER) > 0") }

  def as_json(options)
    my_hash = super(options)
    if self.logo.attached?
      my_hash['logo_url'] = self.logo.variant(:medium)&.processed&.url.to_s
      my_hash['logo_file_name'] = self.logo.filename.to_s
    elsif self.logo_url
      my_hash['logo_url'] = self.logo_url
    end
    my_hash['subscriptions'] = self.subscriptions
    my_hash['domain'] = Rails.application.credentials.root_domain
    my_hash
  end

  def self.find_by_cache(params = nil)
    return find_by(params) unless self.is_cache_enabled?('company_find_by')

    params = params.transform_values do |value|
      return find_by(params) if value.is_a?(Array) && value.many?
      
      value.is_a?(Array) ? value.first.to_s : value&.to_s
    end

    cache_key = "company_find_by_#{params}".gsub(/[\\"]/, "'")  
  
    if [:id, :guid, :subdomain, :stripe_id, :is_sample_company].include?(params.keys.first) 
      Rails.cache.fetch(cache_key, expires_in: 24.hours, skip_nil: true) do
        find_by(params)
      end
    else
      find_by(params)
    end
  end

  def self.find_cache(params = nil)
    find_by_cache(params) || (raise ActiveRecord::RecordNotFound, "Company not found with ID: #{params.values.first}")
  end

  def validate_company_linkage
    if self.changes[:reseller_company_id]
      parent_company = Company.find_cache(id: self.reseller_company_id)
      if [parent_company.subdomain, self.subdomain].include?('sample')
        errors.add('Sample company', 'cannot be linked with any company.')
      end
    end
  end

  def self.current_id=(id)
    Thread.current[:company_id] = id
  end

  def self.current_id
    Thread.current[:company_id]
  end

  def change_search_path
    restore_default_search_path
  end

  def get_prtg_username
    prtg_credentials[:username]
  end

  def get_prtg_passhash
    prtg_credentials[:password]
  end

  def prtg_credentials
    if self.prtg_passhash.present? && self.prtg_username.present?
      {username: self.prtg_username, password: self.prtg_passhash}
    else
      {username: nil, password: nil}
    end
  end

  def create_schema
    if self.subdomain != DEFAULT_SUB_DOMAIN
      result = ActiveRecord::Base.connection.execute("create schema #{self.name}")
      # ActiveRecord::Base.connection.execute("drop extension hstore cascade")
      # ActiveRecord::Base.connection.execute("create extension hstore")

      scope_schema do
        load Rails.root.join("db/schema.rb")
        ActiveRecord::Base.connection.execute("drop table #{self.class.table_name}")
      end
    end
  end

  def scope_schema(*paths)
    if self.subdomain != DEFAULT_SUB_DOMAIN
      begin
        original_search_path = self.class.connection.schema_search_path
        self.class.connection.schema_search_path = ["#{self.name}", *paths].join(",")
        yield
      ensure
        self.class.connection.schema_search_path = original_search_path
      end
    else
      yield
    end
  end

  def hostname
    "#{subdomain}.#{ Rails.application.credentials.root_domain}"
  end

  def default_email
    "helpdesk@#{self.hostname}"
  end

  def self.is_subdomain_unique?(subdomain)
    !Company.exists?(['lower(subdomain) = ?', subdomain.downcase])
  end

  def self.is_reserved? domain_name
    Company::RESERVE_WORDS.include? domain_name.downcase
  end

  def nuke_it!
    ActiveRecord::Base.connection_pool.with_connection do
      ActiveRecord::Base.transaction do
        if self.present?
          raise "This company still has access and can't be deleted." if self.allow_access?

          remove_access_from_sample_company

          self.company_users&.update_all(contributor_id: nil,
                                         custom_form_id: nil)

          Privilege.where(contributor: self.contributors).destroy_all
          destroy_company_contributors
          self.groups.default.update_all(default: false)
          self.groups.destroy_all
          self.groups.reload
          self.nmap_option&.destroy!

          sql = "delete from alert_defaults where company_id = ?"
          query = ActiveRecord::Base.send(:sanitize_sql_array, [sql, [ id ]])
          ActiveRecord::Base.connection.execute(query)

          sql = "delete from apps_logs_options where company_id = ?"
          query = ActiveRecord::Base.send(:sanitize_sql_array, [sql, [ id ]])
          ActiveRecord::Base.connection.execute(query)

          sql = "delete from event_logs where company_id = ?"
          query = ActiveRecord::Base.send(:sanitize_sql_array, [sql, [ id ]])
          ActiveRecord::Base.connection.execute(query)

          self.execution_date.destroy_all
          self.company_mailers.destroy_all
          EmailFormat.where(company_id: self.id).destroy_all

          nuke_company_data!

          destroy_company_users
          destroy_system_users
          destroy_custom_forms
        end
        self.destroy
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def remove_access_from_sample_company
    company_users = self.admin_company_users
    if company_users.present?
      sample_company = Company.find_by_cache(is_sample_company: true)
      return unless sample_company
      company_users.each do |company_user|
        user = company_user.user
        c_ids = [sample_company.id, self.id]
        user_count = user.company_users.where.not(company_id: c_ids).count

        if user_count == 0
          sample_company_user = CompanyUser.find_by_cache(user_id: user.id, company_id: sample_company.id)
          if sample_company_user.present?
            admin_group_member = sample_company.groups.find_by_name("Admins").group_members.find_by(contributor_id: sample_company_user.contributor_id)
            if admin_group_member.present?
              admin_group_member.skip_callbacks = true
              admin_group_member.destroy!
            end
          end
          sample_company_user.destroy! if sample_company_user.present?
        end
      end
    end
  end

  def destroy_system_users
    self.system_users.find_each do |su|
      su.skip_callbacks = true
      su.destroy!
    end
  end
  
  def destroy_company_users
    self.company_users.find_each do |cu|
      cu.skip_callbacks = true
      if cu.destroy!
        cu.user.destroy! if cu.user.company_users.not_sample.count == 0
      end
    end
  end

  def destroy_custom_forms
    self.custom_forms.each do |cf|
      cf.skip_callbacks = true
      cf.destroy
    end
  end

  def destroy_company_contributors
    self.contributors.find_each do |contributor|
      contributor.group_members.find_each do |gm|
        gm.skip_field_permissions = true
        gm.destroy!
      end
      contributor.destroy!
    end
  end

  def nuke_company_data!
    if self.present?
      self.discovered_assets.destroy_all if self.discovered_assets.present?
      AssetSoftware.where(managed_asset_id: self.managed_assets.ids).destroy_all

      self.workspaces.destroy_all if self.workspaces.present?
      self.telecom_services.destroy_all if self.telecom_services.present?
      self.contracts.collect(&:contract_attachments).map(&:destroy) if self.contracts.present?
      self.contracts.destroy_all if self.contracts.present?
      self.invoices.destroy_all if self.invoices.present?
      self.general_transaction_tags.destroy_all if self.general_transaction_tags.present?
      self.general_transactions.in_batches(of: 1000).destroy_all if self.general_transactions.present?
      self.products.destroy_all
      self.categories.destroy_all if self.categories.present?

      self.managed_assets.each do |ma|
        ma.assignment_information&.destroy!
        ma.asset_softwares.destroy_all
        ma.destroy!
      end
      self.depreciations.destroy_all

      # For some reason, this fails when we use the association  :(
      DiscoveredVoiceService.where(company_id: self.id).destroy_all
      DiscoveredDataService.where(company_id: self.id).destroy_all
      self.ip_addresses.destroy_all if self.ip_addresses.present?
      self.phone_numbers.destroy_all if self.phone_numbers.present?
      self.plaid_accounts.destroy_all if self.plaid_accounts.present?
      self.probe_locations.destroy_all if self.probe_locations.present?

      self.locations.each do |location|
        location.telecom_service_locations.destroy_all
        location.contract_locations.destroy_all
      end

      self.cloud_usage_transactions.destroy_all if self.cloud_usage_transactions.present?
      self.telecom_providers.destroy_all if self.telecom_providers.present?
      self.vendors.destroy_all if self.vendors.present?
      self.discovered_users.destroy_all if self.discovered_users.present?
      self.locations.destroy_all if self.locations.present?
      self.subscriptions.destroy_all
      self.asset_preference&.destroy! if self.asset_preference.present?
      self.discovered_asset_preference&.destroy! if self.discovered_asset_preference.present?
      self.ticket_list_columns.destroy_all if self.ticket_list_columns.present?
      self.ticket_kanban_fields.destroy_all if self.ticket_kanban_fields.present?
      self.company_credit_card&.destroy!
      self.module_alerts.destroy_all
      self.integrations_apps.destroy_all
      self.integrations_users.destroy_all
      self.company_integrations&.destroy_all
      self.article_tags.destroy_all
      self.articles.destroy_all
    end
  end

  def has_active_subscription?
    key = [name: 'check_active_subscription', company_id: self.id]
    Rails.cache.fetch(key, :expires_in => 8.hours) do
      subscriptions.present? && subscriptions.where(status: 'active').any?
    end
  end

  def has_expiring_subscription?
    key = [name: 'check_expiring_subscription', company_id: self.id]
    Rails.cache.fetch(key, :expires_in => 8.hours) do
      subscriptions.present? && subscriptions.any?(&:expiring?)
    end
  end

  def has_expired_subscription?
    subscriptions.present? && subscriptions.any?(&:expired?)
  end

  def has_insolvent_subscription?
    subscriptions.present? && subscriptions.where(status: 'insolvent').any?
  end

  def has_paid_subscription?
    has_active_subscription? || has_expiring_subscription?
  end

  def has_current_free_trial?
    free_trial_days_left > 0
  end

  def subscription_active_for_module?(module_type)
    subs = subscriptions.where(status: ["active", "canceled"]).where("end_date IS NULL OR end_date >= ?", Date.today)
    return (free_trial_company? || subs.present?) && show_free_modules? if module_type == "TelecomService"
  
    free_trial_company? || subs.any? { |sub| sub.module_type == module_type || sub.module_type == 'full_platform' }
  end  

  def free_trial_company?
    has_current_free_trial? && subscriptions.empty?
  end
  
  def is_expired_company?                                           
    !has_current_free_trial? && subscriptions.empty?
  end

  def expired_since_30_days?
    return false if subscriptions.present?
    days_since_expired = Date.today.mjd - (created_at.to_date + free_trial_days.days).mjd
    days_since_expired >= 30
  end

  def allow_access? request_path = nil
    paid_modules = ['managed_assets', 'help_tickets', 'vendors', 'contracts', 'reports', 'state_of_it']
    return is_subscribed_module(request_path) if request_path && !request_path.include?('company_user') && paid_modules.find{ |mod| request_path.include?(mod) }

    has_paid_subscription? ||
    has_current_free_trial?
  end

  def is_subscribed_module(request_path)
    paid_modules_hash = {
      'asset_management' => 'managed_assets',
      'it_help_desk' => 'help_tickets',
    }
    required_reporting_modules = ['full_platform', 'it_help_desk', 'vendor_management']
    @subscribed_modules ||= self.subscriptions.where.not(status: 'insolvent')
                                             &.where("end_date IS NULL OR end_date >= ?", Date.today)
                                             &.pluck(:module_type)
    mapped_results = @subscribed_modules.map { |module_type| paid_modules_hash[module_type] }.compact

    return true if @subscribed_modules.include?("full_platform") && request_path.include?('state_of_it')

    return true if request_path.include?('reports') && (@subscribed_modules & required_reporting_modules).any?

    return true if  @subscribed_modules.include?("full_platform") ||
                    mapped_results&.find{ |res| request_path.include?(res) } ||
                    (['vendors', 'contracts'].any? { |mod| request_path.include?(mod) } && @subscribed_modules.include?("vendor_management"))

    false
  end

  def logo_or_default_logo
    self.original_logo_url || self.default_logo_url
  end

  def set_up_self user
    self.email = user.email
    self.timezone = user.timezone.presence
    self.last_logged_in_at = DateTime.current
    self.default_logo_url = "https://s3.amazonaws.com/nulodgic-static-assets/images/location_defaults/default#{rand(1..16)}_thumbnail.png"
    self.save!
    update_business_hour_timezone
    return self
  end

  def run_setup_workers request
    create_app_direct_company if is_second_company?
    DiscoveredVoiceServiceWorker.perform_async(self.id, self.phone_number) if self.phone_number.present?
    DiscoveredDataServiceWorker.perform_async(self.id, request.ip)
    FetchLogoWorker.perform_async(self.id)
  end

  def create_company_tracking cookies
    self.company_trackings.create!(campaign: utm_campaign(cookies), source: utm_source(cookies), medium: utm_medium(cookies), gclid: gclid(cookies))
  end

  def create_categories
    if self.categories.empty?
      ActiveRecord::Base.transaction do
        categories = []
        DefaultCategory.all.each do |cat|
          categories << {
            name: cat.name,
            company_id: self.id,
            module_type: 'company',
          }
        end
        Category.insert_all(categories)
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def create_sample_depreciations
    ActiveRecord::Base.transaction do
      self.depreciations.find_or_create_by(useful_life_in_years: 5, depreciation_type: "straight_line", name: "Straight Line, 5 years", description: "Sample")
      self.depreciations.find_or_create_by(useful_life_in_years: 5, depreciation_type: "declining_balance", name: "Declining Balance, 5 years", depreciation_factor: 1.5, description: "Sample")
      self.depreciations.find_or_create_by(useful_life_in_years: 5, depreciation_type: "sum_of_years_digits", name: "Sum of Years Digits, 5 years", description: "Sample")

      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  def add_company_asset_types
    if self.asset_types.empty?
      AssetType.all.each do |default_asset_type|
        self.asset_types << CompanyAssetType.new(default_asset_type.attributes.except("id", "created_at"))
      end
    end
  end

  def create_company_asset_types
    if self.asset_types.empty?
      ActiveRecord::Base.transaction do
        asset_types = []
        AssetType.all.each do |default_asset_type|
          asset_types <<  default_asset_type.attributes.except("id", "created_at")
                                                                .merge('company_id' => self.id)
        end
        CompanyAssetType.insert_all asset_types
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def create_company_asset_statuses
    if self.asset_statuses.empty?
      ActiveRecord::Base.transaction do
        asset_statuses = []
        DefaultAssetStatus.all.each do |status|
          asset_statuses << status.attributes.except('id', 'created_at', 'updated_at')
                                             .merge('company_id' => self.id)
        end
        CompanyAssetStatus.upsert_all(asset_statuses)
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def create_company_asset_tags
    if self.asset_tags.empty?
      ActiveRecord::Base.transaction do
        asset_tags = []
        default_tags = ["team", "essers", "mobile", "inventory", "district funds"]
        default_tags.each do |tag|
          asset_tags << { name: tag, company_id: self.id }
        end
        CompanyAssetTag.upsert_all(asset_tags)
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end

  def create_company_departments
    inactive = ['DevOps', 'Customer Support', 'Legal']
    departments = []
    DefaultDepartment.where.not(name: inactive).find_each do |dept|
      rec = Department.new({ name: dept.name, company_id: self.id })
      departments << rec.attributes.except('id', 'created_at', 'updated_at') if rec.valid?
    end
    Department.insert_all(departments) if departments.present?
  end

  def create_default_risk_center_widgets
    ManagedAssets::RiskCenter::CreateDefaultRiskCenterWidgets.new(self).call
  end

  def create_default_location_custom_base_form(template = nil)
    ActiveRecord::Base.transaction do
      template ||= CustomFormTemplate.find_by(form_name: "Base Location")
      if template.present?
        attrs = template.attributes.except('id', 'created_at', 'updated_at', 'image_url', 'description')
        attrs['default'] = true
        custom_form = self.custom_forms.find_or_initialize_by(form_name: attrs['form_name'])
        custom_form.assign_attributes(attrs)
        if !custom_form.id? && custom_form.save!
          default_workspace
          custom_form.company = self

          position_records = []
          fields = template.custom_form_field_templates.order('order_position ASC')
          field_positions = FieldPositionTemplate.where(custom_form_field_template: fields)
                              .index_by(&:custom_form_field_template_id)

          fields.each_with_index do |field, idx|
            attrs = field.attributes.except('id', 'created_at', 'updated_at', 'custom_form_template_id')
            attrs[:custom_form_id] = custom_form.id
            attrs[:order_position] = idx

            new_field = custom_form.custom_form_fields.create(attrs)

            field_position = field_positions[field.id]
            position_records << { custom_form_field_id: new_field.id, position: field_position.position } if field_position
          end
          FieldPosition.insert_all!(position_records)
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        custom_form
      end
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  def create_staff_custom_forms(form_names = nil)
    form_names ||= ['Teammates', 'Partners', 'Contractors']
    ActiveRecord::Base.transaction do
      CustomFormTemplate.includes(:custom_form_field_templates).where(form_name: form_names).find_each do |template|
        attrs = template.attributes.except('id', 'created_at', 'updated_at', 'image_url')
        attrs['default'] = template.form_name == 'Teammates'
        attrs['icon'] = form_icon(template.form_name)
        attrs['color'] = generate_random_color
        custom_form = self.custom_forms.find_or_initialize_by(attrs)
        if !custom_form.id? && custom_form.save!
          position_records = []
          fields = template.custom_form_field_templates.order('order_position ASC')
          field_positions = FieldPositionTemplate.where(custom_form_field_template: fields)
                              .index_by(&:custom_form_field_template_id)

          fields.each_with_index do |field, idx|
            attrs = field.attributes.except('id', 'created_at', 'updated_at', 'custom_form_template_id')
            attrs[:custom_form_id] = custom_form.id
            attrs[:order_position] = idx
            attrs[:private] = ["people_list", "asset_list", "location_list", "contract_list", "vendor_list", "telecom_list"].include?(attrs["field_attribute_type"])

            new_field = custom_form.custom_form_fields.create(attrs)

            field_position = field_positions[field.id]
            position_records << { custom_form_field_id: new_field.id, position: field_position.position } if field_position
          end
          FieldPosition.insert_all!(position_records)
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        custom_form
      end
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  def free_trial_days_left
    key = [name: 'check_free_trial_days', company_id: self.id]
    Rails.cache.fetch(key, :expires_in => 8.hours) do
      days_left = (created_at.to_date + free_trial_days.days).mjd - Date.today.mjd
      [days_left, 0].max
    end
  end

  def free_trial_expiration_date
    created_at.to_date + free_trial_days.days
  end

  def subscription_expiring_days_left
    return nil if !subscriptions
    return nil if subscriptions.none?(&:expiring?)
    return nil if !subscriptions[0]&.end_date
    days_left = subscriptions[0]&.end_date&.mjd - Date.today.mjd
    [days_left, 0].max
  end

  def create_app_direct_company
    AppDirect::SyncCompanyWorker.perform_async self.id
  end

  def mfa_enabled
    self&.subdomain != "secure" && mfa_setting&.mfa_enabled
  end

  def has_child_companies?
    Company.where(reseller_company_id: self.id).length > 0
  end

  def default_workspace
    if workspaces.blank?
      workspace = self.workspaces.new(name: 'Helpdesk', icon_class: "genuicon-workspace", color_class: "primary", is_first_workspace: true)
      workspace.privileges << Privilege.new(
        name: 'HelpTicket',
        contributor:  self.groups.find_by(name: 'Admins').contributor,
        permission_type: 'write'
      )
      permission = self.subdomain == "sample" ? 'read' : 'basicread'
      workspace.privileges << Privilege.new(
        name: 'HelpTicket',
        contributor:  self.groups.find_by(name: 'Everyone').contributor,
        permission_type: permission
      )
      workspace.privileges << Privilege.new(
        name: 'HelpTicket',
        contributor:  self.groups.find_by(name: 'Help Desk Agents').contributor,
        permission_type: 'write'
      )
      self.workspaces << workspace
    end
    workspaces.order(:created_at).first
  end

  def helpdesk_agents
    groups.find_by(name: 'Help Desk Agents', default: true)
  end

  def admins
    @admins ||= groups.find_by(name: 'Admins', default: true)
  end

  def helpdesk_agent_emails
    helpdesk_agent_users.map(&:email)
  end

  def admin_emails
    admin_users.map(&:email)
  end

  def helpdesk_agent_company_users
    contributor_ids = helpdesk_agents.contributor.contributor_ids_only_users
    CompanyUser.where(contributor_id: contributor_ids)
  end

  def admin_company_users
    if admins.present?
      contributor_ids = admins.contributor.contributor_ids_only_users
      CompanyUser.where(contributor_id: contributor_ids)
    end
  end

  def helpdesk_agent_users
    contributor_ids = helpdesk_agents.contributor.contributor_ids_only_users
    User.joins(:company_users).where(company_users: { contributor_id: contributor_ids })
  end

  def admin_users
    contributor_ids = admins.contributor.contributor_ids_only_users
    User.joins(:company_users).where(company_users: { contributor_id: contributor_ids })
  end

  def module_allowed?(module_type)
    return true if has_current_free_trial?

    subs = subscriptions.where(status: ["active", "canceled"])
                                          .where("end_date IS NULL OR end_date >= ?", Date.today)

    return true if subs.exists?(module_type: ["full_platform", module_type])
  end

  private
  
    def create_default_business_hour
      self.business_hours.create(
        schedule: default_schedule,
        timezone: self.timezone
      )
    end

    def default_schedule
      days_of_week = %w[Monday Tuesday Wednesday Thursday Friday Saturday Sunday].freeze

      default_schedule = days_of_week.map do |day|
        {
          'day' => day,
          'start_time' => '09:00',
          'end_time' => '17:00',
          'active' => !['Saturday', 'Sunday'].include?(day)
        }
      end
      default_schedule
    end

    def update_business_hour_timezone
      company_business_hours = self.business_hours.find_by(workspace_id: nil)
      company_business_hours.update_columns(timezone: self.timezone) unless company_business_hours.timezone == self.timezone
    end

    def decode_and_attach_base64_image
      if logo_image_data.present? && logo_file_name.present?
        decoded_data = Base64.decode64(logo_image_data)
        data = StringIO.new(decoded_data)
        self.logo = { io: data, filename: logo_file_name }
      end
    end

    def create_default_preference
      selected_columns = ManagedAsset::SELECTED_COLUMNS
      card_preference = ManagedAsset::SELECTED_CARD_DATA
      self.create_asset_preference!(preference: selected_columns, analytics_preference: selected_columns, card_preference: card_preference)
      self.create_contract_preference!(preference: Contract::DEFAULT_SELECTED_COLUMNS)
    end

    def create_default_discovered_asset_preference
      selected_columns = ManagedAsset::DISC_ASSET_SELECTED_COLUMNS
      self.create_discovered_asset_preference!(preference: selected_columns)
    end

    def create_subdomain_from_name
      if self.subdomain.blank? && self.name.present?
        self.subdomain = self.name.downcase.gsub(/\s/, '-').gsub(/[^a-z0-9\-]/, '').gsub(/\A\-*/, '').first(36).gsub(/\-*\z/, '')
      end
    end

    def downcase_subdomain
      self.subdomain = self.subdomain.downcase if self.subdomain.present?
    end

    def subdomain_validator
      if self.subdomain.present?
        errors.add(:subdomain, 'must have between 3 and 63 characters') unless (3..63) === self.subdomain.length
        errors.add(:subdomain, 'cannot start or end with a hyphen') unless self.subdomain =~ /\A[^-].*[^-]\z/i
        errors.add(:subdomain, 'must be only alphanumeric (or hyphen)') unless self.subdomain =~ /\A[a-z0-9\-]*\z/i
      end
    end

    def create_system_user_for_company
      ActiveRecord::Base.transaction do
        User.super_admins.each do |super_admin|
          self.system_users.create(user_id: super_admin.id, company_id: self.id, granted_access_at: Time.now, helpdesk_agent: true)
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end

    def create_alert_default
      self.alert_default = AlertDefault.new(vendor_spend: 5)
    end

    def utm_campaign(cookies)
      @utm_cookie ||= cookies[:utm_data]
      @utm_cookie.split(".")[0] if cookies[:utm_data]
    end

    def utm_source(cookies)
      @utm_cookie ||= cookies[:utm_data]
      @utm_cookie.split(".")[1] if cookies[:utm_data]
    end

    def utm_medium(cookies)
      @utm_cookie ||= cookies[:utm_data]
      @utm_cookie.split(".")[2] if cookies[:utm_data]
    end

    def gclid(cookies)
      @gclid ||= cookies[:gclid]
    end

    def valid_to_sync?
      self.saved_changes.keys.any? { |key| MONITORING_COMMON_ATTRIBUTES.include? key }
    end

    def is_second_company?
      user_companies = self.users&.first&.companies&.not_sample
      return !self.is_sample_company? && user_companies.present? && user_companies.count > 1
    end

    def create_monitoring_remote_company
      Monitoring::SyncRemoteCompanyWorker.perform_async(self.id)
    rescue => e
      Rails.logger.warn e.message
    end

    def update_monitoring_remote_company
      Monitoring::SyncRemoteCompanyWorker.new.perform(self.id)
    rescue => e
      Rails.logger.warn e.message
    end

    def delete_monitoring_remote_company
      Monitoring::DestroyRemoteCompanyWorker.perform_async(self.guid)
    rescue => e
      Rails.logger.warn e.message
    end

    def create_nmap_option
      NmapOption.create(company_id: self.id, enabled: false)
    end

    def create_apps_logs_option
      AppsLogsOption.create(company_id: self.id)
    end

    def create_admins_group
      privileges = []
      admin_group = Group.find_or_create_by(company: self, name: 'Admins')
      admin_group.create_contributor
      admin_group.default = true
      admin_group.save!
      admin_group.contributor.privileges.destroy_all
      Privilege::PRIVILEGES.each do |name|
        privilege = admin_group.contributor.privileges.create(name: name, permission_type: 'write') if name != 'HelpTicket'
        if privilege
          privileges << {
            name: privilege[:name],
            permission_type: privilege[:permission_type]
          }
        end
      end
      GroupPrivilege.insert_all privileges
    end

    def create_everyone_group
      everyone_group = Group.find_or_create_by(company: self, name: 'Everyone')
      everyone_group.create_contributor
      everyone_group.default = true
      everyone_group.include_all = true
      everyone_group.save!
      everyone_group.contributor.privileges.destroy_all
    end

    def create_help_desk_agents
      agent_group = Group.find_or_create_by(company: self, name: 'Help Desk Agents')
      agent_group.create_contributor
      agent_group.default = true
      agent_group.save!
      agent_group.contributor.privileges.destroy_all
      privilege = agent_group.contributor.privileges.create(name: 'MobileApp', permission_type: 'write')
      GroupPrivilege.create(name: privilege[:name], permission_type: privilege[:permission_type])
    end

    def create_default_groups
      create_admins_group
      create_everyone_group
      create_help_desk_agents
    end

    def create_company_domain
      CompanyDomain.create(company_id: self.id, domain_name: self.subdomain, is_registered: true)
    end

    def create_default_assets_automated_tasks
      event_types = {
        disk_space: Assets::EventType.find_by(name: "{an asset's} disk space is low", model: "ManagedAsset"),
        agent_resync: Assets::EventType.find_by(name: "{an agent} didn't resync", model: "ManagedAsset"),
        integration_failed: Assets::EventType.find_by(name: "{an integration} failed continuously", model: "ManagedAsset"),
        software_missing: Assets::EventType.find_by(name: "{a software} wasn't detected", model: "ManagedAsset")
      }

      event_subject_types = {
        any_asset: Assets::EventSubjectType.find_by(parent_id: event_types[:disk_space]&.id, name: "any asset"),
        any_agent: Assets::EventSubjectType.find_by(parent_id: event_types[:agent_resync]&.id, name: "any agent"),
        integration: Assets::EventSubjectType.find_by(parent_id: event_types[:integration_failed]&.id, name: "/a/ {specific integration}"),
        software: Assets::EventSubjectType.find_by(parent_id: event_types[:software_missing]&.id, name: "/a/ {specific software}")
      }

      action_type = Assets::ActionType.find_by(name: "send an [email]")

      unless event_types.values.all?(&:present?) && event_subject_types.values.all?(&:present?) && action_type
        return
      end

      create_default_tasks(event_types, event_subject_types, action_type)
    end
end
