module MsTeamsHelper
  extend ActiveSupport::Concern

  def ms_teams_feature_access_pending?(company_id)
    # For all companies: just enable feature
    # For specific company: add company_id in company_ids column
    @feature_access_pending ||= begin
      service_option = ServiceOption.find_by(service_name: 'ms_teams_feature')
      should_display = service_option.status && !service_option.company_ids.include?(company_id)
    end
  end

  def error_event_params(error, detail, api_type)
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: config.id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: '500',
      created_at: DateTime.now
    }
  end
end
