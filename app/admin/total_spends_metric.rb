ActiveAdmin.register_page "Total Spends" do
  menu parent: "Metrics", label: "Total Spends", priority: 10
  breadcrumb do
    ['admin', 'metrics']
  end

  controller do
    include ActionView::Helpers::NumberHelper
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def index
      @fixed_term_contract_spends = Contract.where(contract_type: "fixed_term").sum(:contract_value_amount)
      @fixed_term_contract_spends = "$#{number_to_human(@fixed_term_contract_spends)}"
      @open_ended_contract_spends  = Contract.where(contract_type: "open_ended").sum(:monthly_cost)
      @open_ended_contract_spends = "$#{number_to_human(@open_ended_contract_spends)}"

      @vendors_spends = GeneralTransaction.active.joins(:vendor).sum(:amount)
      @vendors_spends = "$#{number_to_human(@vendors_spends)}"

      @transactions_sum = GeneralTransaction.active.sum(:amount)
      @transactions_sum = "$#{number_to_human(@transactions_sum)}"

      @telecom_services_sum = TelecomService.active.sum(:monthly_cost)
      @telecom_services_sum = "$#{number_to_human(@telecom_services_sum)}"
    end
  end

  content title: "Total spends" do
    render partial: "admin/metrics/total_spends_metric"
  end
end
