<template>
  <div class="clearfix row">
    <sub-menu />
    <div class="col mt-5">
      <h4 class="font-weight-normal mb-1 h5--responsive">Help Center Display Settings</h4>
      <p class="text-secondary mb-3 readable-length not-as-small">
        Set up and manage your public facing Help Center.
        <a 
          href="#"
          class="smallest px-1 rounded ml-3 text-muted bg-light d-inline-flex align-items-center"
          @click.stop.prevent="$refs.infoModal.open"
        >
          <i class="genuicon-info-circled text-info d-flex py-0 5 mr-1" />
          Learn More
        </a>
      </p>
      <div
        v-if="shouldShowHelpCenterAccessSection"
        class="box box--with-heading box--natural-height readable-length--large mb-4"
      >
        <h6 class="box__heading">
          Public Help Center Access
        </h6>
        <div class="box__inner">
          <div class="row">
            <div
              v-for="moduleData in modulesData"
              :key="`help-center-module-${moduleData.id}`"
              class="col-xl-4 col-lg-4 pr-2"
            >
              <div
                v-tooltip="moduleTooltip(moduleData)"
                class="box box--flat box--with-switch px-1 py-2 text-center d-block clickable--with-hover"
                :class="{ 'is-active': moduleData.include }"
                @click.prevent="toggleModuleState(moduleData.name)"
              >
                <span
                  v-if="!moduleData.include"
                  class="box--disabled-overlay t-0"
                />
                <div>
                  <img
                    :src="moduleData.imgSrc"
                    :alt="moduleData.friendlyName"
                    width="80"
                    height="80"
                  >
                  <div class="mb-2 font-weight-semi-bold">{{ moduleData.friendlyName }}</div>
                  <div
                    v-if="currentOrigin"
                    class="mt-1 text-muted"
                  >
                    <code class="url-label">{{ currentOrigin }}{{ moduleData.url }}</code>
                  </div>
                </div>
                <material-toggle
                  transformed
                  :init-active="moduleData.include"
                  @toggle-sample="toggleModuleState(moduleData.name)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box box--with-heading box--natural-height readable-length--large mb-4">
        <h6 class="box__heading">
          Customize Your Public Help Center
        </h6>
        <div class="box__inner">
          <workspace-settings-banner />
          <table
            v-if="!loading"
            class="table"
          >
            <template v-for="helpCenterPortalSetting in helpCenterPortalSettings">
              <component
                :is="`${toHyphenCase(helpCenterPortalSetting.settingType)}-setting`"
                v-if="helpCenterPortalSetting"
                :id="`scroll-up--${toHyphenCase(helpCenterPortalSetting.settingType)}`"
                :key="helpCenterPortalSetting.id"
                :setting="helpCenterPortalSetting"
                :class="{ 'highlight-setting': highlightKey === toHyphenCase(helpCenterPortalSetting.settingType) }"
                @fetch-helpdesk-settings="fetchHelpdeskSettings"
              />
            </template>
          </table>
          <span
            v-else
            class="ml-3 d-inline-block"
          >
            <pulse-loader
              loading
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </div>
      </div>
    </div>

    <sweet-modal
      ref="infoModal"
      v-sweet-esc
    >
      <help-center-info />
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import strings from 'mixins/string';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import rootDomainMixins from 'mixins/root_domain';
  import adminUser from 'mixins/admin_specific';
  import MaterialToggle from 'components/shared/material_toggle.vue';
  import ImageInput from 'components/shared/image_input.vue';
  import HelpCenterInfo from "./help_center_info.vue";
  import * as HelpdeskSettings from '../index';
  import SubMenu from '../sub_menu.vue';
  import workspaceSettingsBanner from '../workspace_settings_banner.vue';

  export default {
    components: {
      SweetModal,
      PulseLoader,
      MaterialToggle,
      ImageInput,
      SubMenu,
      HelpCenterInfo,
      ...HelpdeskSettings,
      workspaceSettingsBanner,
    },
    mixins: [strings, permissionsHelper, adminUser, rootDomainMixins],
    data() {
      return {
        highlightKey: null,
        workspaceId: null,
        helpdeskSettings: null,
        currentOrigin: null,
        loading: true,
        deleteStatus: "",
        isDeleting: false,
        shouldShowHelpCenterAccessSection: false,
        sections: ['accessSettings', 'automationSettings', 'displaySettings'],
        modulesData: [
          {
            id: 0,
            include: true,
            name: 'Help Center',
            friendlyName: 'Help Center',
            url: '/help_center',
            imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/request.png",
          },
          {
            id: 1,
            include: true,
            name: 'Public Knowledge Base',
            friendlyName: 'Public Knowledge Base',
            url: '/knowledge_base',
            imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/article.png",
          },
          {
            id: 2,
            include: true,
            name: 'Public FAQs',
            friendlyName: 'Public FAQs',
            url: '/faqs',
            imgSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/faq.png",
          },
        ],
      };
    },
    computed: {
      ...mapGetters('multiCompany', [
        'companyFilterId',
        'workspaceFilterId',
      ]),
      ...mapGetters(['tickets', 'customDomain']),

      currentWorkspaceName() {
        const workspace = getWorkspaceFromStorage();
        return workspace ? workspace.name : '';
      },
      helpCenterPortalSettings() {
        if (this.helpdeskSettings) {
          return this.helpdeskSettings.helpCenterSettings;
        }
        return null;
      },
    },
    methods: {
      ...mapMutations([
        'setLoadingStatus',
        'setHelpCenterModernDesignEnabled',
        'setCustomDomain',
        'setHideWorkspaceSettingEnabled',
      ]),
      ...mapActions([
        'fetchCustomDomain',
        'fetchHelpCenterLogo',
      ]),
      onWorkspaceChange() {
        this.fetchHelpdeskSettings();
        this.fetchCustomDomain();
        this.fetchHelpCenterLogo();
        const {highlight} = this.$route.query;
        if (highlight) {
          this.highlightKey = highlight;
          const interval = setInterval(() => {
            const el = document.getElementById(`scroll-up--${highlight}`);
            if (el) {
              el.scrollIntoView({ behavior: 'smooth', block: 'center' });
              clearInterval(interval);
            }
          }, 50);
        }
      },
      fetchHelpdeskSettings() {
        http
          .get('/helpdesk_settings.json')
          .then(res => {
            this.currentOrigin = document.location.origin;
            this.helpdeskSettings = res.data.settings;
            this.setHelpdeskSettings();
            this.loading = false;
            this.setLoadingStatus(false);
          })
          .catch(() => {
            this.emitError('There was an issue fetching your helpdesk settings. Please refresh the page and try again.');
            this.loading = false;
            this.setLoadingStatus(false);
          });
      },
      setHelpdeskSettings() {
        const helpCenterPortalSetting = this.helpCenterPortalSettings.find(setting => setting.settingType === "select_new_ui_for_help_center");
        const hideWorkspaceSetting = this.helpCenterPortalSettings.find(setting => setting.settingType === "include_workspace_in_help_center");
        if (helpCenterPortalSetting) {
          this.setHelpCenterModernDesignEnabled(helpCenterPortalSetting.enabled);
        }
        if (hideWorkspaceSetting) {
          this.setHideWorkspaceSettingEnabled(hideWorkspaceSetting.enabled);
        }
      },
      toggleModuleState(moduleName) {
        const selectedModule = this.modulesData.findIndex(item => item.name === moduleName);
        this.modulesData[selectedModule].include = !this.modulesData[selectedModule].include;
      },
      moduleTooltip(moduleData) {
        const action = moduleData.include ? 'Disable' : 'Enable';
        return `${action} ${moduleData.friendlyName} for your ${this.currentWorkspaceName} workspace.`;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .url-label {
    font-size: 0.625rem;
  }
  .highlight-setting {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 5px;
  }
</style>
