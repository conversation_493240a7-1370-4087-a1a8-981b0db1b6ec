ActiveAdmin.register_page "Subscription through Cheque" do
  content title: "Subscription through Cheque" do
    render "admin/add_subscription/show"
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa

    private

    def subscription_module_type(plan_name)
      if plan_name === "Basic Plan" || plan_name === "Yearly Plan"
        "full_platform"
      else
        words = plan_name.split(' ')
        words.pop
        module_name = words.join(' ')
        module_name.downcase.gsub(" ", "_")
      end
    end
  end
  
  page_action :create, method: :post do
    company_subdomain = params[:company_subdomain]
    plan_name = params[:plan_name]
    duration = params[:duration].to_i

    if company_subdomain.present?
      begin
        @company = Company.find_by_cache(subdomain: company_subdomain)
        if @company
          @subscription = @company.subscriptions
          subscription_plan = SubscriptionPlan.find_by(name: plan_name)
          start_date = Date.today
          end_date = subscription_plan.interval == "month" ? start_date + duration.month : start_date + duration.year

          new_subscription = @subscription.create!(
            status: "active",
            start_date: start_date,
            updated_at: Time.now,
            end_date: end_date,
            stripe_subscription_id: nil,
            subscription_plan_id: subscription_plan.id,
            module_type: subscription_module_type(plan_name)
          )

          if new_subscription && (plan_name === "Basic Plan" || plan_name === "Yearly Plan") 
            @company.update_columns(is_legacy_company: true, show_new_plans: false)
          end

          flash[:success] = "Subscription created successfully."
          new_subscription.subscription_activities.create(owner_id: current_user.id, activity_type: "subscription", activity_action: "created", company_id: @company.id)
          new_subscription.stripe_transactions.create!(company_id: @company.id, amount: subscription_plan.price / 100, status: 0, initiated_at: DateTime.current)
          redirect_to admin_company_path(@company)
        else
          flash[:error] = "Company not found against provided subdomain."
          redirect_to admin_subscription_through_cheque_path
        end
      rescue StandardError => e
        flash[:error] = e.message
        redirect_to admin_subscription_through_cheque_path
      end
    else
      flash[:error] = "Please enter a valid company subdomain."
      redirect_to admin_subscription_through_cheque_path
    end
  end
end
