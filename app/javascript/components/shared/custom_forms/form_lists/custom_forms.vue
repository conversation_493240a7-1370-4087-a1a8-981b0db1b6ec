<template>
  <div>
    <workspace-settings-banner
      v-if="isHelpdeskModule"
      class="mt-5"
    />
    <div class="row mt-3">
      <div class="col-md-6 mb-2">
        <h5 class="font-weight-normal mb-1 d-inline-block h5--responsive">
          My Custom Forms
        </h5>
        <p
          class="not-as-small text-secondary p--responsive"
          :class="{'mb-3': !showDisableDragAlert, 'mb-0': showDisableDragAlert}"
        >
          Select a form below to edit, or create a new one either using a template below or start with a blank state.
        </p>
        <p
          v-if="showDisableDragAlert"
          class="text-danger small"
        >
          Filter by a specific company in order to customize the order.
        </p>
      </div>
      <div class="col-md-6 d-flex justify-content-end align-items-center">
        <dropdown-filter
          id-label="id"
          class="float-right"
          label="Status"
          :options="statusOptions"
          :value="statusFilterValue"
          data-tc-form-filter="Help Desk"
          @selected="filterStatus"
        />
      </div>
    </div>

    <div
      v-if="customForms"
      :class="{'row': customForms.length > 0}"
    >
      <draggable
        v-if="customForms && customForms.length > 0"
        :value="customForms"
        class="row w-100 ml-0"
        handle=".handle"
        animation="150"
        dragover-bubble="true"
        v-bind="dragOptions"
        @choose="startDragging"
        @change="updatePosition"
      >
        <div
          v-for="form in customForms"
          :key="form.id"
          class="col-12 col-lg-3 col-xxl-2 mb-4"
          :data-tc-view-form="form.name || form.formName"
        >
          <router-link
            class="col-12 box box--with-hover py-2 form-card pl-4"
            :to="`${basePath}/${form.id}/edit`"
          >
            <div class="drag-overlay">
              <span class="handle genuicon-draggable"/>
            </div>
            <div class="box__inner">
              <span
                v-if="form.inUse"
                v-tooltip="`Some ${moduleName}s are associated with this form`"
                class="status-icon align-top bg-success"
              />
              <span
                v-else-if="form.isDraft"
                v-tooltip="'This form is in draft. Publish it to make it available.'"
                class="badge box-badge--draft"
              >
                Draft
              </span>
              <span
                v-else
                v-tooltip="`No ${moduleName} is associated with this form`"
                class="status-icon align-top bg-darken"
              />
              <div>
                <div
                  class="mb-2"
                  :data-tc-form-name="form.name || form.formName"
                >
                  {{ form.name || form.formName }}
                </div>
                <div
                  v-if="!isMultiWorkspace()"
                  class="small text-muted"
                >
                  Select to edit this form
                </div>
              </div>
            </div>
          </router-link>
        </div>
      </draggable>
      <div v-else-if="isArchivedOrDraft">
        <h4 class="text-center mt-3">
          <i class="nulodgicon-trash-b mr-1" />
          No {{ statusFilterValue }} forms, currently.
        </h4>
      </div>
    </div>
    <div v-else>
      <clip-loader
        loading
        class="ml-5"
        color="#0d6efd"
        size="1.5rem"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapActions, mapMutations, mapGetters } from 'vuex';
  import clipLoader from 'vue-spinner/src/ClipLoader.vue';
  import Draggable from 'vuedraggable';
  import _get from 'lodash/get';
  import _cloneDeep from 'lodash/cloneDeep';
  import _map from 'lodash/map';
  import multiCompany from 'mixins/multi_company';
  import customFormHelper from 'mixins/custom_form_helper';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import DropdownFilter from "../../dropdown_filter.vue";
  import workspaceSettingsBanner from '../../../help_tickets/settings/workspace_settings_banner.vue';

  export default {
    components: {
      clipLoader,
      DropdownFilter,
      Draggable,
      workspaceSettingsBanner,
    },
    mixins: [ multiCompany, customFormHelper, strings, permissionsHelper ],
    props: {
      allowMultiCompany: {
        type: Boolean,
        default: true,
      },
      basePath: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        showDisableDragAlert: false,
        companyModule: '',
        statusOptions: [
          {
            id: "active",
            name: 'Active',
          },
          {
            id: "archived",
            name: 'Archived',
          },
          {
            id: "draft",
            name: 'Draft',
          },
        ],
      };
    },
    computed: {
      ...mapGetters('customForms', ['customForms', 'statusFilterValue']),
      ...mapGetters('multiCompany', [
        'workspaceFilterId',
      ]),
      moduleName() {
        return this.toTitle(_get(this, "getModuleData.name")).toLowerCase();
      },
      dragOptions() {
        return {
          animation: 200,
          ghostClass: "ghost",
        };
      },
      shouldDisableDragging() {
        const companyIdsCount = [... new Set(_map(this.customForms, 'companyId'))].length;
        // Check if this is a parent company with child companies that also have custom forms and a company is not filtered
        return (this.companyIds && this.companyIds.length > 1 && companyIdsCount > 1);
      },
      isArchivedOrDraft() {
        return ['archived', 'draft'].includes(this.statusFilterValue);
      },
      isHelpdeskModule() {
        return this.moduleName === 'help ticket';
      },
    },
    methods: {
      ...mapMutations('customForms', ['setCustomForms', 'setStatusFilterValue']),
      ...mapActions('customForms', ['fetchCustomForms']),

      onWorkspaceChange() {
        this.setCustomForms(null);
        this.companyModule = this.getCompanyModule;
        this.setCustomForms(null);
        this.fetchForms();

        const onCompanyChange = () => {
          // eslint-disable-next-line no-underscore-dangle
          if (!this._isMounted || this._isBeingDestroyed) {
            document.removeEventListener("company-change", onCompanyChange);
            return;
          }
          this.onWorkspaceChange();
        };
        document.addEventListener("company-change", onCompanyChange);
      },
      fetchForms() {
        const params = {
          companyModule: this.companyModule,
          status: this.statusFilterValue,
        };

        this.fetchCustomForms(params);
      },
      startDragging() {
        this.showDisableDragAlert = false;
      },
      filterStatus(option) {
        const statusValue = option || 'all';
        this.setStatusFilterValue(statusValue);
        this.fetchForms();
      },
      updatePosition(data) {
        if (this.shouldDisableDragging) {
          this.showDisableDragAlert = true;
        } else {
          const moved = _get(data, 'moved.element');
          const newIndex = _get(data, 'moved.newIndex');
          const oldIndex = _get(data, 'moved.oldIndex');
          if (!moved) {
            return;
          }
          const forms = _cloneDeep(this.customForms);
          forms.splice(oldIndex, 1);
          forms.splice(newIndex, 0, moved);
          let idx = -1;
          const idMap = forms.map((form) => {
            idx += 1;
            return {
              id: form.id,
              order: idx,
            };
          });
          const params = {
            forms: idMap,
            companyModule: this.companyModule,
            companyId: this.companyId,
            status: this.statusFilterValue,
          };
          http
            .put('/custom_forms/orderings.json', params)
            .then((res) => {
              this.setCustomForms(res.data);
              this.emitSuccess("Forms updated successfully");
            })
            .catch(() => {
              this.emitError("Sorry, an error occurred during update.");
            });
        }
      },
    },
  };
</script>

<style scoped lang="scss">
  .status-icon {
    position: absolute;
    right: 7px;
    top: 7px;
  }

  .handle {
    cursor: move;
    font-size: 1.125rem;
    position: absolute;
    top: 33%;
  }

  .drag-overlay {
    background-color: rgba(33, 37, 41, 0.1);
    border-radius: 0.50rem 0 0 0.50rem;
    color: $themed-base;
    content: "";
    cursor: move;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    visibility: hidden;
    width: 1.25rem;
    z-index: 2;

    &:hover {
      background-color: rgba(33, 37, 41, 0.3);
    }
  }

  .ghost {
    background-color: color.adjust($primary, $alpha: -0.9) !important;
    background-image: none !important;
    border: 2px dashed $gray-500;
    border-radius: $border-radius * 2;
    margin-top: -2px; // Offset the border
    opacity: 1;

    > *, &:after, &:before {
      opacity: 0 !important;
      visibility: hidden;
      transition: none;
    }
  }

  .form-card:hover {
    .drag-overlay {
      opacity: 1;
      visibility: visible;
    }
  }

  .badge {
    position: absolute;
    border-radius: 0 0 0 $border-radius-sm;
    font-size: 0.625rem;
    right: 0;
    top: 0;
    width: 4rem;
  }
</style>
