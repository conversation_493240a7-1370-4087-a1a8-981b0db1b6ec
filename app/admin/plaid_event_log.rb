ActiveAdmin.register Logs::PlaidEventLog, as: 'plaid_event_log' do
  menu parent: 'Logs', priority: 1
  breadcrumb do
    ['admin', 'logs']
  end

  filter :event_name
  config.filters = false

  index do
    selectable_column
    id_column
    column ("Event") { |event| "#{event.event_name}" }
    column ("Error Code") { |event| "#{JSON.parse(event.metadata)["error_code"]}" }
    column ("Error Message") { |event| "#{JSON.parse(event.metadata)["error_message"]}" }
    column ("Error Type") { |event| "#{JSON.parse(event.metadata)["error_type"]}" }
    column ("Institution") { |event| "#{JSON.parse(event.metadata)["institution_name"]}" }
    column ("Institution ID") { |event| "#{JSON.parse(event.metadata)["institution_id"]}" }
    column ("Company") { |event| "#{event&.company_user&.company&.name}" }
    column ("User Name") { |event| "#{event&.company_user&.user&.full_name}" }
    column ("User Email") { |event| "#{event&.company_user&.user&.email}" }
    column ("Time") { |event| "#{event.created_at.strftime("%Y-%m-%d %I:%M%p")}" }
    column ("Link Session ID") { |event| "#{JSON.parse(event.metadata)["link_session_id"]}" }
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end