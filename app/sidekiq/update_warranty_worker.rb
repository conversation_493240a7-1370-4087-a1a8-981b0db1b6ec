class UpdateWarrantyWorker
  include Sidekiq::Worker

  def perform(file_path)
    s3 = Aws::S3::Client.new(
        region: Rails.application.credentials.aws[:s3][:region],
        access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
        secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )
    s3_response = s3.get_object({
        bucket: "gogenuity-#{Rails.env}",
        key: file_path
    })
    file_text = s3_response.body.read
    CSV.parse(file_text, headers: true).each do |row|
      csv_serial_number = row[0].strip
      csv_expire_date = DateTime.parse(row[4].strip).to_date
      manufacturers = ["Hp","hP","HP","hp"]
      ManagedAsset.where(manufacturer: manufacturers, machine_serial_number: csv_serial_number, merged: false).find_each do |asset|
        if asset.warranty_expiration != csv_expire_date
          asset.update(warranty_expiration: csv_expire_date, warranty_info_fetched: true)
        end
      end
    end
  end
end
