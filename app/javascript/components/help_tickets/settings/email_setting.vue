<template>
  <div class="clearfix mx-1">
    <div v-if="!helpdeskEmailSettings">
      <h4 class="mt-5">
        <span class="float-left mr-2">
          Loading General <PERSON><PERSON> Settings
        </span>
        <span>
          <pulse-loader
            :loading="true"
            class="float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h4>
    </div>
    <div
      v-else
      class="box box--block box--natural-height p-5 mb-5 ml-3 mt-5 table-width"
    >
      <workspace-settings-banner />
      <div class="box-inner w-100">
        <h5 class="font-weight-normal mb-1 h5--responsive">
          General Email Settings
        </h5>
        <table class="table">
          <template v-for="setting in helpdeskEmailSettings">
            <component
              :is="`${toHyphenCase(setting.settingType)}-setting`"
              :key="setting.id"
              :setting="setting"
            />
          </template>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import { mapMutations } from 'vuex';
  import * as HelpdeskSettings from './index';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';


  export default {
      components: {
        PulseLoader,
        ...HelpdeskSettings,
        workspaceSettingsBanner,
      },
      mixins: [strings, permissionsHelper ],
      data() {
        return{
          helpdeskEmailSettings: null,
        };
      },
      methods: {
        ...mapMutations(['setLoadingStatus']),
        onWorkspaceChange() {
          this.fetchCompanyMailers();
        },
        fetchCompanyMailers() {
          this.setLoadingStatus(true);
          http
            .get('/company_mailers.json?module=Helpdesk')
            .then(res => {
              this.helpdeskEmailSettings = res.data.helpdeskEmailSettings.emailSettings;
              this.setLoadingStatus(false);
            })
            .catch(() => {
              this.emitError('There was an issue fetching your email settings. Please refresh the page and try again.');
              this.setLoadingStatus(false);
            });
        },
      },
  };
</script>
<style lang="scss" scoped>
  .table-width {
    min-width: 20%; 
    max-width: 100%;
  }
</style>
