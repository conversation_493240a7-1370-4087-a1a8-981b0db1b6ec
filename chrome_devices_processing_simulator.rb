#!/usr/bin/env ruby
# Chrome Devices Processing Simulator
# Simulates the EXACT save_chrome_devices_worker.rb logic on NSBOROS CSV data

require 'csv'

class ChromeDevicesProcessingSimulator
  def initialize(csv_file_path)
    @csv_file_path = csv_file_path
    @company_id = 1 # Simulate NSBOROS company ID
    @import_target = "managed_asset" # Default import target
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0

    # Simulate existing discovered assets database
    @existing_discovered_assets = {}
    @existing_managed_assets = {}

    @processing_stats = {
      total_devices: 0,
      mac_validation_passed: 0,
      mac_validation_failed: 0,
      nil_mac_addresses: 0,
      empty_mac_addresses: 0,
      new_discovered_assets: 0,
      updated_discovered_assets: 0,
      new_managed_assets: 0,
      devices_with_empty_final_mac: 0,
      devices_using_existing_mac: 0,
      devices_using_new_mac: 0
    }
    @sample_devices = []
  end

  def run_simulation
    puts "🔍 CHROME DEVICES PROCESSING SIMULATION"
    puts "=" * 50
    puts "📁 Processing file: #{@csv_file_path}"
    
    process_csv_data
    generate_detailed_report
    analyze_your_selected_code
    provide_recommendations
  end

  private

  def process_csv_data
    CSV.foreach(@csv_file_path, headers: true) do |row|
      @processing_stats[:total_devices] += 1
      
      # Extract device data (matching your worker structure)
      device_data = {
        serial_number: row['serialNumber'],
        model: row['model'],
        os_version: row['osVersion'],
        mac_address: row['macAddress'],  # Column 14
        ethernet_mac: row['ethernetMacAddress'], # Column 13
        device_id: row['deviceId'],
        provision_status: row['provisionStatus']
      }
      
      # Simulate the exact worker logic
      simulate_device_processing(device_data)
    end
  end

  def simulate_device_processing(device_data)
    # EXACT same logic as save_chrome_devices_worker.rb
    @discovered_asset = nil
    @managed_asset = nil
    mac_addresses = []

    # Extract device data exactly like the worker
    display_name = device_data[:model] && !device_data[:model].empty? ? device_data[:model] : "ChromeOS #{device_data[:os_version]}"
    serial_number = device_data[:serial_number]
    model = device_data[:model]
    operating_system = "ChromeOS #{device_data[:os_version]}"
    os_name = "ChromeOS"
    os_version = device_data[:os_version]
    mac_address = device_data[:mac_address] ? device_data[:mac_address].upcase : nil

    # EXACT MAC address validation logic from your worker
    if mac_address && mac_address_valid?(mac_address)
      mac_addresses << mac_address
      @processing_stats[:mac_validation_passed] += 1
    else
      @processing_stats[:mac_validation_failed] += 1
      if mac_address.nil?
        @processing_stats[:nil_mac_addresses] += 1
      elsif mac_address.empty?
        @processing_stats[:empty_mac_addresses] += 1
      end
    end

    source = 'google_workspace'
    last_check_in_time = device_data[:last_sync]

    # Simulate find_discovered_asset - check if asset exists
    asset_key = "#{serial_number}_#{mac_addresses.join('_')}_#{display_name}"
    discovered_asset = @existing_discovered_assets[asset_key]

    if discovered_asset.nil?
      # Create new discovered asset (simulate DiscoveredAsset.ready_for_import.new)
      discovered_asset = {
        id: @existing_discovered_assets.length + 1,
        company_id: @company_id,
        machine_serial_no: "",
        mac_addresses: [],
        display_name: "",
        ip_address: nil,
        new_record: true
      }
      @existing_discovered_assets[asset_key] = discovered_asset
      @new_discovered_assets_count += 1
      @processing_stats[:new_discovered_assets] += 1
    else
      @processing_stats[:updated_discovered_assets] += 1
    end

    # YOUR SELECTED CODE - This is the critical line
    mac_addresses_data = mac_addresses.any? ? mac_addresses : discovered_asset[:mac_addresses]
    display_name_data = display_name && !display_name.empty? ? display_name : discovered_asset[:display_name]
    ip_address_data = discovered_asset[:ip_address]
    serial_data = serial_number && !serial_number.empty? ? serial_number : discovered_asset[:machine_serial_no]

    # Track the impact of your selected code
    if mac_addresses.any?
      @processing_stats[:devices_using_new_mac] += 1
    else
      @processing_stats[:devices_using_existing_mac] += 1
      if mac_addresses_data.empty?
        @processing_stats[:devices_with_empty_final_mac] += 1
      end
    end

    # Simulate precedence check (simplified)
    is_lower_precedence = false

    # Update discovered asset attributes
    discovered_asset.merge!({
      display_name: display_name_data,
      machine_serial_no: serial_data,
      mac_addresses: mac_addresses_data,
      ip_address: ip_address_data,
      asset_type: "Laptop",
      os: operating_system,
      os_name: os_name,
      os_version: os_version,
      model: model,
      source: source,
      discovered_asset_type: "device",
      lower_precedence: is_lower_precedence,
      last_check_in_time: last_check_in_time
    })

    # Simulate managed asset creation if import_target is "managed_asset"
    if @import_target == "managed_asset" && discovered_asset[:managed_asset_id].nil?
      # Simulate BulkDiscoveredAssetsUpdate.move_to_managed_asset
      managed_asset_key = "#{serial_data}_#{mac_addresses_data.join('_')}"
      if @existing_managed_assets[managed_asset_key].nil?
        @existing_managed_assets[managed_asset_key] = {
          id: @existing_managed_assets.length + 1,
          company_id: @company_id,
          name: display_name_data,
          machine_serial_number: serial_data,
          mac_addresses: mac_addresses_data
        }
        discovered_asset[:managed_asset_id] = @existing_managed_assets[managed_asset_key][:id]
        @new_managed_assets_count += 1
        @processing_stats[:new_managed_assets] += 1
      end
    end

    # Sample devices for analysis
    if @sample_devices.length < 15
      @sample_devices << {
        serial: serial_number,
        model: model,
        original_mac: device_data[:mac_address],
        processed_mac: mac_address,
        mac_validation_passed: mac_address && mac_address_valid?(mac_address),
        mac_addresses_array: mac_addresses,
        final_mac_data: mac_addresses_data,
        used_existing_mac: !mac_addresses.any?,
        provision_status: device_data[:provision_status],
        created_new_asset: discovered_asset[:new_record],
        created_managed_asset: !discovered_asset[:managed_asset_id].nil?
      }
    end
  end

  def mac_address_valid?(mac_address)
    return false if mac_address.nil? || mac_address.empty?

    # EXACT same regex from your GoogleWorkspaceCommonMethods
    regex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i
    return false if mac_address.to_s.include?("00:00:00:00:00:00")
    !mac_address.to_s.match(regex).nil?
  end

  def generate_detailed_report
    puts "\n📊 REAL-TIME WORKER PROCESSING SIMULATION"
    puts "=" * 50

    total = @processing_stats[:total_devices]
    puts "Total devices in CSV: #{total}"
    puts "Expected in system: 4,957"
    puts "Missing devices: #{total - 4957}"

    puts "\n🔍 MAC Address Validation Results:"
    puts "MAC validation passed: #{@processing_stats[:mac_validation_passed]} (#{percentage(@processing_stats[:mac_validation_passed], total)}%)"
    puts "MAC validation failed: #{@processing_stats[:mac_validation_failed]} (#{percentage(@processing_stats[:mac_validation_failed], total)}%)"
    puts "  - Nil MAC addresses: #{@processing_stats[:nil_mac_addresses]}"
    puts "  - Empty MAC addresses: #{@processing_stats[:empty_mac_addresses]}"
    puts "  - Invalid format MACs: #{@processing_stats[:mac_validation_failed] - @processing_stats[:nil_mac_addresses] - @processing_stats[:empty_mac_addresses]}"

    puts "\n📈 Asset Creation Results:"
    puts "New discovered assets created: #{@processing_stats[:new_discovered_assets]}"
    puts "Existing discovered assets updated: #{@processing_stats[:updated_discovered_assets]}"
    puts "New managed assets created: #{@processing_stats[:new_managed_assets]}"

    puts "\n🎯 Your Selected Code Impact:"
    puts "Devices using new MAC data: #{@processing_stats[:devices_using_new_mac]}"
    puts "Devices using existing asset MAC: #{@processing_stats[:devices_using_existing_mac]}"
    puts "Devices ending up with empty MAC: #{@processing_stats[:devices_with_empty_final_mac]}"

    puts "\n🔍 Sample Processed Devices (First 15):"
    @sample_devices.each_with_index do |device, index|
      puts "\n#{index + 1}. Serial: #{device[:serial]}"
      puts "   Model: #{device[:model]}"
      puts "   Provision Status: #{device[:provision_status]}"
      puts "   Original MAC: '#{device[:original_mac]}'"
      puts "   MAC Validation: #{device[:mac_validation_passed] ? 'PASSED' : 'FAILED'}"
      puts "   MAC Array: #{device[:mac_addresses_array].inspect}"
      puts "   Final MAC Data: #{device[:final_mac_data].inspect}"
      puts "   Used Existing MAC: #{device[:used_existing_mac] ? 'YES' : 'NO'}"
      puts "   Created New Asset: #{device[:created_new_asset] ? 'YES' : 'NO'}"
      puts "   Created Managed Asset: #{device[:created_managed_asset] ? 'YES' : 'NO'}"
    end
  end

  def analyze_your_selected_code
    puts "\n🎯 YOUR SELECTED CODE DETAILED ANALYSIS"
    puts "=" * 45
    puts "Code: mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses"

    puts "\n📊 Real Processing Impact:"
    puts "Devices with valid MAC (mac_addresses.present? = true): #{@processing_stats[:devices_using_new_mac]}"
    puts "  └─ These use NEW MAC data from Google Workspace"
    puts "Devices with invalid MAC (mac_addresses.present? = false): #{@processing_stats[:devices_using_existing_mac]}"
    puts "  └─ These use EXISTING asset MAC data (if any)"
    puts "Devices ending up with empty MAC arrays: #{@processing_stats[:devices_with_empty_final_mac]}"
    puts "  └─ These are your 684 empty MAC rows in database!"

    puts "\n🔍 The Chain of Events:"
    puts "1. Google API returns #{@processing_stats[:mac_validation_failed]} devices with invalid/empty MACs"
    puts "2. mac_address_valid? filters these out → mac_addresses array stays empty"
    puts "3. Your selected code: mac_addresses.present? = false → uses existing asset MAC"
    puts "4. For new assets, existing MAC is also empty → final MAC array is empty"
    puts "5. Device gets saved with empty MAC array → creates your 684 empty MAC rows"

    puts "\n⚠️  The Real Problem:"
    puts "- It's NOT that devices are filtered out of processing"
    puts "- ALL #{@processing_stats[:total_devices]} devices get processed and saved"
    puts "- But #{@processing_stats[:devices_with_empty_final_mac]} devices have no MAC for identification"
    puts "- This causes deduplication and counting issues"
    puts "- Result: Missing #{@processing_stats[:total_devices] - 4957} devices in your inventory"
  end

  def provide_recommendations
    puts "\n💡 FINDINGS & RECOMMENDATIONS"
    puts "=" * 35

    devices_without_mac = @processing_stats[:empty_mac_addresses] + @processing_stats[:nil_mac_addresses]

    puts "\n🎯 Key Findings:"
    puts "- Your CSV contains #{@processing_stats[:total_devices]} total Chrome devices"
    puts "- #{devices_without_mac} devices (#{percentage(devices_without_mac, @processing_stats[:total_devices])}%) have no MAC address"
    puts "- The worker processes ALL devices (no filtering based on MAC)"
    puts "- Your selected code causes #{devices_without_mac} devices to have empty MAC arrays"
    puts "- This explains your 684 empty MAC rows in the database"

    puts "\n� The Real Problem:"
    puts "- It's NOT that devices are being filtered out"
    puts "- It's that devices without MAC addresses can't be properly:"
    puts "  • Deduplicated (multiple entries for same device)"
    puts "  • Matched with existing assets"
    puts "  • Identified uniquely"
    puts "- This leads to counting/inventory discrepancies"

    puts "\n🔧 Recommended Solutions:"
    puts "1. Use serial numbers as primary identifier instead of MAC addresses"
    puts "2. Add logging to track devices with empty MAC arrays"
    puts "3. Implement better deduplication logic for devices without MACs"
    puts "4. Investigate why Google Workspace doesn't provide MAC addresses for these devices"

    puts "\n� Expected Impact:"
    puts "- If you fix the MAC address issue, you should see closer to #{@processing_stats[:total_devices]} devices"
    puts "- Current gap: #{@processing_stats[:total_devices] - 4957} devices"
    puts "- Likely cause: Deduplication/counting issues with empty MAC devices"
  end

  def percentage(part, total)
    return 0 if total == 0
    ((part.to_f / total) * 100).round(2)
  end
end

# Run the simulation
if __FILE__ == $0
  csv_path = "/home/<USER>/Downloads/nsboro google devices console.csv"
  simulator = ChromeDevicesProcessingSimulator.new(csv_path)
  simulator.run_simulation
end
