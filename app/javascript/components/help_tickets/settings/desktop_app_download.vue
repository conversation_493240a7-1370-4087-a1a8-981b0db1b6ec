<template>
  <div class="row d-flex flex-nowrap">
    <sub-menu />
    <div
      v-if="isLoadingMiniHDApp"
      class="mt-5"
    >
      <sync-loader
        color="#0d6efd"
        size="0.5rem"
        :loading="true"
      />
    </div>
    <div
      v-else
      class="mt-5 mr-3 heading_width"
    >
      <div class="box box--with-heading w-100">
        <h5 class="box__heading">
          Desktop App
        </h5>
        <div class="d-flex flex-row box__inner inner p-0">
          <div class="d-flex flex-column justify-content-between pl-3">
            <div class="pt-3">
              <h4 class="title mt-2">
                Desktop App for Genuity End Users
              </h4>
              <p class="mt-3 text-muted description-size">
                Take the power of your help desk with you. With the Genuity Help Desk Desktop App, you can deliver exceptional service without switching browsers or losing focus. Consolidate inquiries from every channel and resolve issues quickly, all in one place.
              </p>
            </div>
            <div class="pl-3 d-flex row app-links">
              <a
                :href="`${miniHDMacUrl}`"
                target="blank"
                rel="noopener noreferrer"
                class="d-flex button px-3 py-1 mr-3 align-items-center box"
              >
                <img
                  src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/apple-logo.png"
                  alt="Apple store logo"
                  class="logo mr-3"
                >
                <div class="d-flex flex-column">
                  <p class="p-0 smallest mb-0">
                    Download for
                  </p>
                  <h5 class="p-0 m-0">
                    Mac
                  </h5>
                </div>
              </a>
              <a
                :href="`${miniHDWindowsUrl}`"
                target="blank"
                rel="noopener noreferrer"
                class="d-flex button px-3 py-1 align-items-center box"
              >
                <img
                  src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/windows-logo.webp" 
                  alt="Play store logo"
                  class="logo mr-3"
                >
                <div class="d-flex flex-column">
                  <p class="p-0 smallest mb-0">
                    Download for
                  </p>
                  <h5 class="p-0 m-0">
                    Windows
                  </h5>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import SubMenu from './sub_menu.vue';

  export default {
    components: {
      SubMenu,
      SyncLoader,
    },
    data() {
      return {
        isLoadingMiniHDApp: true,
        miniHDMacUrl: '',
        miniHDWindowsUrl: '',
        miniHDMacAppVersion: '',
        miniHDWindowsAppVersion: '',
      };
    },
    mounted() {
      this.setMiniHdAppsData();
    },
    methods: {
      setMiniHdAppsData() {
        http
          .get('/download_mini_helpdesk.json')
          .then((res) => {
            if (res.data.status) {
              this.miniHDMacUrl = res.data.macUrl;
              this.miniHDWindowsUrl = res.data.windowsUrl;
              this.miniHDMacAppVersion = res.data.macAppVersion;
              this.miniHDWindowsAppVersion = res.data.windowsAppVersion;
            } else {
              this.emitError('Sorry, Desktop App might not be present. Please try again later.');
            }
          })
          .catch(() => {
            this.emitError('Sorry there was an error downloading. Please try again later.');
          })
          .finally(() => {
            this.isLoadingMiniHDApp = false;
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .heading_width {
    width: 75%;
  }

  .title {
    font-weight: 300;
  }

  .description-size {
    font-size: 0.9rem;
    margin-right: 14rem;
  }

  .button {
    background-color: black;
    border-radius: 0.5rem;
    color: white;
    transition: transform 0.2s ease;
  }

  .button:hover {
    transform: translateY(-2px);
  }

  .logo {
    width: 1.5rem;
    height: auto;
    display: block;
    object-fit: contain;
  }

  .app-links {
    margin-bottom: 1.75rem;
  }
</style>
