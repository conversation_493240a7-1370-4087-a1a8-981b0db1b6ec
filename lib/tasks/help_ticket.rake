namespace :help_ticket do
  desc "It will fix a bad formatted help ticket paragraph"
  task fix_help_paragraph: :environment do
    HelpTicket.find_each do |ticket|
      text = help_paragraph_formatted(ticket.help_paragraph)
      if text != ticket.help_paragraph
        ticket.help_paragraph = text
        ticket.save!
        putc "X"
      else
        putc "."
      end
    end
    HelpTicketEmail.find_each do |email|
      html = nil
      text = nil
      html = help_paragraph_formatted(email.body_html) if email.body_html
      text = help_paragraph_formatted(email.body_text) if email.body_text
      if text != email.body_text || html != email.body_html
        email.body_text = text
        email.body_html = html
        email.save!
        putc "X"
      else
        putc "."
      end
    end
    puts
  end

  desc "create new help desk settings"
  task create_new_help_desk_settings: :environment do
    companies = Company.all
    companies.find_each do |company|
      DefaultHelpdeskSetting.find_each do |setting|
         helpdesk_setting = company.helpdesk_settings.find_or_initialize_by(
          company_id: company.id,
          default_helpdesk_setting_id: setting.id
        )
        if setting.setting_type == "response_to_closed_ticket"
          helpdesk_setting.assign_attributes( selected_option: "add_comment")
        end
        helpdesk_setting.save!
      end
    end
  end

  desc "create new help desk settings"
  task add_new_config_to_help_desk_settings: :environment do
    companies = Company.all
    new_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_non_user_to_get_update_about_their_tickets")
    companies.find_each do |company|
      helpdesk_setting = company.helpdesk_settings.find_or_initialize_by(
        company_id: company.id,
        default_helpdesk_setting_id: new_setting.id
      )
      helpdesk_setting.save!
    end
  end

  desc "Added background color for open ticket portal in help desk settings"
  task add_background_color_settings_in_help_desk: :environment do
    companies = Company.all
    bg_color_setting = DefaultHelpdeskSetting.find_by(setting_type: "bg_color_for_help_center")
    companies.find_each do |company|
      helpdesk_setting = company.helpdesk_settings.find_or_initialize_by(
        company_id: company.id,
        default_helpdesk_setting_id: bg_color_setting.id,
        selected_option: "#ffffff"
      )
      helpdesk_setting.save!
    end
  end

  desc "Update Url in ticket description and comments"
  task update_urls_in_ticket_description_and_comment: :environment do
    HelpTicket.where.not(help_paragraph: "").find_each do |ticket|
      doc = Nokogiri::HTML ticket.help_paragraph
      if doc.css("a").count > 0
        ticket.update_columns(help_paragraph: update_links(ticket, doc))
      end
    end
    HelpTicketActivity.where(activity_type: "note").find_each do |note|
      doc = Nokogiri::HTML note.data["note_body"]
      if doc.css("a").count > 0
        note.update_columns(data: {"note_body": update_links(note.help_ticket, doc)})
      end
    end
  end

  def update_links ticket, doc
    if Rails.env.production? || Rails.env.staging?
      http = "https://"
    else
      http = "http://"
    end

    arr = ['/help_tickets/', '/managed_assets/', '/contracts/', '/vendors/', '/company/users/', '/help_tickets/faqs/']
    doc.css("a")&.each do |link|
      have_link_to_update = false
      arr.each do |str|
        if link.values.to_s.include?(str) && !link.values.to_s.include?("http://")
          have_link_to_update = true
          break
        end
      end
      if have_link_to_update
        link.attributes["href"].value = "#{http}#{ticket.company.subdomain}.#{ Rails.application.credentials.root_domain }#{link.attributes["href"].value}"
      end
    end
    doc.to_html
  end

  desc "Update assignment activity"
  task update_assignment_activities: :environment do
    HelpTicketActivity.where(activity_type: "assignment").find_each do |activity|
      if activity.data.key?('current_assigned_to') || activity.data.key?('previous_assigned_to')
        data = activity.data

        data['current_contributor_id'] = data.delete('current_assigned_to')
        data['previous_contributor_id'] = data.delete('previous_assigned_to')

        current_contributor = CompanyUser.find_by_id(data['current_contributor_id'])
        previous_contributor = CompanyUser.find_by_id(data['previous_contributor_id'])

        data['current_contributor_id'] = current_contributor ? current_contributor&.contributor.id : nil
        data['previous_contributor_id'] = previous_contributor ? previous_contributor&.contributor.id : nil

        activity.update_columns(data: data, activity_type: "assigned_contributor")
      end
    end
  end

  def help_paragraph_formatted(help_paragraph)
    if help_paragraph.index("&lt;body")
      if help_paragraph =~ /body.*\&gt\;/
        idx = help_paragraph.index("&lt;body")
        help_paragraph = help_paragraph[idx..-1]
        idx = help_paragraph.index("&gt;") + 4
        help_paragraph = help_paragraph[idx..-1]
      end
      if help_paragraph.index("&lt;/body")
        idx = help_paragraph.index("&lt;/body") - 1
        help_paragraph = help_paragraph[0..idx]
      end
      help_paragraph = Nokogiri::HTML.parse(help_paragraph).text
      help_paragraph
    end
    help_paragraph
  end

  desc "move helpdesk_faq_attachments records to attachment_uploads records"
  task update_helpdesk_faq_attachments_location: :environment do
    HelpdeskFaqAttachment.all.find_each do |faq_attachment|
      new_attachment = AttachmentUpload.new(
        attachment: faq_attachment.attachment,
        attachable_type: "HelpdeskFaq",
        attachable_id: faq_attachment.helpdesk_faq_id,
        company_id: faq_attachment.company_id
      )
      faq_attachment.destroy if new_attachment.save
    end
  end

  desc "Add missing data to help ticket comment activities"
  task add_missing_data_to_ticket_comment_activities: :environment do
    HelpTicketActivity.where(activity_type: "note").find_each do |activity|
      if activity.data && activity.data["ticket_subject"].nil?
        new_data = activity.data
        new_data["ticket_subject"] = HelpTicketFacade.new(activity.help_ticket).get_custom_form_value("subject")
        new_data["ticket_number"] = activity.help_ticket.ticket_number
        activity.skip_automated_task_processing = true
        activity.update(data: new_data)
      end
    end
  end

  desc "Attach image urls in ticket description"
  task fix_ticket_description: :environment do
    HelpTicket.where.not(message_id: nil).each do |ticket|
      description_value = ticket.custom_form_values
        .includes(:custom_form_field)
        .find_by(custom_form_fields: { label_name: "Description" })
      description = description_value&.value_str

      if ticket.custom_form_values.joins(:custom_form_attachment).present? && description.present?
        html_doc = Nokogiri::HTML description
        if html_doc.css("img").present?
          ticket.custom_form_values.joins(:custom_form_attachment).each do |cfv|
            html_doc.css("img").each do |img|
              attachment_name = cfv.custom_form_attachment.attachment_file_name.parameterize.underscore
              img_alt = img.attributes["alt"]&.value&.parameterize&.underscore
              if img_alt == attachment_name
                au = AttachmentUpload.create(
                  attachment: cfv.custom_form_attachment.attachment,
                  attachable_type: "HelpTicketComment",
                  company_id: cfv.company_id
                )
                img.set_attribute('src', "https:#{au.attachment.url}")
                img.set_attribute('target', "_blank")
                img.set_attribute('attachmentId', au.id)
              end
            end
          end
          description_value.update_columns(value_str: html_doc.to_html)
        else
          description
        end
      else
        description
      end
    end
  end

  desc "Update ticket creator if company user exists"
  task update_ticket_creator: :environment do
    CompanyUser.find_each do |cu|
      cu.update_help_ticket_creator
    end
  end

  desc "Fix help ticket comments images size"
  task fix_help_ticket_comment_images_size: :environment do
    HelpTicketComment.find_each do |htc|
      if htc.comment_body&.include?("MsoNormal" && "header_images")
        html = EmailReplyParser.parse_reply(htc.comment_body)
        html = Nokogiri::HTML html
        html.css("img").each do |img|
          if img.attributes['src'].present? && img.attributes['src'].value.include?("header_images") && !img.attributes['style']
            img.set_attribute("style", "width:100px")
          end
        end
        html = html.inner_html
        html = help_paragraph_formatted(html)&.delete("\u0000")
        htc.comment_body = html
        htc.save!
      end
    end
  end

  desc "Updates status to be closed"
  task close_tickets: :environment do
    abort("Must supply subdomain of company") unless ARGV[1]
    abort("Must supply status") unless ARGV[2]

    company = Company.find_by(subdomain: ARGV[1])
    status = ARGV[2]

    company.help_tickets.find_each do |ticket|
      status_field = ticket.custom_form.custom_form_fields.find_by(label_name: 'Status')
      status_value = ticket.custom_form_values.find_by(custom_form_field: status_field)

      if status_value&.value_str != status
        payload = {
          value: status,
          custom_form_field_id: status_field.id,
        }

        value_update = CustomForms::ValueUpdate.new(ticket, nil)
        value_update.call(payload)

        activity_data = {
          previous_value: status_value&.value_str,
          current_value: status,
          activity_label: status_field.label_name,
          ticket_subject: ticket.subject,
          ticket_number: ticket.ticket_number
        }

        activity_params = {
          help_ticket_id: ticket.id,
          owner_id: nil,
          activity_type: 'status',
          data: activity_data
        }

        HelpTicketActivity.create(activity_params)
      end
    end
  end

  desc "Add source for email created help tickets and comments"
  task add_email_tickets_and_comments_source: :environment do
    HelpTicket.where.not(message_id: nil).each do |ticket|
      ticket.update_columns(source: :email)
    end

    HelpTicketComment.where.not(message_id: nil).each do |comment|
      comment.update_columns(source: :email)
    end
  end

  desc "Remove description field from company preferences."
  task remove_description_preference: :environment do
    Company.find_each do |company|
      pref = company.help_ticket_preference
      if pref.present? && pref.preference.present?
        desc_field = pref.preference.find{|e| e['field_name'] == 'description'}
        if desc_field.present?
          pref.preference.delete(desc_field)
          pref.save!
        end
      end
    end
  end

  desc "It will add module type and module id value for nil in custom form values"
  task add_missing_module_to_custom_form_values: :environment do
    CustomFormValue.where( module_id: nil).where.not(help_ticket_id: nil).find_each do |value|
      value.update_columns(module_type: 'HelpTicket', module_id: value.help_ticket_id)
    end
  end

  desc "It will update s3 url for kumi-na company helpdesk attachment to s3 object url for preventing the red screen error (Deceptive site ahead)"
  task update_object_urls_for_kumi: :environment do
    company_ht_ids = Company.find_by_subdomain("kumi-na").help_tickets
    help_ticket_comments = HelpTicketComment.where(help_ticket_id: company_ht_ids)
    help_ticket_comments.find_each do |comment|
      if comment.comment_body&.include?("s3.amazonaws.com/nulodgic")
        updated_value = comment.comment_body.gsub(/s3.amazonaws.com\/nulodgic-(\w+)/, 'nulodgic-\1.s3.amazonaws.com')
        comment.update_column('comment_body', updated_value)
      end
    end
  end

  desc "It will update s3 url for all companies helpdesk attachment to s3 object url for preventing the red screen error (Deceptive site ahead)"
  task update_object_urls: :environment do
    Company.all.find_each do |company|
      company_ht_ids = company.help_tickets
      help_ticket_comments = HelpTicketComment.where(help_ticket_id: company_ht_ids)
      help_ticket_comments.find_each do |comment|
        if comment.comment_body&.include?("s3.amazonaws.com/nulodgic")
          updated_value = comment.comment_body.gsub(/s3.amazonaws.com\/nulodgic-(\w+)/, 'nulodgic-\1.s3.amazonaws.com')
          comment.update_column('comment_body', updated_value)
        end
      end
    end
  end

  desc "It will add default status and priorty where they are not present"
  task add_missing_status_and_priorty_to_custom_form_values: :environment do
    no_status = []
    no_priority = []

    HelpTicket.all.find_each do |ticket|
      status = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status' })&.value_str
      no_status << [ticket, 'status'] if status.blank?

      priority = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'priority' })&.value_str
      no_priority << [ticket, 'priority'] if priority.blank?
    end

    status_priority_tickets = (no_status + no_priority).uniq

    status_priority_tickets.each do |ticket|
      custom_form = ticket[0].custom_form
      priority_field = custom_form.custom_form_fields.find_by(name: 'priority')
      status_field = custom_form.custom_form_fields.find_by(name: 'status')

      if ticket[1] == 'status'
          CustomFormValue.create(
            custom_form_field_id: status_field.id,
            value_str: status_field.default_value || 'Open',
            custom_form_id: custom_form.id,
            company_id: ticket[0].company.id,
            module_type: "HelpTicket",
            module_id: ticket[0].id
          )
      elsif ticket[1] == 'priority'
          CustomFormValue.create(
            custom_form_field_id: priority_field.id,
            value_str: priority_field.default_value || 'low',
            custom_form_id: custom_form.id,
            company_id: ticket[0].company.id,
            module_type: "HelpTicket",
            module_id: ticket[0].id
          )
      end
    end
  end

  desc "It will delete the value_str of created_by field which has both value_str and value_int values"
  task update_values_created_by_field_with_multiple_values: :environment do
    help_tickets = HelpTicket.includes(custom_form_values: :custom_form_field).where(custom_form_fields: {name: "created_by"}).where.not(custom_form_values: {value_str: nil, value_int: nil})
    help_tickets.find_each do |ht|
      created_by_field = ht.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "created_by" })
      if created_by_field.present?
        created_by_field.update(value_str: nil)
      end
    end
  end

  desc "Migrate tickets from It General Request form to Base Ticket form"
  # This task is only for companies with a single workspace
  task :migrate_tickets_form, [:subdomain] => :environment do |t, args|
    company = Company.find_by(subdomain: args[:subdomain])
    if company.present?
      from_form = company.custom_forms.helpdesk.find_by(form_name: "IT General Request")
      to_form = company.custom_forms.helpdesk.find_by(form_name: "Base Ticket")

      if to_form.present? && from_form.present?
        company.help_tickets.where(custom_form_id: from_form.id).find_each do |ticket|
          ticket.custom_form_values.find_each do |cfv|
            from_form_field = from_form.custom_form_fields.find_by(field_attribute_type: cfv.custom_form_field.field_attribute_type, name: cfv.custom_form_field.name)
            if from_form_field.name == 'raise_this_request_on_behalf_of'
              to_field_name = 'followers'
            else
              to_field_name = cfv.custom_form_field.name
            end
            to_form_field = to_form.custom_form_fields.find_by(field_attribute_type: cfv.custom_form_field.field_attribute_type, name: to_field_name)
            if to_form_field.present? && to_form_field.id.present?
              cfv.update_columns(custom_form_field_id: to_form_field.id, custom_form_id: to_form.id)
            end
          end
          ticket.update_columns(custom_form_id: to_form.id)
        end
      end
    end
  end

  desc "It will add missing priority, description and attachments"
  task add_missing_values_to_tickets: :environment do
    HelpDesk::ValuesUpdateFromEmail.new().call
  end

  desc "It will add missing email attachment for the given ids"
  task add_missing_email_attachment_to_tickets: :environment do
    HelpDesk::EmailAttachmentsCreate.new().call
  end

  desc "It will close all archived tickets that are open"
  task :close_archived_tickets, [:subdomain] => :environment do |t, args|
    company = Company.find_by(subdomain: args[:subdomain])
    if company.present?
      ticks = company.help_tickets.where(archived: true).unclosed
      ticks.find_each do |tick|
        tick_fields = tick&.custom_form&.custom_form_fields
        if tick_fields.present?
          status_field = tick_fields.find_by(name: 'status')
          status_value = tick.custom_form_values.find_by(custom_form_field: status_field)
        end

        if status_value.present?
          status_value.update_columns(value_str: "Closed")
        end
      end
    end
  end

  desc "Helpdesk Faq convert into Article"
  task faq_convert_article: :environment do
    Company.all.each do |company|
      company.helpdesk_faqs.each do |faq|
        if faq.company.admins.company_users.present?
          new_article = company.default_workspace.articles.find_or_initialize_by(
            title: Nokogiri::HTML.parse(faq.question_body).text,
            body: faq.answer_body,
            company_id: faq.company_id,
            company_user_id: faq.company.admins.company_users.first.id
          )
          new_article.category = faq.category.name
          admin_group_id = company.groups.find_by(name: 'Admins').contributor_id
          everyone_group_id = company.groups.find_by(name: 'Everyone').contributor_id
          helpdesk_group_id = faq.workspace.workspace_agents.contributor_id
          new_article.article_privileges << ArticlePrivilege.new(
            contributor_id: admin_group_id,
            permission_type: "write"
          )
          new_article.article_privileges << ArticlePrivilege.new(
            contributor_id: everyone_group_id,
            permission_type: "read"
          )
          new_article.article_privileges << ArticlePrivilege.new(
            contributor_id: helpdesk_group_id,
            permission_type: "write"
          )
          new_article.save!
        end
      end
    end
  end

  desc "Helpdesk linked Faq convert into Article"
  task linked_faq_convert_article: :environment do
    Company.all.each do |company|
      protocol = Rails.env.production? || Rails.env.staging? ? 'https://' : 'http://'
      port = ''

      if Rails.env.development?
        port = ':3000'
      end

      company.articles.each do |article|
        doc = Nokogiri::HTML article.body
        doc.css("a")&.each do |link|
          if link["href"].include?("/help_tickets/faqs")
            faq_id = link["href"]&.split("/")&.last&.to_i
            faq = HelpdeskFaq.find_by(id: faq_id)
            if faq.present?
              faq_title_to_text = Nokogiri::HTML.parse(faq.question_body).text
              faq_title_make_slug = faq_title_to_text.parameterize
              faq_article_title = company.articles.find_by(slug: faq_title_make_slug)
              if faq_article_title.present?
                link.attributes["href"].value = "#{protocol}#{article.company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/help_tickets/articles/#{faq_title_make_slug}"
              end
            end
          end
        end
        updated_body = doc.search('body').inner_html
        article.update_columns(body: updated_body)
      end

      company.help_tickets.find_each do |ticket|
        ticket.help_ticket_comments.find_each do |comment|
          com = Nokogiri::HTML comment.comment_body
          com.css("a")&.each do |link|
            if link["href"].include?("/help_tickets/faqs")
              faq_id = link["href"]&.split("/")&.last&.to_i
              faq = HelpdeskFaq.find_by(id: faq_id)
              if faq.present?
                faq_title_to_text = Nokogiri::HTML.parse(faq.question_body).text
                faq_title_make_slug = faq_title_to_text.parameterize
                faq_article_title = Article.find_by(slug: faq_title_make_slug)
                if faq_article_title.present?
                  link.attributes["href"].value = "#{protocol}#{ticket.company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/help_tickets/articles/#{faq_title_make_slug}"
                end
              end
            end
          end
          updated_body = com.search('body').inner_html
          comment.update_columns(comment_body: updated_body)
        end
      end

      company.custom_forms.find_each do |form|
        form.custom_form_fields.where(field_attribute_type: "rich_text").find_each do |field|
          field.custom_form_values.find_each do |value|
            doc = Nokogiri::HTML value.value_str
            doc.css("a")&.each do |link|
              if link["href"].include?("/help_tickets/faqs")
                faq_id = link["href"]&.split("/")&.last&.to_i
                faq = HelpdeskFaq.find_by(id: faq_id)
                if faq.present?
                  faq_title_to_text = Nokogiri::HTML.parse(faq.question_body).text
                  faq_title_make_slug = faq_title_to_text.parameterize
                  faq_article_title = Article.find_by(slug: faq_title_make_slug)
                  if faq_article_title.present?
                    link.attributes["href"].value = "#{protocol}#{form.company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/help_tickets/articles/#{faq_title_make_slug}"
                  end
                end
              end
            end
            updated_body = doc.search('body').inner_html
            value.update_columns(value_str: updated_body)
          end
        end
      end
    end
  end

  desc "Creates help-tickets through a CSV file"
  task :create_csv_help_tickets, [:subdomain, :file_url, :lists_number] => :environment do |t, args|
    csv = nil
    if Rails.env.development?
      file_path = args[:file_url]
      unless File.exist?(file_path)
        exit "File not found at '#{file_path}'; exiting..."
      end
      csv = CSV.new(File.read(file_path), headers: true, header_converters: :symbol, converters: :all)
    else
      uri = URI.parse(args[:file_url])
      key = Addressable::URI.encode(File.basename(uri.path)).gsub("+", " ")
  
      s3 = Aws::S3::Client.new(
        region: Rails.application.credentials.aws[:s3][:region],
        access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
        secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
      )
  
      s3_response = s3.get_object(bucket: "nulodgic-static-assets", key: key)
      csv = CSV.new(s3_response.body.read, :headers => true, :header_converters => :symbol, :converters => :all) if s3_response.present?
    end
    com = Company.find_by(subdomain: args[:subdomain])

    form = com.custom_forms.find_by(default: true, company_module: "helpdesk") if com.present?
    exit "No default form found; exiting..." unless form
    csv_hash = nil
    csv_hash = csv.to_a.map {|row| row.to_hash } if csv.present?
    exit "Unable to load csv data; exiting..." unless csv_hash
    
    field_ids = form.custom_form_fields.each_with_object({}) do |field, hash|
      hash[field['name']] = field.id
    end

    csv_hash.each do |data|
      begin
        ticket = form.help_tickets.new(company_id: com.id, workspace_id: form.workspace_id)
        
        if data[:subject].present? && field_ids['subject']
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['subject'], value_str: data[:subject])
          value.skip_event_trigger = true
        else
          putc "d"
          next
        end

        creator = nil 
        if data[:created_by].present? && field_ids['created_by']
          creator = com.company_users.joins(:user).find_by("users.email = ?", (data[:created_by]&.squish))
          if !creator.present?
            creator = Guest.find_or_create_by(email: data[:created_by]&.squish, company_id: form.company_id, workspace_id: form.workspace_id)
          end
        end
        if creator.present?
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['created_by'])
          value.value_int = creator.contributor_id
          value.skip_event_trigger = true
        else
          putc "c"
          next
        end

        assignee = nil
        if data[:assigned_to].present? && field_ids['assigned_to']
          assignee = com.company_users.joins(:user).find_by("users.email = ?", data[:assigned_to]&.squish)
          assignee ||= Guest.find_or_create_by(email: data[:assigned_to]&.squish, company_id: form.company_id, workspace_id: form.workspace_id)
          if assignee.present?
            value = ticket.custom_form_values.new(custom_form_field_id: field_ids['assigned_to'], value_int: assignee.contributor_id)
            value.skip_event_trigger = true
          end
        end

        
        raise_this_request_on_behalf_of = nil
        if data[:raise_this_request_on_behalf_of].present? && field_ids['raise_this_request_on_behalf_of']
          raise_this_request_on_behalf_of = com.company_users.joins(:user).find_by("users.email = ?", (data[:raise_this_request_on_behalf_of]&.squish))
          if !raise_this_request_on_behalf_of.present?
            raise_this_request_on_behalf_of = Guest.find_or_create_by(email: data[:raise_this_request_on_behalf_of]&.squish, company_id: form.company_id, workspace_id: form.workspace_id)
          end
        end

        if raise_this_request_on_behalf_of.present? 
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['raise_this_request_on_behalf_of'])
          value.value_int = raise_this_request_on_behalf_of.contributor_id
          value.skip_event_trigger = true
        end

        if data[:priority].present? && field_ids['priority']
          value_exists = JSON.parse(form.custom_form_fields.find(field_ids['priority']).options).map{|x| x["name"]}.include?(data[:priority].downcase)
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['priority'], value_str: data[:priority].downcase) if value_exists
          value.skip_event_trigger = true
        end

        if data[:status].present? && field_ids['status']
          value_exists = JSON.parse(form.custom_form_fields.find(field_ids['status']).options).map{|x| x["name"]}.include?(data[:status].capitalize)
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['status'], value_str: data[:status].capitalize) if value_exists
          value.skip_event_trigger = true
        end
  
        if data[:category].present? && field_ids['category']
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['category'], value_str: data[:category])
          value.skip_event_trigger = true
        end

        if data[:description].present? && field_ids['description']
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['description'])
          value.value_str = data[:description].gsub("\n", "<br>")
          value.skip_event_trigger = true
        end

        lists_number = args[:lists_number].to_i
        (1..lists_number).each do |i|
          field_name = i == 1 ? 'list' : "list_#{i}"
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids[field_name], value_str: data[field_name.to_sym]) if data[field_name.to_sym].present? && field_ids[field_name]
          value.skip_event_trigger = true
        end

        csv_to_form_field_mapping = {
          location: 'location_list',
          tag: 'tag',
          date: 'date',
          epic_client_code: 'epic_client_code'
        }

        csv_to_form_field_mapping.each do |csv_column_name, form_field_name|
          value_from_csv = csv_column_name == :location ? data[:location] : data[csv_column_name]
          next unless value_from_csv.present? && field_ids[form_field_name]
          if form_field_name == 'location_list'
            location = CustomFormValue.find_by(module_type: 'Location', value_str: value_from_csv)&.module
            if location
              value = ticket.custom_form_values.new(custom_form_field_id: field_ids[form_field_name], value_int: location.id)
            end
          else
            value = ticket.custom_form_values.new(custom_form_field_id: field_ids[form_field_name], value_str: value_from_csv)
          end
          value.skip_event_trigger = true
        end

        ticket.skip_event_trigger = true
        ticket.skip_automated_tasks
        ticket.save!

        putc "."
      rescue => e
        puts "error: #{e.message}"
      end
    end
  end

  desc "Populate closed at, opened at and days opened field data"
  task populate_ticket_new_fields_data: :environment do
    def date_difference_in_days_sql(start_date, end_date)
      "(EXTRACT(epoch from age(#{end_date}::date, #{start_date}::date)) / 86400)::int"
    end

    HelpTicket.find_each do |ht|
      sql = """
        SELECT
         #{ht.id} AS ticket_id,
          MIN(closed_date.created_at) As date_closed,
          MIN(opened_date.created_at),  '#{ht.created_at}' As date_opened,
          open_days_value.count As asdasdasdsad,
          (CASE
            WHEN open_days_value.value_str = 'Open' THEN #{date_difference_in_days_sql("'#{ht.created_at}'", 'CURRENT_DATE')}
            ELSE COALESCE(#{date_difference_in_days_sql("'#{ht.created_at}'", 'MIN(open_status_date.created_at)')}, 0)
            END) AS days_opened
        FROM help_tickets
        LEFT JOIN help_ticket_activities AS closed_date
          ON closed_date.help_ticket_id = #{ht.id}
            AND closed_date.activity_type = 15
            AND closed_date.data->'activity_label' = 'Status'
            AND closed_date.data->'current_value' = 'Closed'

        LEFT JOIN  help_ticket_activities AS opened_date
          ON opened_date.help_ticket_id = #{ht.id}
            AND opened_date.activity_type = 15
            AND opened_date.data->'activity_label' = 'Status'
            AND opened_date.data->'current_value' = 'Open'

        LEFT JOIN custom_form_values AS open_days_value ON open_days_value.module_id = #{ht.id} AND open_days_value.value_str = 'Open'
        LEFT JOIN custom_form_fields open_days_field
          ON open_days_field.id = open_days_value.custom_form_field_id
            AND open_days_value.module_type = 'HelpTicket'
            AND open_days_field.field_attribute_type = 15
            AND open_days_field.label = 'Status'
        LEFT JOIN help_ticket_activities AS open_status_date
          ON open_status_date.help_ticket_id = #{ht.id}
            AND open_status_date.activity_type = 15
            AND open_status_date.data->'activity_label' = 'Status'
            AND open_status_date.data->'previous_value' = 'Open'
        GROUP BY open_days_value.value_str
      """

      query = ActiveRecord::Base.send(:sanitize_sql_array, [sql])
      result = ActiveRecord::Base.connection.execute(query).to_a[0]
      ht.update_columns(closed_at: result['date_closed'], opened_at: result['date_opened'], days_opened: result['days_opened'])
    end
  end

  desc "Update or delete custom form values field ids for tickets moved from another custom form"
  task update_delete_moved_ticket_fields: :environment do
    HelpTicket.find_each do |ticket|
      if ticket.custom_form_values.pluck(:custom_form_id).uniq.count > 1
        values_with_old_field_ids = ticket.custom_form_values.where.not(custom_form_id: ticket.custom_form_id)
        values_with_old_field_ids.each do |ticket_value|
          field_name = ticket_value.custom_form_field.name
          new_field = CustomFormField.find_by(custom_form_id: ticket.custom_form_id, name: field_name)
          if new_field.present?
            ticket_value.custom_form_field = new_field.id 
            ticket_value.save!
          else
            ticket_value.destroy
          end
        end
      end
    end
  end

  desc 'Update or delete custom form values field ids who has different field ids'
  task update_delete_ticket_fields: :environment do
    HelpTicket.find_each do |ticket|
      if ticket.custom_form_fields.pluck(:custom_form_id).uniq.count > 1
        invalid_fields = ticket.custom_form_fields.where.not(custom_form_id: ticket.custom_form_id)
        invalid_fields.each do |field|
          cfv = ticket.custom_form_values.where(custom_form_field_id: field.id)
          field_name = field.name
          new_field = CustomFormField.find_by(custom_form_id: ticket.custom_form_id, name: field_name)
          if new_field.present?
            cfv.update_all(custom_form_field_id: new_field.id)
          else
            cfv.destroy
          end
        end
      end
    end
  end

  desc 'Update the days opened count when ticket was closed multiple time'
  task update_days_opened_count: :environment do
    ticket_ids = HelpTicket.joins(:help_ticket_activities).group('help_tickets.id')
                .where("help_ticket_activities.data->'activity_label' = 'Status'")
                .having("COUNT(help_ticket_activities.data->'current_value' = 'Closed') > 1").pluck(:id)
    HelpTicket.where(id: ticket_ids).find_each do |ht|
      total_days = nil
      opened_date = []
      puts(ht.id)
      ht.help_ticket_activities.sort_by(&:created_at).each do |activity|
        if activity.activity_type == 'status' && activity.data['current_value'] == 'Closed' && total_days.nil?
          total_days = (activity.created_at.to_date - ht.created_at.to_date).to_i
        elsif activity.activity_type == 'status' && activity.data['current_value'] != 'Closed'
          opened_date << activity.created_at
        elsif activity.activity_type == 'status' && activity.data['current_value'] == 'Closed' && total_days.present?
          total_days += (activity.created_at.to_date - opened_date.min&.to_date).to_i if opened_date.present?
        end
      end
      ht.update_columns(days_opened: total_days) if total_days.present?
    end
  end

  desc 'Set days opened for tickets which were created with status Closed'
  task set_days_opened_for_closed_tickets: :environment do
    HelpTicket.where(days_opened: nil).find_each do |ticket|
      status = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status' }).value_str
      if status == 'Closed'
        ticket.update_columns(days_opened: 0)
      end
    end
  end

  desc 'Set duplicate status in help ticket'
  task set_duplicate_status_in_help_ticket: :environment do
    Company.all.find_each do |company|
      company.help_tickets.find_each do |ticket|
        status = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status', field_attribute_type: "status" })&.value_str
        if status.present?
          ticket.update_columns(status: status)
        end
      end
    end
  end

  desc 'Set duplicate assigned users in help ticket'
  task set_duplicate_assigned_users_in_help_ticket: :environment do
    Company.all.find_each do |company|
      company.help_tickets.find_each do |ticket|
        assigned_user_ids = ticket.custom_form_values.includes(:custom_form_field)
                            .where(custom_form_fields: { name: 'assigned_to', field_attribute_type: "people_list" })
                            &.pluck(:value_int)
        if assigned_user_ids.present?
          ticket.update_column(:assigned_user_ids, assigned_user_ids)
        end
      end
    end
  end

  desc 'Setting is_new false for the tickets that have agents assigned'
  task update_help_tickets_new_status: :environment do
    HelpTicket.where(is_new: true).update_all(is_new: false)
  end

  desc 'Setting is_seen true for the tickets that have been opened'
  task update_help_tickets_seen_status: :environment do
    HelpTicket.where(is_seen: false).update_all(is_seen: true)
  end

  desc 'Set created by/assigned to field group member ids in help ticket'
  task set_group_member_ids_in_help_ticket: :environment do
    HelpTicket.all.find_each do |ticket|
      ids_to_populate = {}

      ticket.assigned_user_ids.each do |id|
        group = Group.find_by(contributor_id: id)
        next unless group.present?
        if ids_to_populate["assigned_to"].present?
          ids_to_populate["assigned_to"] = (ids_to_populate["assigned_to"] + group.contributor.contributor_ids_only_users).uniq
        else
          ids_to_populate["assigned_to"] = group.contributor.contributor_ids_only_users
        end
      end
      if !ids_to_populate.empty?
        ticket.update_column(:group_member_ids, ids_to_populate.to_json)
      end
    end
  end

  desc 'set subjects for tickets where subjects are missing'
  task set_subject_for_tickets_missing_subjects: :environment do
    def query_result(sql_query)
      query = ActiveRecord::Base.send(:sanitize_sql_array, [sql_query, {}])
      results = ActiveRecord::Base.connection.execute(query)
      results.as_json
    end
    
    sql_query = """
      SELECT help_tickets.id
      FROM help_tickets
      LEFT JOIN custom_form_fields as subjects ON subjects.name = 'subject'
      AND subjects.custom_form_id = help_tickets.custom_form_id
      LEFT JOIN custom_form_values as subject_values
        ON subject_values.custom_form_field_id = subjects.id
        AND subject_values.module_type = 'HelpTicket'
        AND subject_values.module_id = help_tickets.id
      WHERE subject_values is null
    """
    
    ticket_ids_without_subject = query_result(sql_query).pluck('id')
    
    HelpTicket.where(id: ticket_ids_without_subject).each do |ticket|
      subject_field = ticket.custom_form.custom_form_fields.find_by(name: 'subject')
      if subject_field.present?
        ticket.skip_automated_tasks
        ticket.custom_form_values.find_or_create_by(custom_form_field_id: subject_field.id) do |value|
          value.value_str = 'Subject Missing'
        end
      end
    end
  end

  desc 'Set description value in help ticket'
  task set_description_in_ticket: :environment do
    CustomFormValue.joins(custom_form_field: :custom_form).where(custom_form: { company_module: 'helpdesk' }, custom_form_field: { name: 'description', field_attribute_type: 'rich_text' }).find_each do |fv|
      if fv.module.present?
        fv.module.update_column(:description, fv.value_str)
      end
    end
  end

  desc 'Set subject value in help ticket table'
  task set_subject_in_ticket: :environment do
    CustomFormValue.joins(custom_form_field: :custom_form).where(custom_form: { company_module: 'helpdesk' }, custom_form_field: { name: 'subject', field_attribute_type: 'text' }).find_each do |fv|
      if fv.module.present?
        fv.module.update_column(:subject, fv.value_str)
      end
    end
  end

  desc 'Set priority value in help ticket table'
  task set_priority_in_ticket: :environment do
    CustomFormValue.joins(custom_form_field: :custom_form).where(custom_form: { company_module: 'helpdesk' }, custom_form_field: { name: 'priority', field_attribute_type: 'priority' }).find_each do |fv|
      if fv.module.present?
        fv.module.update_column(:priority, fv.value_str)
      end
    end
  end

  desc 'Add description body tsvector token in help ticket table'
  task add_tsvector_descitpion_in_ticket: :environment do
    CustomFormValue.joins(custom_form_field: :custom_form).where(custom_form: { company_module: 'helpdesk' }, custom_form_field: { name: 'description', field_attribute_type: 'rich_text' }).find_each do |fv|
      puts fv.id
      if fv.module.present?
        fv.module.update_column(:description_body_tokens, fv.value_str_tokens)
      end
    end
  end

  desc 'Add missing tickets status value'
  task add_missing_ticket_status: :environment do
    HelpTicket.where(status: '').find_each do |ticket|
      status_value = ticket.custom_form_values.joins(custom_form_field: :custom_form).find_by(custom_form: { company_module: 'helpdesk' }, custom_form_field: { name: 'status', field_attribute_type: 'status' }).value_str
      ticket.update_column(:status, status_value)
    end
  end

  desc "Update value_str_tokens and description_body_tokens for tickets with non-empty description_body_tokens"
  task update_tokens: :environment do
    HelpTicket.includes(custom_form_values: :custom_form_field)
              .where(custom_form_fields: { name: 'description' })
              .where.not(description_body_tokens: nil)
              .find_each(batch_size: 2000) do |ticket|
      
      puts "Processing HelpTicket #{ticket.id}"
      
      custom_form_value = ticket.custom_form_values.first
      if custom_form_value.present? && custom_form_value.value_str_tokens
        custom_form_value.populate_value_str_tokens
        ticket.update_column(:description_body_tokens, custom_form_value.value_str_tokens)
      end
    end
  end

  desc 'set assigned users and group member ids in help ticket'
  task set_assigned_users_and_group_member_ids_in_help_ticket_for_bulk_assignment: :environment do
    ht_ids = CustomFormValue.joins(:custom_form_field)
            .where(custom_form_field: { field_attribute_type: 'people_list', name: 'assigned_to'})
            .pluck(:module_id)
            .compact
    help_tickets = HelpTicket.where(id: ht_ids, assigned_user_ids: [])

    help_tickets.find_each do |ticket|
      if ticket.assigned_user_ids.empty?
        assigned_user_ids = ticket.custom_form_values.includes(:custom_form_field)
                                  .where(custom_form_fields: { name: 'assigned_to', field_attribute_type: "people_list" })
                                  &.pluck(:value_int)

        if assigned_user_ids.present?
          ticket.update_column(:assigned_user_ids, assigned_user_ids)
        end
      end

      if ticket.group_member_ids.nil?
        ids_to_populate = {}
        ticket.assigned_user_ids.each do |id|
          group = Group.find_by(contributor_id: id)
          next unless group.present?
          if ids_to_populate["assigned_to"].present?
            ids_to_populate["assigned_to"] = (ids_to_populate["assigned_to"] + group.contributor.contributor_ids_only_users).uniq
          else
            ids_to_populate["assigned_to"] = group.contributor.contributor_ids_only_users
          end
        end

        if !ids_to_populate.empty?
          ticket.update_column(:group_member_ids, ids_to_populate.to_json)
        end
      end
    end
  end

  desc 'set first_response_at value for tickets'
  task set_first_response_value: :environment do
    tickets = HelpTicket.where(first_response_at: nil).where.not(assigned_user_ids: [])
    tickets.find_each do |ticket|
      puts "Processing ticket #{ticket.id}"
      assigned_to_ids = ticket.assigned_user_ids
      group_members_ids = JSON.parse(ticket.group_member_ids || '{"assigned_to": []}')["assigned_to"] || []
      assigned_user_ids = assigned_to_ids | group_members_ids

      if assigned_user_ids.present?
        activity = HelpTicketActivity.joins(owner: :contributor)
                                     .where(activity_type: ["note", "status"], help_ticket_id: ticket.id)
                                     .where('contributors.id IN (?)', assigned_user_ids)
                                     .order(:created_at)
                                     .first
        ticket.update_column(:first_response_at, activity.created_at)  if activity.present?
      end
    rescue => e
      puts "Couldn't update ticket #{ticket.id}. Error: #{e}"
      next
    end
  end

  desc 'Delete ticket sessions that are older than 1 weeks.'
  task delete_ticket_sessions: :environment do
    DeleteTicketSessionsWorker.perform_async
  end

  desc "Copy HelpTicket 'status' column's value if it is 'Closed' in to a boolean named 'closed'"
  task set_help_ticket_closed_column_value: :environment do
    batch_size = 1000
    batch_index = 0
    HelpTicket.where(status: 'Closed').find_in_batches(batch_size: batch_size) do |batch|
      batch_index += 1
      puts "Processing batch #{batch_index} with #{batch.size} records"
      HelpTicket.where(id: batch.map(&:id)).update_all(closed: true)
    end
  end

  desc 'Set duplicate creators in help ticket'
  task set_duplicate_creators_in_help_ticket: :environment do
    batch_size = 1000
    batch_index = 0
    ticket_errors = []
    HelpTicket.find_in_batches(batch_size: batch_size) do |batch|
      batch_index += 1
      puts "Processing batch #{batch_index} with #{batch.size} records"
      batch.each do |ticket|
        begin
          creator_ids = ticket.custom_form_values
                              .includes(:custom_form_field)
                              .where(custom_form_fields: { name: 'created_by', field_attribute_type: "people_list" })
                              .where.not(value_int: nil)
                              .pluck(:value_int)
          if creator_ids.present?
            ticket.update_column(:creator_ids, creator_ids)
            puts "Ticket Updated #{ticket.id}"
          end
        rescue => e
          Rails.logger.warn "Issue updating Ticket with ID: #{ticket.id}. Error message: #{e.message}"
          ticket_errors << { ticket_id: ticket.id, error: e.message }
        end
      end
    end
    puts "Errors: #{ticket_errors}"
  end

  desc 'Denormalize data for smart lists and linkables for HelpTickets'
  task denormalize_data_for_help_tickets: :environment do
    batch_size = 5000
    ticket_errors = []
    batch_index = 0
  
    field_attribute_types = {
      location_list: { column_name: :related_location_ids, linkable_types: ["Location"] },
      vendor_list: { column_name: :related_vendor_ids, linkable_types: ["Vendor"] },
      asset_list: { column_name: :related_asset_ids, linkable_types: ["ManagedAsset"] },
      telecom_list: { column_name: :related_telecom_ids, linkable_types: ["TelecomService"] },
      contract_list: { column_name: :related_contract_ids, linkable_types: ["Contract"] },
      people_list: { column_name: [:related_people_ids, :related_group_ids], linkable_types: ["Group", "CompanyUser"] }
    }

    HelpTicket.find_in_batches(batch_size: batch_size) do |batch|
      batch_index += 1
      puts "Processing batch #{batch_index} with #{batch.size} records"
      batch.each do |ticket|
        begin
          field_attribute_types.each do |field_attribute, config|
            process_field_attribute(ticket, field_attribute, config)
          end
        rescue => e
          Rails.logger.warn "Error processing HelpTicket #{ticket.id}: #{e.message}"
          ticket_errors << { ticket_id: ticket.id, error: e.message }
        end
      end
    end
    puts "Errors: #{ticket_errors}"
  end

  desc "Creates help-tickets through an Excel file"
  task :create_excel_help_tickets, [:subdomain, :file_url, :custom_form_id] => :environment do |t, args|

    file_path = args[:file_url]
    xlsx = nil
    
    if Rails.env.development?
      unless File.exist?(file_path)
        exit "File not found at '#{file_path}'; exiting..."
      end
      xlsx = Roo::Excelx.new(file_path)
    else
      uri = URI.parse(args[:file_url])
      key = Addressable::URI.encode(File.basename(uri.path)).gsub("+", " ")

      s3 = Aws::S3::Client.new(
        region: Rails.application.credentials.aws[:s3][:region],
        access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
        secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
      )

      s3_response = s3.get_object(bucket: "nulodgic-static-assets", key: key)
      Tempfile.create(['tickets', '.xlsx']) do |temp_file|
        temp_file.binmode
        temp_file.write(s3_response.body.read)
        temp_file.rewind
        xlsx = Roo::Excelx.new(temp_file.path)
      end
    end

    com = Company.find_by(subdomain: args[:subdomain])
    exit "Company not found; exiting..." unless com

    form = com.custom_forms.find_by(id: args[:custom_form_id].to_i, company_module: "helpdesk")
    exit "No default form found; exiting..." unless form

    field_ids = form.custom_form_fields.each_with_object({}) do |field, hash|
      hash[field['name']] = field.id
    end

    xlsx.each_row_streaming(offset: 1, pad_cells: true) do |row|
      data = {
        ticket_number: row[0]&.value,
        subject: row[1]&.value,
        priority: row[2]&.value,
        status: row[3]&.value,
        created_by: row[4]&.value,
        assigned_to: row[5]&.value,
        description: row[10]&.value
      }

      begin
        ticket = form.help_tickets.new(company_id: com.id, workspace_id: form.workspace_id)

        if data[:subject].present? && field_ids['subject']
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['subject'], value_str: data[:subject])
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        else
          putc "d"
          next
        end

        creator = com.company_users.joins(:user).find_by("users.email = ?", data[:created_by]&.squish)
        creator ||= Guest.find_or_create_by(email: data[:created_by]&.squish, company_id: com.id, workspace_id: form.workspace_id)
        if creator.present?
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['created_by'], value_int: creator.contributor_id)
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        else
          putc "c"
          next
        end

        assignee = com.company_users.joins(:user).find_by("users.email = ?", data[:assigned_to]&.squish)
        assignee ||= Guest.find_or_create_by(email: data[:assigned_to]&.squish, company_id: com.id, workspace_id: form.workspace_id)
        if assignee.present?
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['assigned_to'], value_int: assignee.contributor_id)
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        end

        if data[:priority].present? && field_ids['priority']
          value_exists = JSON.parse(form.custom_form_fields.find(field_ids['priority']).options).map { |x| x["name"] }.include?(data[:priority].downcase)
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['priority'], value_str: data[:priority].downcase) if value_exists
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        end

        if data[:status].present? && field_ids['status']
          value_exists = JSON.parse(form.custom_form_fields.find(field_ids['status']).options).map { |x| x["name"] }.include?(data[:status].capitalize)
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['status'], value_str: data[:status].capitalize) if value_exists
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        end

        if data[:description].present? && field_ids['description']
          value = ticket.custom_form_values.new(custom_form_field_id: field_ids['description'], value_str: data[:description].gsub("\n", "<br>"))
          value.skip_event_trigger = true
          value.skip_survey_trigger = true
        end

        ticket.ticket_number = data[:ticket_number]
        ticket.closed = true
        ticket.skip_event_trigger = true
        ticket.skip_survey_trigger = true
        ticket.skip_automated_tasks
        ticket.save!
        putc "."
      rescue => e
        puts "error: #{e.message}"
      end
    end
  end
end

def process_field_attribute(ticket, field_attribute, config)
  ids = extract_ids(ticket, field_attribute, config[:linkable_types])
  if ids.any?
    if field_attribute == :people_list
      process_people_or_groups(ticket, ids, config)
    else
      ticket.update_column(config[:column_name], ids)
      puts "Ticket Updated #{ticket.id} for #{config[:column_name]}"
    end
  end
end

def extract_ids(ticket, field_attribute, linkable_types)
  custom_ids = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: CustomFormFieldTemplate.field_attribute_types[field_attribute.to_s] }).where.not(value_int: nil).pluck(:value_int)
  if ticket.linkable&.source_linkable_links && field_attribute != :people_list
    custom_ids += Linkable.where(id: ticket.linkable.source_linkable_links.pluck(:target_id), linkable_type: linkable_types).pluck(:linkable_id)
  end
  custom_ids
end

def process_people_or_groups(ticket, ids, config)
  linkable_ids = ticket.linkable&.source_linkable_links&.pluck(:target_id)
  people_ids = []
  group_ids = []

  if linkable_ids
    Linkable.where(id: linkable_ids, linkable_type: config[:linkable_types]).includes(:linkable).each do |linkable|
      if linkable.linkable_type == "CompanyUser"
        people_ids << linkable.linkable&.contributor_id
      elsif linkable.linkable_type == "Group"
        group_ids << linkable.linkable&.contributor_id
      end
    end
  end

  group_contributor_ids = Group.where(contributor_id: ids).pluck(:contributor_id)
  ids.each do |id|
    if group_contributor_ids.include?(id)
      group_ids << id
    else
      people_ids << id
    end
  end

  ticket.update_column(config[:column_name][0], people_ids) if people_ids.any?
  ticket.update_column(config[:column_name][1], group_ids) if group_ids.any?

  if people_ids.any? || group_ids.any?
    puts "Ticket Updated #{ticket.id} for People and Groups"
  end
end
# Need to execute a rake task to fix days opened
