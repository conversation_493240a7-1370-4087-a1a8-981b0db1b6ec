ActiveAdmin.register UpdateSubscriptionLog do
  menu parent: 'Logs', priority: 16
  breadcrumb do
    ['admin', 'logs']
  end

  scope :all, default: true
  scope("Insufficient funds") { |scope| scope.where(status: 'error') }
  scope("Updated successfully") { |scope| scope.where(status: 'success') }

  filter :status, as: :select
  filter :company_subdomain, as: :select
  filter :created_at

  actions :index, :show, :destroy

  index do
    column :company_subdomain
    column :new_plan
    column :existing_plan
    column :user_email
    column :status
    column :response do |record|
      record.status == 'error' ? "Insufficient funds" : "Updated successfully"
    end

    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
