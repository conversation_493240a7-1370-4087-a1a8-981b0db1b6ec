class Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketOptionsController < Webhooks::Integrations::Helpdesk::BaseController
  include MsTeamsOptions
  include <PERSON>TeamsHelper
  before_action :authenticate_user, if: :is_linking?

  def index
    options = fetch_options(params[:options_type])
    log_event(options, 'index', :success)
    render json: options, status: :ok
  end

  private

  def is_linking?
    params[:is_linking] == 'true'
  end

  def integration
    'MsTeams'
  end

  def custom_form
    return @custom_form if defined?(@custom_form)
    # TODO: MS teams: remove env check 
    workspace = if !ms_teams_feature_access_pending?(scoped_company.id)
                  if params[:workspace_id]
                    scoped_company.workspaces.find_by(id: params[:workspace_id])
                  else
                    scoped_company.ms_teams_configs.find_by(active: true)&.workspace
                  end
                else
                  scoped_company.default_workspace
                end
  
    return unless workspace

    @custom_form = workspace.custom_forms.active.find_by(company_module: 'helpdesk', default: true) ||
                   workspace.custom_forms.active.where(company_module: 'helpdesk').order(:created_at).first
  end
end
