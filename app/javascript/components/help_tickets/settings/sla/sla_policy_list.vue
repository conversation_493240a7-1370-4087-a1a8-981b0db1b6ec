<template>
  <div class="clearfix row">
    <sub-menu />
    <div class="col mt-5">
      <workspace-settings-banner />
      <h4
        class="font-weight-normal mb-3 h5--responsive"
        data-tc-heading="SLA Policies"
      >
        SLA Policies
      </h4>
      <p
        class="not-as-small text-secondary p--responsive"
        data-tc-paragraph="SLA Policies"
      >
        Service Level Agreement (SLA) Policies allow companies to set time limits for ticket actions according to the priority of their corresponding custom forms.
      </p>
      <div>
        <div class="box-inner w-100">
          <div class="mt-5 w-100">
            <div
              v-if="!isLoaded"
              class="py-3"
            >
              <h4>
                <span class="float-left mr-2">
                  Loading SLA Policies
                </span>
                <span>
                  <pulse-loader
                    :loading="true"
                    class="float-left"
                    color="#0d6efd"
                    size="0.5rem"
                  />
                </span>
              </h4>
            </div>
            <h4 v-else-if="!slaPolicies && isLoaded">
              No Policies, currently.
            </h4>
            <div v-else-if="isLoaded">
              <div
                v-for="formId in filteredFormIds"
                :key="formId"
              >
                <h6 class="header-text">{{ customFormNames[formId] }}</h6>
                <draggable
                  :value="slaPolicies[formId]"
                  handle=".handle"
                  animation="150"
                  class="row w-100"
                  dragover-bubble="true"
                  v-bind="dragOptions"
                  @change="updatePosition"
                >
                
                  <div
                    v-for="policy in slaPolicies[formId]"
                    :key="policy.id"
                    class="mb-4 col-12 col-md-6 col-lg-4"
                    :data-tc-policy="policy.name"
                  >
                    <card-item :value="policy"/>
                  </div>

                  <div
                    v-if="basePolicies[formId]"
                    :key="formId"
                    class="mb-4 col-12 col-md-6 col-lg-4"
                    :data-tc-policy="basePolicy(formId)"
                  >
                    <card-item 
                      :value="basePolicy(formId)" 
                      :base-policy="true"
                    />
                  </div>
                </draggable>
              </div>      
            </div>
            <div
              v-else
              class="text-center col"
            >
              <h4> No Policies found </h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
  import http from 'common/http';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import _cloneDeep from 'lodash/cloneDeep';
  import _get from 'lodash/get';
  import SubMenu from '../sub_menu.vue';
  import CardItem from './card_item.vue';
  import workspaceSettingsBanner from '../workspace_settings_banner.vue';

  export default {
    components: {
      CardItem,
      PulseLoader,
      SubMenu,
      workspaceSettingsBanner,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        slaPolicies: '',
        basePolicies: '',
        customFormNames: '',
      };
    },
    computed: {
      isLoaded() {
        return !!this.slaPolicies;
      },
      dragOptions() {
        return {
          animation: 200,
          ghostClass: "ghost",
        };
      },
      filteredFormIds() {
        return Object.keys(this.customFormNames).filter(formId => 
          this.slaPolicies[formId] || this.basePolicies[formId]
        );
      },
    },
    methods: {
      onWorkspaceChange() {
        this.fetchSlaPolicies();
      },
      basePolicy(formId) {
        return this.basePolicies[formId][0];
      },
      fetchSlaPolicies() {
        http
          .get('/sla/policies.json')
          .then((res) => {
            this.slaPolicies = res.data.slaPolicies;
            this.basePolicies = res.data.basePolicies;
            this.customFormNames = res.data.customFormNames;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading SLA Policies.`);
          });
      },
      updatePosition(data) {
        const moved = _get(data, 'moved.element');
        const newIndex = _get(data, 'moved.newIndex');
        const oldIndex = _get(data, 'moved.oldIndex');
        const customFormId = _get(data, 'moved.element.customFormId');
        let idx = -1;

        if (!moved) {
          return;
        }
        const policies = _cloneDeep(this.slaPolicies);

        policies[customFormId].splice(oldIndex, 1);
        policies[customFormId].splice(newIndex, 0, moved);

        
        const idMap = policies[customFormId].map((policy) => {
          idx += 1;
          return {
            id: policy.id,
            order: idx,
          };
        });

        http
          .put('/sla/orderings.json', { policies: idMap })
          .then((res) => {
            this.slaPolicies = res.data.slaPolicies;
            this.basePolicies = res.data.basePolicies;
            this.customFormNames = res.data.customFormNames;
            this.emitSuccess("Policy order updated successfully");
          })
          .catch(() => {
            this.emitError("Sorry, an error occurred during update.");
          });
      },
    },
  };
</script>

<style scoped lang="scss">
  .ghost {
    background-color: color.adjust($primary, $alpha: -0.9) !important;
    background-image: none !important;
    border: 2px dashed $gray-500;
    border-radius: $border-radius * 2;
    margin-top: -2px; // Offset the border
    opacity: 1;

    > *, &:after, &:before {
      opacity: 0 !important;
      visibility: hidden;
      transition: none;
    }
  }
  
  .header-text {
    font-size: 18px;
    margin-bottom: 15px;
  }
</style>
