class Integrations::Meraki::SaveAllClientsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include MerakiSyncData
  include ReadReplicaDb

  sidekiq_options queue: 'long_integrations'

  def perform(credential_id, first_time_flag, is_resyncing = false, log_id = nil, company_user_id = nil)
    @company_user_id = company_user_id
    @log_id = log_id
    @credential_id = credential_id
    @starting_time = Time.current

    set_read_replica_db do
      @meraki_config = Integrations::Meraki::Config.find(credential_id)
      @current_company = @meraki_config.company
      @company_integration = @meraki_config.company_integration
      @intg_id = Integration.find_by_name("meraki").id
    end

    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    save_all_clients
    intg_duration_analysis(@current_company.id, first_time_flag)
  end

  def save_all_clients
    clients&.each_slice(500) do |clients_info|
      Integrations::Meraki::SaveClientsWorker.perform_async(clients_info, @credential_id, @first_time_flag, @is_resyncing, @log_id, @company_user_id)
    end
  end

  def log_exception(exception, discovered_asset)
    Rails.logger.error(exception)
    Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !exception.message.include?("Managed asset has already been taken")
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, 'save_clients').to_json)
  end
end
