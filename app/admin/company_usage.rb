ActiveAdmin.register Company, as: "Company Usage" do
  menu parent: 'Metrics', label: "Company Usage", priority: 6
  breadcrumb do
    ['admin', 'metrics']
  end
  actions :index

  scope :paid_companies, default: true do |companies|
    companies.joins(:subscriptions)
             .where(subscriptions: { status: ["active", "canceled"] })
             .where("subscriptions.end_date IS NULL OR subscriptions.end_date > ?", Date.today)
             .distinct
  end

  scope :expired_companies do |companies|
    companies.joins(:subscriptions)
             .where(subscriptions: { status: ["insolvent"] })
             .where("subscriptions.end_date IS NOT NULL AND subscriptions.end_date < ?", Date.today)
             .distinct
  end

  scope :trial_companies do |companies|
    companies.where("(CAST (TO_CHAR(companies.created_at, 'J') AS INTEGER) + free_trial_days) - CAST(TO_CHAR(now(), 'J') AS INTEGER) > 0")
  end

  scope :all_companies do |companies|
    companies
  end

  filter :name_or_subdomain_contains, as: :string
  filter :subscriptions_subscription_plan_id,
         as: :select, 
         label: 'Subscription Plan', 
         collection: -> { SubscriptionPlan.group(:name).select('name, MIN(id) AS id').order(:name).map { |plan| [plan.name, plan.id] } }
         
  config.sort_order = 'name_asc'

  csv do
    column :subdomain
    column ("Users") { |company| "#{company.company_users.size}" }
    column ("Assets") { |company| "#{company.managed_assets.size}" }
    column ("Discovered Assets") { |company| "#{company.discovered_assets.size}" }
    column ("Contracts") { |company| "#{company.contracts.size}" }
    column ("Vendors") { |company| "#{company.vendors.size}" }
    column ("Transactions") { |company| "#{company.general_transactions.size}" }
    column ("Telecom Providers") { |company| "#{company.telecom_providers.size}" }
    column ("Help Tickets") { |company| "#{company.help_tickets.size}" }
    column ("Locations") { |company| "#{company.locations.size}" }
    column ("Subscription Status") do |company|
      company.subscriptions.map.with_index(1) do |subscription, index|
        "#{index}. #{subscription.status}"
      end.join("\n")
    end
    column ("Subscription Plan") do |company|
      company.subscriptions.map.with_index(1) do |subscription, index|
        plan_name = subscription.subscription_plan.name
        "#{index}. #{plan_name}"
      end.join("\n")
    end
    column ("Subscription Start Date") do |company|
      company.subscriptions.map.with_index(1) do |subscription, index|
        formatted_date = subscription.start_date&.strftime('%a, %d %b %Y')
        "#{index}. #{formatted_date}"
      end.join("\n")
    end
    column ("Subscription End Date") do |company|
      company.subscriptions.map.with_index(1) do |subscription, index|
        formatted_date = subscription.end_date&.strftime('%a, %d %b %Y') || "-"
        "#{index}. #{formatted_date}"
      end.join("\n")
    end
  end

  index download_links: [:csv] do
    column ("Company"), sortable: :name do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end

    column ("Users"), sortable: "company_users_count" do |company|
      company.company_users.size
    end

    column ("Assets"), sortable: "managed_assets_count" do |company|
      company.managed_assets.size
    end

    column ("Discovered Assets"), sortable: "discovered_assets_count" do |company|
      company.discovered_assets.size
    end

    column ("Contracts"), sortable: "contracts_count" do |company|
      company.contracts.size
    end

    column ("Vendors"), sortable: "vendors_count" do |company|
      company.vendors.size
    end

    column ("Transactions"), sortable: "general_transactions_count" do |company|
      company.general_transactions.size
    end

    column ("Telecom Providers"), sortable: "telecom_providers_count" do |company|
      company.telecom_providers.size
    end

    column ("Help Tickets"), sortable: "help_tickets_count" do |company|
      company.help_tickets.size
    end

    column ("Locations"), sortable: "locations_count" do |company|
      company.locations.size
    end

    column ("Subscription Status") do |company|
      safe_join(
        company.subscriptions.map.with_index(1) do |subscription, index|
          "#{index}. #{sanitize(subscription.status)}"
        end,
        tag.br()
      )
    end
  
    column ("Subscription Plan") do |company|
      safe_join(
        company.subscriptions.map.with_index(1) do |subscription, index|
          plan_name = subscription.subscription_plan.name
          "#{index}. #{plan_name}"
        end,
        tag.br()
      )
    end
  
    column ("Subscription Start Date") do |company|
      safe_join(
        company.subscriptions.map.with_index(1) do |subscription, index|
          formatted_date = subscription.start_date&.strftime('%a, %d %b %Y')
          "#{index}. #{formatted_date}"
        end,
        tag.br()
      )
    end
  
    column ("Subscription End Date") do |company|
      safe_join(
        company.subscriptions.map.with_index(1) do |subscription, index|
          formatted_date = subscription.end_date&.strftime('%a, %d %b %Y') || "-"
          "#{index}. #{formatted_date}"
        end,
        tag.br()
      )
    end 
  end

  controller do
    include MultiCompany::GeneralScoping
    
    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def find_collection(options = {})
      if params[:order] && params[:order].split('_')[0] != "name"  
        array = params[:order].split('_count_')
        table = array[0]
        order = array[1]
        super.includes(table).select("companies.*, COUNT(#{table}.id) as #{table}_count")
          .joins("left outer join #{table} on #{table}.company_id = companies.id")
          .group("companies.id")
          .order("#{table}_count #{order}")
      else
        super
      end
    end
  end
end
