<template>
  <div class="clearfix row">
    <sub-menu />
    <div v-if="isLoading">
      <h4 class="mt-5">
        <span class="float-left mr-2">
          Loading Extensions
        </span>
        <span>
          <sync-loader
            :loading="true"
            class="float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h4>
    </div>
    <div 
      v-else
      class="col mt-5"
    >
      <template-sub-menu />
      <div>
        <workspace-settings-banner />
        <div class="box box--with-heading box--flat mt-4">
          <div class="box__heading py-3 px-4 rounded-top">
            <div class="d-flex align-items-center">
              <h5 class="font-weight-normal mb-0">
                Allowed File Types 
              </h5>
            </div>
          </div>
          <div
            v-if="emailExtensions.length"
            class="box__inner d-flex flex-wrap mt-1"
          >
            <div class="col-3">
              <span class="font-weight-normal mb-0">
                CATEGORIES:
              </span>
              <div
                class="mt-2"
              >
                <a
                  v-for="(extension, index) in emailExtensions"
                  :key="index"
                  :class="[isActiveClass(extension.categoryType) ? 'category-heading' : 'bg-themed-light']"
                  href="#"
                  class="side-menu-item align-items-center cursor-pointer mb-2 p-2"
                  @click="setCategory(extension.categoryType)"
                >
                  {{ extensionCategoryType(extension) }}
                </a>
              </div>
            </div>
            <div class="col">
              <span class="font-weight-normal mb-0">
                EXTENSIONS:
              </span>
              <div>
                <span 
                  v-for="(extension, index) in extensions"
                  :key="index"
                  class="extension-pill d-inline-block py-1 mt-2 mr-1 bg-themed-light rounded-pill"
                >
                  {{ extension }}
                  <i
                    class="category-heading badge nulodgicon-android-close ml-2 rounded-circle"
                    @click="removeExtension(extension)"
                  />
                </span>
              </div>
              <div v-if="showAddType">
                <a
                  href="#"
                  class="mt-3 btn btn-outline-primary btn-featured mr-3"
                  @click="toggleButton"
                >
                  <i class="nulodgicon-plus-round mr-1" />
                  Add File Type
                </a>
              </div>
              <div v-else>
                <hr>
                <div class="form-group">
                  <div class="d-flex">
                    <div
                      class="pl-0"
                      :class="category === 'all_extensions' ? 'col-6' : 'col-12 pr-0'"
                    >
                      <label class="text-muted required">File type extension</label>
                      <input
                        v-model="extensionType"
                        type="text"
                        class="form-control"
                      >
                      <span
                        v-if="extensionTypeError"
                        class="form-text small text-danger"
                      >
                        {{ extensionTypeError }}
                      </span>
                    </div>
                    <div
                      v-if="category === 'all_extensions'"
                      class="col-6 pr-0"
                    >
                      <label class="text-muted required">Category</label>
                      <select
                        v-model="selectedCategory"
                        class="form-control input w-100"
                        data-tc-category-id
                      >
                        <option
                          :value="null"
                          hidden
                        >
                          Select Category
                        </option>
                        <option
                          v-for="(categoryType, index) in categories"
                          :key="index"
                          :value="categoryType"
                          :data-tc-category-option="categoryType"
                        >
                          {{ categoryType }}
                        </option>
                      </select>
                      <span
                        v-if="categoryTypeError"
                        class="form-text small text-danger"
                      >
                        {{ categoryTypeError }}
                      </span>
                    </div>
                  </div>
                  <div class="d-flex justify-content-end mt-2">
                    <button
                      class="btn btn-link text-secondary mr-2"
                      @click.prevent="cancel"
                    >
                      Cancel
                    </button>
                    <submit-button
                      class="ml-2"
                      :btn-content="'Add Extension'"
                      :btn-classes="'px-4 form-create-btn'"
                      :is-saving="disabled"
                      @submit="addExtension"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapMutations } from 'vuex';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import strings from 'mixins/string';
  import SubmitButton from 'components/shared/submit_button.vue';
  import SubMenu from './sub_menu.vue';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';

  export default {
    components: {
      SubMenu,
      SyncLoader,
      SubmitButton,
      workspaceSettingsBanner,
    },
    mixins: [permissionsHelper, strings],
    data() {
      return {
        isLoading: true,
        emailExtensions: [],
        category: 'all_extensions',
        showAddType: true,
        extensionType: null,
        allExtensions: {},
        disabled: false,
        extensionTypeError: '',
        categoryTypeError: '',
        selectedCategory: null,
        categories: ['HTML File Types', 'Image Types', 'Document Types', 'Video Types', 'Compression Types', 'Audio Types', 'Misc Types'],
      };
    },
    computed: {
      extensions() {
        return this.emailExtensions.filter(e => e.categoryType === this.category)[0]?.extensions;
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus', 'setValidExtensions']),
      onWorkspaceChange() {
        this.fetchValidEmailExtensions();
      },
      fetchValidEmailExtensions() {
        this.setLoadingStatus(true);
        http.get('/valid_email_extensions.json')
          .then(res => {
            this.emailExtensions = [res.data.allExtensions, ...res.data.validEmailExtensions];
            this.setValidExtensions(res.data.allExtensions.extensions);
            this.setLoadingStatus(false);
            this.isLoading = false;
          })
          .catch((error) => {
            this.setLoadingStatus(false);
            this.isLoading = false;
            this.emitError(`Sorry, there was an error fetching valid email extensions.${error.response?.data.message}`);
          });
      },
      setCategory(category) {
        this.category = category;
        this.cancel();
      },
      extensionCategoryType(extension) {
        if (extension.categoryType === 'html_file_types') {
          return 'HTML File Types';
        }
        return this.toTitle(extension.categoryType);
      },
      addExtension() {
        if (this.validateFields()) {
          this.disabled = true;
          let categoryType = this.category;
          if (this.selectedCategory && this.category === 'all_extensions') {
            categoryType = this.toSnakecase(this.selectedCategory);
          }
          const params = { extension_type: this.extensionType, category_type: categoryType };
          this.updateExtensions(params);
        }
        else {
          this.emitError('Please correct the highlighted errors before submitting.');
        }
      },
      removeExtension(extension) {
        const filteredCategoryType = this.emailExtensions.filter(item => item.extensions.includes(extension) && item.categoryType !== 'all_extensions');
        const params = { extension_type: extension, category_type: filteredCategoryType[0]?.categoryType, remove_extension: true };
        this.updateExtensions(params);
      },
      updateExtensions(params) {
        http
        .put('/valid_email_extensions/update.json', params)
        .then(() => {
          let message = "removed";
          if (!params.remove_extension) {
            this.disabled = false;
            this.cancel();
            message = "added";
          }
          this.setValidExtensions([]);
          this.fetchValidEmailExtensions();
          this.emitSuccess(`Extension ${message} successfully.`);
        })
        .catch((error) => {
          this.emitError(`Sorry, there was an error on updating extensions. ${error.response?.data?.message}`);
          if (!params.remove_extension) {
            this.disabled = false;
          }
        });
      },
      validateFields() {
        let isValid = true;
        const executableExtensions = ['.exe', '.bat', '.sh'];
        if (!this.extensionType) {
          this.extensionTypeError = 'Please enter the extension type';
          isValid = false;
        } else if (!/^\.(?!\.)/.test(this.extensionType)) {
          this.extensionTypeError = 'Invalid extension type.';
          isValid = false;
        }
        else if (executableExtensions.includes(this.extensionType)) {
          this.extensionTypeError = 'Executable files are not allowed';
          isValid = false;
        } else {
          this.extensionTypeError = '';
        }
        if (!this.selectedCategory && this.category === 'all_extensions') {
          this.categoryTypeError = 'Please enter the category';
          isValid = false;
        } else {
          this.categoryTypeError = '';
        }
        return isValid;
      },
      cancel() {
        this.showAddType = true;
        this.selectedCategory = null;
        this.extensionType = null;
        this.categoryTypeError = '';
        this.extensionTypeError = '';
      },
      toggleButton() {
        this.showAddType = false;
      },
      isActiveClass(category) {
        return this.category === category;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .category-heading {
    background-color: $themed-dark-drawer-bg;
    color: $white;
  }
  .side-menu-item {
    font-size: 1.05rem;
  }
  .badge { 
    padding: 0.1em 0.3em;
    font-size: 52%;
  }
  .extension-pill {
    text-align: center;
    position: relative;
    padding: 0 1.875rem 0 1.25rem;
    i {
      display: none;
      position: absolute;
      right: 0.25rem;
      top: 0.375rem;
      cursor: pointer;
    }
  }

  .extension-pill:hover {
    i {
      display: inline-block;
    }
  }
  .nulodgicon-android-close {
    &:before {
      color: $white;
      font-size: 1rem;
    }
  }
</style>
