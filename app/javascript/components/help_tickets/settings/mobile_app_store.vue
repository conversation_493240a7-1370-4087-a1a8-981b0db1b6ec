<template>
  <div class="row d-flex flex-nowrap mx-1">
    <div class="mt-5 mr-3 w-100">
      <div class="box box--with-heading w-100">
        <h5 class="box__heading">
          Mobile App
        </h5>
        <div class="d-flex flex-row box__inner inner p-0">
          <div class="d-flex align-items-center justify-content-center qr-figure">
            <div
              class="app-logo border bg-themed-box-bg d-flex position-absolute justify-content-center"
            >
              <img 
                src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/playstore-appstore-combined.png" 
                class="dynamic-img"
              >
            </div>
            <vue-qrcode
              :value="qrSource"
              :options="{ width: 275 }"
            />
          </div>
          <div class="d-flex flex-column justify-content-between pl-3">
            <div class="pt-3">
              <h4 class="font-weight-light mt-2">
                Mobile App for IT Admins and Help Desk Agents
              </h4>
              <p class="mt-3 text-muted description-size">
                Take the power of your help desk with you. With the Genuity Help Desk App, you can provide outstanding support wherever you are and make your users delighted. Aggregate questions from multiple channels and respond to them easily from your phone.
              </p>
            </div>
            <div class="pl-3 d-flex row app-links">
              <a 
                href="https://apps.apple.com/us/app/genuity-help-desk/id1665603044" 
                target="blank" 
                rel="noopener noreferrer"
                class="d-flex button px-3 py-1 mr-3 align-items-center box"
              >
                <img 
                  src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/apple-logo.png" 
                  alt="Apple store logo"
                  class="logo mr-3"
                >
                <div class="d-flex flex-column">
                  <p class="p-0 smallest mb-0">
                    Download on the
                  </p>
                  <h5 class="p-0 m-0">
                    App Store
                  </h5>
                </div>
              </a>
              <a 
                href="https://play.google.com/store/apps/details?id=com.genuityhd&hl=en" 
                target="blank" 
                rel="noopener noreferrer"
                class="d-flex button px-3 py-1 align-items-center box"
              >
                <img 
                  src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/google-play-store.png" 
                  alt="Play store logo"
                  class="logo mr-3"
                >
                <div class="d-flex flex-column">
                  <p class="p-0 smallest mb-0">
                    Get it on
                  </p>
                  <h5 class="p-0 m-0">
                    Google Play
                  </h5>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import VueQrcode from '@chenfengyuan/vue-qrcode';

  export default {
    components: {
      VueQrcode,
    },
    data() {
      return {
        qrSource: '',
      };
    },
    created() {
      const projectMode = window.rails_environment || '';

      switch (projectMode) {
        case 'development':
          this.qrSource = 'http://secure.localhost:3000/app-store-redirect';
          break;
        case 'staging':
          this.qrSource = 'https://secure.gogenuity-staging.com/app-store-redirect';
          break;
        case 'production':
          this.qrSource = 'https://secure.gogenuity.com/app-store-redirect';
          break;
        default:
          break;
      }
    },
  };
</script>

<style lang="scss" scoped>
  .description-size {
    font-size: 0.9rem;
  }

  .button {
    background-color: black;
    border-radius: 0.5rem;
    color: white;
    transition: transform 0.2s ease; 
  }

  .button:hover {
    transform: translateY(-2px);  
  }

  .logo {
    width: 1.5rem;
    height: auto;
    display: block;
    object-fit: contain;
  }

  .qr-code {
    width: 15rem;
    height: auto;
  }

  .dynamic-img {
    width: 3.75rem;
    height: auto;
  }

  .inner-box-padding {
    padding: 0% !important;
  }

  .app-links {
    margin-bottom: 1.75rem;
  }

  .qr-figure {
    padding-left: 0.25rem;
  }
</style>
