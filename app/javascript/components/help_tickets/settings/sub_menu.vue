<template>
  <div
    v-if="!isWorkspacesRoute"
    class="col-auto pr-3 mt-5 bottom-spacing"
  >
    <router-link
      data-id="tc-general"
      class="side-menu-item"
      to="/settings/general"
      :class="isSettingsRoute(['/settings/general', '/settings', '/settings/'])"
    >
      General
    </router-link>

    <span data-tc="help center">
      <router-link
        id="help_center_callout"
        to="/settings/help_center"
        class="side-menu-item"
        :class="{ 'is-active': isHelpCenterInfoRoute || (isHelpCenterTreeRoute && !isHelpCenterOpen) }"
        @click.native.stop.prevent="navigateToHelpCenterInfo"
      >
        <i
          :class="{
            'nulodgicon-arrow-right-b d-inline-block': !isHelpCenterOpen,
            'nulodgicon-arrow-down-b d-inline-block': isHelpCenterOpen,
          }"
        /> Public Help Center
        <div class="side-menu-item__sub-label small text-muted">(Portal, Knowledge Base, FAQs)</div>
      </router-link>

      <span
        v-if="isHelpCenterCustomDomainRoute"
        class="beta-badge ml-2"
      >
        Beta
      </span>
      <div
        class="sub-menu-items position-relative ml-3 pl-2"
        :class="{ opened: isHelpCenterOpen }"
      >
        <router-link
          to="/settings/help_center/display"
          class="side-menu-item--small d-flex align-items-middle"
          :class="helpCenterDisplayRouteClass"
          data-tc="display settings"
        >
          Display Settings
        </router-link>
        <router-link
          to="/settings/help_center/custom_domain"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isCustomDomainRoute"
        >
          Custom Domain
        </router-link>
      </div>
    </span>

    <router-link
      data-tc-custom-settings
      class="side-menu-item mt-3"
      to="/settings/custom_forms"
      :class="isActiveClass('/settings/custom_forms')"
    >
      Custom Forms
    </router-link>


    <span>
      <span
        v-if="!$isSampleCompany"
        class="side-menu-item"
        :class="{ 'is-active': (isEmailOpenRoute && !isEmailOpen) }"
        @click="toggleEmailOpen"
      >
        <i
          :class="[isEmailOpen ? 'nulodgicon-arrow-down-b' : 'nulodgicon-arrow-right-b', 'd-inline-block']"
        /> Email
      </span>

      <div
        class="sub-menu-items position-relative ml-3 pl-2"
        :class="{ opened: isEmailOpen }"
      >
        <router-link
          to="/settings/email_setting"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isEmailGeneralRoute"
        >
          General Email Settings
        </router-link>
        <router-link
          to="/settings/email_notifications"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isEmailNotificationRoute"
        >
          Notifications
        </router-link>
        <router-link
          to="/settings/helpdesk_email_format"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isEmailFormat"
        >
          Email Format
        </router-link>
        <router-link
          to="/settings/email_templates/end_users"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isEmailTemplateRoute"
        >
          Templates
        </router-link>
        <router-link
          to="/settings/blocked_mails"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isBlockedEmailDomain"
        >
          Blocked Emails &amp; Domains
        </router-link>
        <router-link
          to="/settings/blocked_keywords"
          class="side-menu-item--small d-flex align-items-middle"
          :class="isBlockedKeyword"
        >
          Blocked Email Keywords
        </router-link>
      </div>
    </span>

    <span>
      <span
        class="side-menu-item mt-3"
        @click="toggleResourcesOpen"
      >
        <i
          :class="[isResourcesOpen ? 'nulodgicon-arrow-down-b' : 'nulodgicon-arrow-right-b', 'd-inline-block']"
        /> Resources
      </span>

      <div
        class="sub-menu-items position-relative ml-3 pl-2"
        :class="{ opened: isResourcesOpen }"
      >
        <router-link
          to="/articles"
          class="side-menu-item--small d-flex align-items-middle"
        >
          Knowledge Base
        </router-link>
        <router-link
          to="/responses"
          class="side-menu-item--small d-flex align-items-middle"
        >
          Canned Responses
        </router-link>
        <router-link
          to="/task_checklists"
          class="side-menu-item--small d-flex align-items-middle"
        >
          Task Checklists
        </router-link>
        <router-link
          v-if="isWrite"
          to="/surveys"
          class="side-menu-item--small d-flex align-items-middle"
        >
          Surveys
        </router-link>

        <router-link
          to="/documents"
          class="side-menu-item--small d-flex align-items-middle"
        >
          Documents
        </router-link>
        <router-link
          to="/faqs"
          class="side-menu-item--small d-flex align-items-middle"
        >
          FAQ
        </router-link>
      </div>
    </span>
    
    <router-link
      v-if="isWrite"
      class="side-menu-item mt-3"
      to="/settings/sla/policies"
      :class="isActiveClass('/settings/sla/policies')"
      data-tc-tab="SLA Policy"
    >
      SLA Policy
    </router-link>
    <router-link
      v-if="isWrite"
      class="side-menu-item mt-3"
      to="/settings/business_hour_settings"
      :class="isActiveClass('/settings/business_hour_settings')"
    >
      Work Schedule
    </router-link>
    <router-link
      v-if="isWrite"
      class="side-menu-item mt-3"
      to="/settings/categories"
      data-tc-categories-tab
      :class="isActiveClass('/settings/categories')"
    >
      Categories
    </router-link>
    <router-link
      to="/settings/valid_email_extensions"
      class="side-menu-item mt-3"
      :class="isActiveClass('/settings/valid_email_extensions')"
    >
      Allowed File Types
    </router-link>
    <router-link
      v-if="!$isSampleCompany"
      class="side-menu-item mt-3"
      to="/settings/connectors"
      :class="isActiveClass('/settings/connectors')"
    >
      Connectors
    </router-link>
    <router-link
      class="side-menu-item mt-3"
      to="/settings/mobile_app_store"
      :class="isActiveClass('/settings/mobile_app_store')"
    >
      Mobile App
    </router-link>
    <router-link
      v-if="!$isSampleCompany && !isProductionENV"
      class="side-menu-item mt-3"
      to="/settings/desktop_app"
      :class="isActiveClass('/settings/desktop_app')"
    >
      Desktop App
    </router-link>
  </div>
</template>

<script>
  import permissionsHelper from 'mixins/permissions_helper';
  import { mapGetters, mapMutations } from 'vuex';

  export default {
    mixins: [permissionsHelper],
    props: ["active"],
    data() {
      return {
        isResourcesOpen: false,
        isProductionENV: window.rails_environment === 'production',
      };
    },
    computed: {
      ...mapGetters(['isEmailOpen', 'isHelpCenterOpen' ]),
      helpCenterDisplayRouteClass() {
        return {
          'is-active': this.isHelpCenterDisplayRoute,
        };
      },
      isHelpCenterDisplayRoute() {
        return this.$route.name === 'help_center_display';
      },
      isHelpCenterInfoRoute() {
        return this.$route.name === 'help_center';
      },
      isHelpCenterCustomDomainRoute() {
        return this.$route.name === 'custom_domain';
      },
      isHelpCenterTreeRoute() {
        return this.isHelpCenterDisplayRoute || this.isHelpCenterInfoRoute || this.isHelpCenterCustomDomainRoute;
      },
      isCustomDomainRoute() {
        return {
          'is-active': (this.isHelpCenterCustomDomainRoute),
        };
      },
      isWorkspacesRoute() {
        return this.$route.path.match(/workspaces/);
      },
      isEmailGeneralRoute() {
         return {
          'is-active': (this.$route.name === 'email-setting'),
        };
      },
      isEmailNotificationRoute() {
        return {
          'is-active': (this.$route.name === 'email-notifications'),
        };
      },
      isEmailFormat() {
        return {
          'is-active': (this.$route.name === 'email-format'),
        };
      },
      isEmailTemplateRoute() {
        return {
          'is-active': (this.$route.name === 'email-templates'),
        };
      },
      isBlockedKeyword() {
        return {
          'is-active': (this.$route.name === 'help-tickets-blocked-keywords'),
        };
      },
      isBlockedEmailDomain() {
        return {
          'is-active': (this.$route.name === 'help-tickets-blocked-mails'),
        };
      },
      isEmailOpenRoute() {
        return ['email-notifications', 'email-templates', 'help-tickets-blocked-keywords', 'help-tickets-blocked-mails', 'email-setting', 'email-format'].includes(this.$route.name);
      },
    },
    methods: {
      ...mapMutations(['setIsEmailOpen', 'setIsHelpCenterOpen']),
      onWorkspaceChange() {
        if (!this.isWrite) {
          this.$router.push("/");
        }
      },
      toggleEmailOpen() {
        const value = !this.isEmailOpen;
        this.setIsEmailOpen(value);
      },
      toggleResourcesOpen() {
        this.isResourcesOpen = !this.isResourcesOpen;
      },
      isCurrentPath(path) {
        return this.$route.path.match(path) || this.$route.path === `${path}/`;
      },
      isActiveClass(path) {
        return { 'is-active': this.isCurrentPath(path) };
      },
      isSettingsRoute(input) {
        const paths = Array.isArray(input) ? input : [input];
        return { 'router-link-exact-active router-link-active is-active': paths.includes(this.$route.path) };
      },
      navigateToHelpCenterInfo() {
        const value  = !this.isHelpCenterOpen;
        this.setIsHelpCenterOpen(value);
        if (!this.isHelpCenterTreeRoute) {
          this.$router.push("/settings/help_center");
        }
      },
    },
  };
</script>
<style lang="scss">
  .sub-menu-items {
    border-left: 1px solid $themed-fair;
    transform: scaleY(0);
    transform-origin: top;
    transition: $transition-base;
    transition-timing-function: ease;
    max-height: 0;
    overflow: hidden;

    &.opened {
      transform: scaleY(1);
      max-height: 25rem;
    }
  }

  .side-menu-item--small {
    padding: 0.45rem 0.75rem;

    .side-menu-icon {
      line-height: unset;
      height: 1.375rem;
    }
  }

  .beta-badge {
    background-color: #ff8201;
    border-radius: 1rem;
    font-size: 12px;
    padding: 2px;
    color: white;
    position: absolute;
    left: 10rem;
    top: 8.2rem;
    z-index: 1;
  }

  .bottom-spacing {
    margin-bottom: 10rem;
  }
</style>
