<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="row">
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="Login to your Mosyle account"
        >
          <p class="not-as-small mt-3">
            <ol>
              <li>Login to Mosyle portal using an account with admin priviliges.</li>
              <li>Navigate to <strong>Organization</strong>.</li>
              <li>Next, go to the <strong>Integrations</strong> dropdown and select <strong>Mosyle API Integration</strong>.</li>
              <li>Click on <strong>Add new token</strong> to create an API token for your account.</li>
              <li>Once the token is created, ensure you <strong>copy it</strong> immediately.</li>
            </ol>
          </p>
        </step-instruction-box>

        <step-instruction-box
          step-number="2"
          step-title="Fill out the following information"
        >
          <form
            class="mt-4"
            @submit.prevent="submitMosyleForm"
          >
            <p
              v-if="errors.length > 0"
              class="col-12 small text-danger"
            >
              <strong>Please correct the following error(s):</strong>
              <ul class="mb-0">
                <li
                  v-for="(error, index) in errors"
                  :key="`error-${index}`"
                >
                  {{ error }}
                </li>
              </ul>
            </p>
            <div class="row">
              <div class="form-group col-12">
                <label for="username">
                  Username
                </label>
                <input
                  id="username"
                  v-model="mosyleData.username"
                  class="input form-control"
                  required
                  type="text"
                  @keydown.space.prevent
                >
              </div>
              <div class="form-group col-12">
                <label for="password">
                  Password
                </label>
                <input
                  id="password"
                  v-model="mosyleData.password"
                  class="input form-control"
                  required
                  type="password"
                  @keydown.space.prevent
                >
              </div>
              <div class="form-group col-12">
                <label for="accessToken">
                  Access Token
                </label>
                <input
                  id="accessToken"
                  v-model="mosyleData.accessToken"
                  class="input form-control"
                  required
                  type="text"
                  @keydown.space.prevent
                >
              </div>
            </div>
            <div class="form-group col-12 mb-0 text-right">
              <button
                v-if="!loading"
                class="btn btn-sm btn-link text-secondary mr-2"
                @click.prevent="close"
              >
                <span>Cancel</span>
              </button>
              <button
                :disabled="loading || !dataPresent"
                class="btn btn-sm btn-primary px-3"
              >
                Submit
              </button>
            </div>
          </form>
        </step-instruction-box>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import OnboardingTitle from 'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        header: 'Integrate Mosyle',
        subHeader: 'Sync your existing asset services with Mosyle',
        imageSrc: 'https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/logos/integrations-logos/mosyle.png',
        integrationInstructions: `<li class='mt-3'> You must login with an account that has <b>Admin Privileges</b> to Mosyle.</li>
                                  <li class='mt-1'>Syncing might take up to 15 minutes.</li>`,
        mosyleData: {
          username: '',
          password: '',
          accessToken: '',
        },
        errors: [],
        loading: false,
      };
    },
    computed: {
      dataPresent() {
        return Object.values(this.mosyleData).every((val) => val);
      },
    },
    mounted() {
      this.$parent.$parent.setCondenseModal(true);
    },
    methods: {
      close() {
        this.$emit('close');
      },
      submitMosyleForm() {
        this.loading = true;
        this.errors = [];
        if (this.dataPresent) {
          http
            .post('/integrations/mosyle/configs.json', { mosyle_config: this.mosyleData })
            .then(() => {
              this.loading = false;
              this.$store.dispatch('fetchCompanyIntegrations');
              this.close();
            })
            .catch(error => {
              this.loading = false;
              this.errors.push(error.response.data.message);
            });
        } else {
          this.showErrors();
          this.loading = false;
        }
      },
      showErrors() {
        if (!this.mosyleData.username) {
          this.errors.push('Username is missing');
        }
        if (!this.mosyleData.password) {
          this.errors.push('Password is missing');
        }
        if (!this.mosyleData.accessToken) {
          this.errors.push('Access Token is missing');
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
.instance-input {
  border: 1px solid $gray-400;
  height: calc(2.25rem + 2px);
}
.instance-input input {
  height: auto;
}
.instance-input input:focus {
  box-shadow: none;
}

.instance-input label {
  color: #808080;
}
</style>
