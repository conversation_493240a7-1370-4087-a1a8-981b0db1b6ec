module MsTeamsOptions
  extend ActiveSupport::Concern
  include ContributorOptionsHelper

  attr_accessor :scoped_company

  def custom_fields_options
    fields_with_options = custom_form.custom_form_fields.where(name: ['status', 'priority']).select(:name, :options)
    options = {}
    fields_with_options.each do |field|
      options["#{field.name}_options"] = map_options(field)
    end
    options[:contributor_options] = contributor_options
    options
  end

  def contributor_field
    @contributor_field ||= custom_form.custom_form_fields.find_by_name('assigned_to')
  end

  def contributor_options
    contributors = Contributor.where(id: @selected_cont_ids)
    contributors = contributors.map do |contributor|
      if contributor.name
        {
          title: contributor.name,
          value: contributor.id
        }
      else
        nil
      end
    end
    contributors.compact
  end

  def ticket_json
    json = {}
    begin
      ticket.help_ticket_fields
        .select{ |field| HelpTicket::MS_TEAMS_FIELDS.include?(field['name']) }
        .map do |field|
          if field['name'] == 'assigned_to' && json["#{field['name']}_name"].blank?
            assigned_to_ids = help_ticket_data['assigned_to']
            unless assigned_to_ids
              assigned_to_ids = ticket.custom_form_values.where(custom_form_field_id: field['id']).pluck(:value_int).compact
            end

            contributors = Contributor.includes(:group, company_user: :user).where(id: assigned_to_ids)
            if contributors.present?
              json["#{field['name']}_name"] = contributors.map(&:name).to_sentence
              @selected_cont_ids = contributors.pluck(:id)
              json["#{field['name']}_id"] = @selected_cont_ids.join(',')
            end
          elsif field['name'] == 'created_by'
            contributor_id = field[:values].pluck('value_int').compact.first
            creator_contributor = Contributor.includes(:group, company_user: :user).where(id: contributor_id).first
            json[field['name']] = creator_contributor&.name
          elsif field['name'] == 'description'
            description = field[:values].pluck('value_str').to_sentence.gsub('<div><!--block-->', '').gsub('</div>', '')
            json[field['name']] = HtmlToMarkdownConverter.new(parse_type: 'teams', content: description).call
          else
            json[field['name']] = field[:values].pluck('value_str').to_sentence
          end
        end

      json['help_ticket_id'] = ticket.id
      json['ticket_number'] = ticket.ticket_number
      json['url'] = "#{build_company_url(ticket.company)}help_tickets/#{ticket.id}"
      json['company_subdomain'] = "#{ticket.company.subdomain}"
      json['fields_options'] = custom_fields_options
    rescue => e
      Bugsnag.notify(e, { message: e.message, ticket_id: ticket&.id, backtrace: e.backtrace })
      json = {}
    end
    json
  end

  def map_options(field)
    options = JSON.parse(field.options)

    case field.name
    when 'status'
      return options.map { |option| { title: option['name'], value: option['name'] }}
    when 'priority'
      return options.map { |option| { title: option['name'].humanize, value: option['name'] }}
    end
  end

  def company
    @company ||= scoped_company
  end
end
