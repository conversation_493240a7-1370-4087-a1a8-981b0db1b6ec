import _cloneDeep from 'lodash/cloneDeep';
import strings from 'mixins/string';
import dates from 'mixins/dates';

export default {
  mixins: [dates, strings],
  methods: {
    // Todo: Need to optimize these methods
    resetData() {
      this.scheduledTask = _cloneDeep(this.selectedTask);
      this.selectedPattern = 'daily';
      this.selectedDays =  [];
      this.option = this.isScheduledComment ? "Comment Details" : "Task Details";
      this.weekSelectError = null;
      this.taskAssignee = null;
      this.nameError =  '';
      this.descriptionError = '';
      this.assigneeError = '';
      this.endDateError = '';
      this.recurrenceEndDateError = '';
      this.recurrenceOccurancesError = '';
      this.startDateError = '';
      this.startTimeError = '';
      this.endTimeError = '';
      this.recurrenceStartDateError = '';
      this.dailyRevisionError = '';
      this.weeklyRevisionError = '';
      this.monthlyDayError = '';
      this.monthlyDayRevisionError = '';
      this.monthlyWeekRevisionError = '';
      this.yearlyRevisionError = '';
      this.yearlyDayError = '';
      this.initialStartDate = null;
      this.initialRecurrenceDate = null;
      this.scheduledTask.taskStartedAt.startTime = moment(this.scheduledTask.taskStartedAt.startTime, 'HH:mm').format('hh:mm A');
      this.scheduledTask.taskEndedAt.endTime = moment(this.scheduledTask.taskEndedAt.endTime, 'HH:mm').format('hh:mm A');
    },
    getTime(minutesDelayed=0) {
      const now = new Date();
      const later = new Date(now.getTime() + minutesDelayed * 60000);

      const hours = later.getHours().toString().padStart(2, '0');
      const minutes = later.getMinutes().toString().padStart(2, '0');
      const year = later.getFullYear().toString().padStart(2, '0');
      let month = later.getMonth() + 1;
      month = month.toString().padStart(2, '0');
      const day = later.getDate().toString().padStart(2, '0');

      const period = hours >= 12 ? 'PM' : 'AM';
      const hourIn12 = hours % 12 || 12;

      return { laterTime: `${hourIn12}:${minutes} ${period}`, laterDate: `${year}-${month}-${day}` };
    },
    validateForm() {
      let isValid = true;
      let recurrenceSelectedEndDate;
      let recurrenceSelectedStartDate;
      let initialDate;
      let endingDate;
      const currentDate = this.currentFormatedDate();
      const taskEndDate = this.scheduledTask.taskEndedAt.date;
      const taskStartDate = this.scheduledTask.taskStartedAt.date;

      const startTime24Hr = this.scheduledTask.taskStartedAt.startTime ? moment(this.scheduledTask.taskStartedAt.startTime, 'hh:mm A').format('HH:mm') : "";
      const endTime24Hr = this.scheduledTask.taskEndedAt.endTime ? moment(this.scheduledTask.taskEndedAt.endTime, 'hh:mm A').format('HH:mm') : "";
      this.scheduledTask.taskStartedAt.startTime = startTime24Hr;
      this.scheduledTask.taskEndedAt.endTime = endTime24Hr;

      const { startTime } = this.scheduledTask.taskStartedAt;
      const { endTime } = this.scheduledTask.taskEndedAt;
      const timings = this.getTime();
      const errorKey = this.scheduledTask.recurring ? 'recurrenceStartDateError' : 'startDateError';
      const currentTimeMoment = moment(timings.laterTime, 'hh:mm A');
      const startTimeMoment = moment(startTime, 'HH:mm');
      const endTimeMoment = moment(endTime, 'HH:mm');

      if (this.scheduledTask.recurring) {
        recurrenceSelectedEndDate = this.scheduledTask.recurrence.recurrenceDate.endDate;
        recurrenceSelectedStartDate = this.scheduledTask.recurrence.recurrenceDate.startDate;
        initialDate = recurrenceSelectedStartDate;
        endingDate = recurrenceSelectedEndDate;
      } else {
        initialDate = taskStartDate;
        endingDate = taskEndDate;
      }

      if (initialDate === currentDate && (startTimeMoment.isBefore(currentTimeMoment) || moment(timings.laterDate).isAfter(currentDate)) && !this.isScheduledComment) {
        this.startTimeError = `Start time must be greater than ${timings.laterTime}`;

        isValid = false;
      } else if (initialDate === endingDate && startTimeMoment.isAfter(endTimeMoment) && !this.isScheduledComment) {
        this.startTimeError = "Start time must be smaller than End time";
        this.endTimeError = "End time must be greater than Start time";
        isValid = false;
      } else if (initialDate === endingDate && startTimeMoment.isSame(endTimeMoment) && !this.isScheduledComment) {
        this.startTimeError = "Start time and End time cannot be the same on the same date";
        this.endTimeError = "End time must be greater than Start time on the same date";
        isValid = false;
      } else if (initialDate === currentDate && startTimeMoment.isSameOrBefore(currentTimeMoment) && !this.isScheduledComment) {
        this.startTimeError = "Start time must be greater than the current time";
        isValid = false;
      } else if (moment(taskStartDate).isAfter(currentDate) && (startTimeMoment.isSameOrBefore(currentTimeMoment) || startTimeMoment.isSame(currentTimeMoment)) && !this.isScheduledComment) {
        this.startTimeError = "";
      } else {
        this.startTimeError = '';
        this.endTimeError = '';
      }

      if (!this.scheduledTask.name && !this.isScheduledComment) {
        this.nameError = 'Please enter the task name';
        isValid = false;
      } else {
        this.nameError = '';
      }
      if (!this.scheduledTask.description && !this.isScheduledComment) {
        this.descriptionError = 'Please enter the task description';
        isValid = false;
      } else {
        this.descriptionError = '';
      }
      if (!this.scheduledTask.assigneeId) {
        this.assigneeError = 'Please enter the assign to';
        isValid = false;
      } else {
        this.assigneeError = '';
      }
      /// End Date Validation
      if (!this.scheduledTask.recurring && !this.isScheduledComment) {
        if (!taskEndDate) {
          this.endDateError = 'End date is required';
          isValid = false;
        } else if (taskEndDate < currentDate) {
          this.endDateError = 'End date cannot be in past';
          isValid = false;
        } else if ((taskEndDate < taskStartDate) && taskStartDate) {
          this.endDateError = 'End date cannot be before the start date';
          isValid = false;
        } else {
          this.endDateError = '';
        }
      }

      if (this.scheduledTask.recurring && !this.isEndByDateDisable) {
        if (!recurrenceSelectedEndDate) {
          this.recurrenceEndDateError = 'End date is required';
          isValid = false;
        } else if (recurrenceSelectedEndDate < currentDate) {
          this.recurrenceEndDateError = 'End date cannot be in past';
          isValid = false;
        } else if ((recurrenceSelectedEndDate < recurrenceSelectedStartDate) && recurrenceSelectedStartDate) {
          this.recurrenceEndDateError = 'End date cannot be before the start date';
          isValid = false;
        } else {
          this.recurrenceEndDateError = '';
        }
      }

      if (this.scheduledTask.recurring && !this.isOccurrencesDisable) {
        const occurances = parseInt(this.scheduledTask.recurrence.recurrenceDate.occurances, 10);
        if (Number.isNaN(occurances) || occurances === 0) {
          this.recurrenceOccurancesError = 'Must be greater or 0';
          isValid = false;
        } else {
          this.recurrenceOccurancesError = '';
        }
      }

      /// Start Date Validation
      this[errorKey] = (initialDate === currentDate && (timings.laterDate > currentDate)) ? `Start date must be from ${timings.laterDate}` : '';

      if (!this.scheduledTask.recurring) {
        if (!taskStartDate) {
          this.startDateError = 'Start date is required';
          isValid = false;
        } else if (
          (taskStartDate < currentDate && !this.initialStartDate) || 
          (taskStartDate < currentDate && this.initialStartDate && this.initialStartDate !== taskStartDate))
        {
          this.startDateError = 'Start date cannot be in past';
          isValid = false;
        } else if (!this.isScheduledComment && (taskStartDate > taskEndDate) && taskEndDate) {
          this.startDateError = 'Start date cannot be after the end date';
          isValid = false;
        } else {
          this.startDateError = '';
        }
      }
      if (this.scheduledTask.recurring) {
        const isDateInvalid = recurrenceSelectedStartDate < currentDate;
        const isStartDateChanged = this.initialRecurrenceDate && this.initialRecurrenceDate !== recurrenceSelectedStartDate;
        if (!recurrenceSelectedStartDate) {
          this.recurrenceStartDateError = 'Start date is required';
          isValid = false;
        } else if ((isDateInvalid && !this.initialRecurrenceDate) || (isDateInvalid && isStartDateChanged)) {
          this.recurrenceStartDateError = 'Start date cannot be in past';
          isValid = false;
        } else if (recurrenceSelectedStartDate > recurrenceSelectedEndDate && recurrenceSelectedEndDate) {
          this.recurrenceStartDateError = 'Start date cannot be after the end date';
          isValid = false;
        } else {
          this.recurrenceStartDateError = '';
        }
      }

      /// Daily Pattern Validation
      if (this.scheduledTask.recurring && this.selectedPattern === 'daily') {
        const dailyRecurrence = this.scheduledTask.recurrence.recurrencePattern.daily;
        const revisionCount = parseInt(dailyRecurrence.revisionCount, 10);
        if (!this.isDayRevisionDisable && (Number.isNaN(revisionCount) || revisionCount === 0)) {
          this.dailyRevisionError = "Must be greater than 0";
          isValid = false;
        } else {
          this.dailyRevisionError = '';
        }
      }

      /// Weekly Pattern Validation
      if (this.scheduledTask.recurring && this.selectedPattern === 'weekly') {
        const weeklyRecurrence = this.scheduledTask.recurrence.recurrencePattern.weekly;
        const revisionCount = parseInt(weeklyRecurrence.revisionCount, 10);
        if (weeklyRecurrence.selectedDays.length === 0) {
          this.weekSelectError = 'Select at least one week day';
          isValid = false;
        } else {
          this.weekSelectError = '';
        }
        if (Number.isNaN(revisionCount) || revisionCount === 0) {
          this.weeklyRevisionError = "Must be greater than 0";
          isValid = false;
        } else {
          this.weeklyRevisionError = '';
        }
      }
      /// Monthly Pattern Validation
      if (this.scheduledTask.recurring) {
        const monthlyRecurrence = this.scheduledTask.recurrence.recurrencePattern.monthly;
        const dayMonthRevision = parseInt(monthlyRecurrence.dayMonthRevision, 10);
        const weekMonthRevision = parseInt(monthlyRecurrence.weekMonthRevision, 10);
        const monthDay = parseInt(monthlyRecurrence.monthDay, 10);
        if (!this.isMonthyDayDisable && (Number.isNaN(monthDay) || monthDay < 1 || monthDay > 31)) {
          this.monthlyDayError = "must be between 1 and 31";
          isValid = false;
        } else {
          this.monthlyDayError = '';
        }
        if (!this.isMonthyDayDisable && (Number.isNaN(dayMonthRevision) || dayMonthRevision < 1 || dayMonthRevision > 12)) {
          this.monthlyDayRevisionError = "must be between 1 and 12";
          isValid = false;
        } else {
          this.monthlyDayRevisionError = '';
        }
        if (!this.isMonthyWeekDisable && (Number.isNaN(weekMonthRevision) || weekMonthRevision < 1 || weekMonthRevision > 12)) {
          this.monthlyWeekRevisionError = "must be between 1 and 12";
          isValid = false;
        } else {
          this.monthlyWeekRevisionError = '';
        }
      }

      /// Yearly Pattern Validation
      if (this.scheduledTask.recurring && this.selectedPattern === 'yearly') {
        const yearlyRecurrence = this.scheduledTask.recurrence.recurrencePattern.yearly;
        const revisionCount = parseInt(yearlyRecurrence.revisionCount, 10);
        const monthDay = parseInt(yearlyRecurrence.monthDay, 10);
        if (Number.isNaN(revisionCount) || revisionCount === 0) {
          this.yearlyRevisionError = "must not be empty or 0";
          isValid = false;
        } else {
          this.yearlyRevisionError = '';
        }
        if (!this.isMonthDayOptionDisable && (Number.isNaN(monthDay) || monthDay < 1 || monthDay > this.availableDays)) {
          this.yearlyDayError = "Date must be within selected month's range";
          isValid = false;
        } else {
          this.yearlyDayError = '';
        }
      }

      return isValid;
    },
    taskStartDate(task) {
      const date = task.recurring ? task.recurrence?.recurrenceDate?.startDate : task.taskStartedAt?.date || '';
      const startTime = task.taskStartedAt?.startTime;
      let dateFormat = "MMM D, Y";
      if (startTime) {
        dateFormat += ", h:mma";
      }
      return moment(`${date} ${startTime}`).format(dateFormat);
    },
    flattenedRecipients(notification) {
      const recipients = new Set(notification.value.recipients || []);
      const recipientEmails = new Set(notification.value.recipientEmails || []);
      return [...recipients, ...recipientEmails];
    },
    isEmailNotification(type) {
      return type === "email";
    },
    convertTimeDate(task) {
      const { utcTime, timeZone } = task.taskStartedAt;
      if (utcTime && timeZone) {
        const localMoment = this.$moment.utc(utcTime).tz(timeZone);
        const startTime = localMoment.format('HH:mm');
        const convertStartTime = this.convertTimeFormat(startTime, 'to12');
        task.taskStartedAt.date = localMoment.format('YYYY-MM-DD');
        task.taskStartedAt.startTime = convertStartTime;
      }
    },
    setScheduledCommentDetails(initTask) {
      if (initTask.id) {
        this.$nextTick(()=> {
          this.$refs.scheduledTaskFormModal.setSendToCreatorEmail();
          this.$refs.scheduledTaskFormModal.setScheduledCommentRecipients();
          this.$refs.scheduledTaskFormModal.setScheduledCommentSubject();
          this.$refs.scheduledTaskFormModal.setSubjectValues();
        });
      } else {
        this.$nextTick(()=> {
          this.$refs.scheduledTaskFormModal.setSubjectValues();
        });
      }
    },
  },
};

function getDefaultDate(skipDaysCount = 0) {
  const today = new Date();
  const futureDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + skipDaysCount);
  const year = futureDay.getFullYear();
  const month = String(futureDay.getMonth() + 1).padStart(2, '0');
  const day = String(futureDay.getDate()).padStart(2, '0'); 
  
  return `${year}-${month}-${day}`;
}

export const defaultRecurrencePattern = {
  recurrencePattern: {
    daily: { recurrenceType: 'revision', revisionCount: 1 },
    weekly: { selectedDays: [], revisionCount: 1 },
    monthly: { recurrenceType: 'monthDay', monthDay: 1, dayMonthRevision: 1, weekMonthRevision: 1, selectedWeek: 'First', weekDayName: "Monday" },
    yearly: { recurrenceType: 'monthDayOption', revisionCount: 1, dayMonthName: "January", weekMonthName: "January", monthDay: 1, weekDay: "First", dayName: "Monday" },
  },
  recurrenceDate: { startDate: getDefaultDate(1), endDateType: 'endByDate', endDate:  getDefaultDate(2), occurances: 1 },
};

export const defaultInitTasklist = {
  name: "", 
  description: "",
  assigneeId: null,
  recurring: false, 
  taskEndedAt: { date: getDefaultDate(2), endTime: '09:30', timeZone: Vue.prototype.$timezone}, 
  taskStartedAt: { date: getDefaultDate(1), startTime: '09:00', timeZone: Vue.prototype.$timezone},
  recurrence: defaultRecurrencePattern,
  notifications: [
    {
      notificationType: 'email',
      enabled: false,
      value: { recipients: [], recipientEmails: [] },
    },
    {
      notificationType: 'text',
      enabled: false,
      value: { recipients: [] },
    },
    {
      notificationType: 'desktop',
      enabled: false,
      value: { recipients: [] },
    },
  ],
};
