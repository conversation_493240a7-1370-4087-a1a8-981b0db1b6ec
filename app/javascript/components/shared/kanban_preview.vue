<template>
  <div class="w-100 position-relative">
    <div class="d-flex justify-content-center">
      <a class="box mb-5">
        <div class="box__inner align-self-start d-flex flex-column h-100">
          <div class="row align-items-center mb-3 mt-n0.5">
            <div class="col-auto text-secondary font-weight-normal align-self-start pr-2.5 ml-n0.5">
              <span class="d-inline-flex">
                <span>
                  <div class="mb-0 small text-muted nowrap has-tooltip">
                    <div
                      aria-hidden="true"
                      class="d-inline-flex justify-content-center logo-base logo-large"
                    >
                      <span>JD</span>
                    </div>
                  </div>
                </span>
              </span>
            </div>
            <div class="col pl-0 truncate-subject my-n1">
              <div class="reduced">Ticket Subject</div>
            </div>
          </div>

          <div class="d-flex align-items-start flex-wrap mt-auto mx-n0.5 mb-n1">
            <div
              v-if="showTicketProperty('status')"
              class="ticket-state-info ticket-state-info--status px-0.5 mb-1"
            >
              <div class="dropdown d-inline-block has-tooltip">
                <a
                  href="#"
                  class="btn btn-sm d-flex align-items-center border-0 py-0.5 true-small dropdown-toggle has-tooltip ticket-status"
                >
                  <span>
                    <i
                      class="status-icon status-icon--sm mr-1"
                      style="background: rgb(46, 204, 113)"
                    />
                  </span>
                  <span class="d-block truncate font-weight-normal">
                    Open
                  </span>
                </a>
                <span>
                  <div class="teleporter hidden"/>
                </span>
              </div>

            </div>
            <div
              v-if="showTicketProperty('priority')"
              class="ticket-state-info ticket-state-info--priority px-0.5 mb-1"
            >
              <div class="dropdown d-inline-block has-tooltip">
                <a class="btn btn-text text-secondary btn-sm d-flex align-items-center py-0.5 border-0 true-small dropdown-toggle has-tooltip">
                  <i
                    class="flag-height align-middle genuicon-priority-low"
                    style="color: rgb(255, 215, 0)"
                  />
                  <span class="ml-1 font-weight-normal truncate">
                    Low
                  </span>
                  <i class="nulodgicon nulodgicon-arrow-down"/>
                </a>
              </div>
            </div>
            <div
              v-if="showTicketProperty('assignedTo')"
              class="ticket-state-info ticket-state-info--assignment true-small px-0.5 mt-n1 ml-auto"
            >
              <span>
                <div class="form-group mb-0 d-flex align-items-center p-1 rounded dropdown-hover">
                  <span class="d-inline-flex align-items-center flex-child-force-shrink-x pr-1">
                    <span class="avatar-holder">
                      <div class="mb-0 small text-muted nowrap">
                        <div
                          aria-hidden="true"
                          class="position-relative d-inline-flex align-items-center justify-content-center text-center rounded-circle user-select-none logo-base logo-assign"
                        >
                          <span>JD</span>
                        </div>
                      </div>
                    </span>
                    <span class="truncate">
                      <span>
                        John Doe
                      </span>
                    </span>
                  </span>
                  <div>
                    <a class="text-secondary dropdown-toggle"/>
                  </div>
                </div>

              </span>
            </div>
          </div>

          <div
            v-if="ticketBottomTray"
            class="bg-themed-lighter text-secondary smallest font-weight-semi-bold rounded mt-2.5 mb-n2 mx-n2 py-1 px-2"
          >
            <div class="d-flex flex-wrap justify-content-between mr-n4">
              <div
                v-if="showTicketProperty('ticketNumber')"
                class="d-flex align-items-center separated-column justify-content-between my-0.5"
              >
                <span
                  v-tooltip="'Ticket Number'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                >
                  <i class="genuicon-nav-tickets reduced mr-1" />
                  <span
                    class="nowrap"
                  >
                    #12
                  </span>
                </span>
              </div>

              <div
                v-if="showTicketProperty('commentCount')"
                class="d-flex align-items-center separated-column justify-content-between my-0.5"
              >
                <span
                  v-tooltip="'Comments'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                >
                  <i class="genuicon-comment-o reduced mr-1" />
                  <span >5</span>
                </span>
              </div>

              <div
                v-if="showTicketProperty('updatedAt')"
                class="d-flex align-items-center separated-column justify-content-between my-0.5"
              >
                <span
                  v-tooltip="'Last updated'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                >
                  <i class="genuicon-android-time reduced mr-1" />
                  <span class="nowrap">0m</span>
                </span>
              </div>

              <div
                v-if="showTicketProperty('source')"
                class="d-flex align-items-center separated-column justify-content-between my-0.5"
              >
                <span
                  v-tooltip="'source'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                  :class="{ 'mr-4': !workspaceCompany }"
                >
                  <img
                    src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/source-manual.svg"
                    class="ticket-source-image position-relative"
                    height="16"
                    width="16"
                  >
                </span>
              </div>
              <div
                v-if="showTicketProperty('company')"
                class="d-flex align-items-center separated-column justify-content-between my-0.5"
              >
                <span
                  v-tooltip="'Company'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                >
                  <i class="genuicon-company reduced mr-1" />
                  <span >Company</span>
                </span>
              </div>
              <div
                v-if="showTicketProperty('workspace')"
                class="d-flex align-items-center flex-grow-1 justify-content-between mr-3"
              >
                <span
                  v-tooltip="'Workspace'"
                  class="d-flex align-items-center separated-column__content justify-content-center"
                >
                  <i class="genuicon-workspace reduced mr-1" />
                  <span >Workspace</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      columnData: {
        type: Array,
        required: true,
        default: () => [],
      },
    },
    data() {
      return {
        isHoveringPreview: false,
      };
    },
    computed: {
      ticketBottomTray() {
        return [
          'ticketNumber',
          'commentCount',
          'updatedAt',
          'source',
          'company',
          'workspace',
        ].some(prop => this.showTicketProperty(prop));
      },
      workspaceCompany() {
        return this.showTicketProperty('company') || this.showTicketProperty('workspace');
      },
      activatedFields() {
        return this.columnData.map((c) => c.fieldName);
      },
    },
    watch: {
      columnData() {
        this.$nextTick(() => {
          if (this.$refs.previewScrollableTable) {
            this.$refs.previewScrollableTable.setLeftOffsets();
          }
        });
      },
    },
    methods: {
      columnName(column) {
        return column.description || column.friendlyName || '';
      },
      showTicketProperty(property) {
        return this.activatedFields.includes(property);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .ticket-state-info {
    min-width: 0;
  }
  .ticket-state-info--priority {
    flex-basis: 5.5rem;
  }
  .ticket-state-info--assignment {
    flex-basis: 7rem;
  }
  .separated-column {
    flex-grow: 1;
  }
  .separated-column__content {
    flex: 1 1;
  }
  .separated-column__separator {
    color: var(--themed-very-muted);
    content: "•";
    text-align: center;
    width: 1.5rem;
  }
  .separated-columns-wrap {
    clip-path: inset(0 1.5rem 0 0);
  }
  .separated-column:not(:last-of-type):after,
  .separated-column__separator {
    color: var(--themed-very-muted);
    content: "\2022";
    text-align: center;
    width: 1.5rem;
  }
  .box {
    width: 33rem;
  }
  .logo-base {
    border-radius: 50%;
    background-color: rgb(0, 150, 136);
    color: rgb(80, 230, 216);
    z-index: 1;
  }
  .logo-assign {
    width: 1.5rem;
    height: 1.5rem;
    font: 0.563rem / 1.5rem Helvetica, Arial, sans-serif;
  }
  .logo-large {
    width: 2.25rem;
    height: 2.25rem;
    font: 0.875rem / 2.25rem Helvetica, Arial, sans-serif;
  }
  .ticket-status {
    max-width: 15.625rem !important;
    background-color: #e0f7ea;
    color: #1e8549;
  }
</style>
