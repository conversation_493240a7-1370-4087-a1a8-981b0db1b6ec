class Integrations::Sophos::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs
  include IntegrationAlertsHelper

  attr_accessor :config, :first_time_flag, :page

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    self.config = set_read_replica_db do
      Integrations::Sophos::Config.find_by(id: config_id)
    end
    return if self.config.nil?

    @import_target = self.config.import_type
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    pusher(config, true, 0.2)
    refresh_access_token
    pusher(config, true, 0.4)
    save_devices
    pusher(config, true, 0.8)
    update_company_integration
    pusher(config, true, 1, {
      new_sophos_discovered_assets: @new_discovered_assets_count,
      new_sophos_managed_assets: @new_managed_assets_count
    })
    create_logs
    intg_duration_analysis(config.company_id, first_time_flag)
  rescue Exception => e
    create_logs
    send_notification_with_updating_status(e)
    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em) }
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'sophos_pro_integration').to_json)
    pusher(config, false, 0)
  end

  def refresh_access_token
    client.refresh_access_token
  end

  def error_messages
    ['forbidden']
  end

  def company_integration
    set_read_replica_db do
      config.company_integration
    end
  end

  def client
    @sophos_service ||= Integrations::Sophos::FetchData.new(config.company_id, config, config.sophos_tenant)
  end

  def save_devices(next_page_token = nil)
    response =  client.get_devices(next_page_token)
    devices = response["items"]
    next_page_token = response["pages"]["nextKey"]
    devices&.each do |device|
      retries = 0
      begin
        @mac_addresses = []
        @device_info = device
        display_name = @device_info['hostname']
        ip_address = @device_info['ipv4Addresses']&.join(", ")
        serial_number = @device_info['serialNumber']
        manufacturer = extract_manufacturer(@device_info)
        @mac_addresses = @device_info['macAddresses']
        @discovered_asset = find_discovered_asset(config.company, false, serial_number, @mac_addresses, display_name, ip_address, manufacturer, false)
        @discovered_asset ||= DiscoveredAsset.sophos.ready_for_import.new(company_id: config.company.id)
        assign_discovered_asset_attributes(device)
        create_asset_software
        managed_asset = find_managed_asset(config.company, serial_number, @mac_addresses, display_name, manufacturer)

        if managed_asset.present?
          @discovered_asset.status = :imported
          @discovered_asset.managed_asset_id = managed_asset.id
        end
        assign_discovered_assets_hardware_details(device)
        is_new = @discovered_asset.new_record?
        @discovered_asset.save!
        @asset_count += 1 if is_new
        @new_discovered_assets_count += 1 if is_new
        create_asset_source(device)
        @discovered_asset.reload

        if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
          BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
          @new_managed_assets_count += 1
        end
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, @discovered_asset)
          break
        end
      rescue StandardError => e
        log_exception(e, @discovered_asset)
      end
    end
    save_devices(next_page_token) if next_page_token
  end

  def send_notification_with_updating_status(error)
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      error_message: error.message,
      alert_info: IntegrationAlertsHelper::ERROR_STATE
    )
    company_integration.save
    send_notification(company_integration) unless @first_time_flag
  end

  def update_company_integration
    if client.ok?
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        alert_info: IntegrationAlertsHelper::SUCCESS_STATE
      )
    else
      company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        error_message: client.error,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        alert_info: IntegrationAlertsHelper::ERROR_STATE
      )
    end
    company_integration.save
  end

  def assign_discovered_asset_attributes(device)
    @discovered_asset.assign_attributes(
      display_name: device['hostname'],
      mac_addresses: @mac_addresses,
      ip_address: device["ipv4Addresses"]&.join(", "),
      machine_serial_no: device['serialNumber'],
      os: device['os']['platform'],
      os_name: device['os']['name'],
      os_version: extract_os_version(device),
      source: 'sophos',
      system_uuid: device['id'],
      last_synced_at: DateTime.now,
      lower_precedence: @is_lower_precedence,
      asset_type: get_asset_type(device),
      last_check_in_time: device['lastSeenAt']
    )
  end

  def extract_manufacturer(device_info)
    platform = device_info["os"]["platform"]
    case platform
    when "macOS"
      "Apple"
    when "windows"
      "Microsoft"
    else
      platform.capitalize
    end
  end

  def extract_os_version(device_info)
    major = device_info.dig("os", "majorVersion")
    minor = device_info.dig("os", "minorVersion")
    "#{major}.#{minor}"
  end

  def extract_disk_encryption_status(device_info)
    status = device_info.dig("encryption", "overallStatus")
    status == "encrypted" ? "On" : "Off"
  end

  def get_asset_type(device_info)
    is_server = device_info.dig("os", "isServer")
    hostname = device_info["hostname"].to_s.downcase

    if is_server
      return "Server"
    elsif hostname.include?("desktop")
      return "Desktop"
    else
      return "Laptop"
    end
  end

  def create_asset_software
    discovered_asset_softwares = set_read_replica_db do
      @discovered_asset.asset_softwares
    end
    return if is_lower_precedence && discovered_asset_softwares.present?
    if @discovered_asset.os_name.present?
      discovered_asset_softwares.find_or_initialize_by({
        software_type: 'Operating System',
        name: @discovered_asset.os_name
      })
    end
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end
    {
      asset_sources: discovered_managed_asset_sources,
      current_source: 'sophos',
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.sophos.new
    }
  end

  def is_lower_precedence
    !is_higher_precedence?(**precedence_data)
  end

  def assign_discovered_assets_hardware_details(device)
    @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
    @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
      hostname: device['hostname'],
      disk_encryption: extract_disk_encryption_status(device)
    )
  end

  def create_asset_source(device)
    asset_source_data = {
      display_name:  device['hostname'],
      machine_serial_no: device['serialNumber'],
      manufacturer: extract_manufacturer(device),
      os_name: device['os']['name'],
      source: 'sophos',
    }
    add_source(asset_source_data)
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :sophos)
    end
    das.updated_at = DateTime.now
    das.asset_data = asset_source_data
    das.company_integration_id = company_integration.id
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.save
  end

  def event_params(error, detail, api_type)
    {
      company_id: config.company.id,
      status: :error,
      error_detail: error.backtrace.join('\n'),
      class_name: self.class.name,
      integration_id: intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: '422',
      created_at: DateTime.now
    }
  end

  def intg_id
    @intg_id ||= set_read_replica_db do
      Integration.find_by_name('sophos').id
    end
  end

  def log_exception(exception, discovered_asset)
    Rails.logger.error(exception)
    Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !exception.message.include?('Managed asset has already been taken')
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, 'save_devices').to_json)
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Sophos', @company_user_id, config.company.id)
    end
  end
end
