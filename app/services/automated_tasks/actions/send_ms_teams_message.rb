module AutomatedTasks
  module Actions
    class SendMsTeamsMessage < HelpTicketAction
      include AutomatedTasks::Concerns::Activity
      include MsTeamsOptions
      include Utilities::Domains
      include <PERSON><PERSON><PERSON>ms<PERSON><PERSON>per

      def call
        return if root.class.name == 'HelpTicketComment' && root.private_flag
        send_message
        # TODO: MS teams: remove env check 
        send_child_config_message unless ms_teams_feature_access_pending?(scoped_company.id)
        create_recepients_activity("Ms team message", recipients_name) if object.class.name == 'HelpTicket'
      end

      def ticket
        object
      end

      def custom_form
        ticket.custom_form
      end

      def send_message
        ms_teams_service.call('automated_message', { message: message, object_id: object.id, help_ticket: ticket_json })
      end

      def send_child_config_message
        # Finds such a config which has the current ticket id
        bot_config = scoped_company.ms_teams_configs
                                   .where('bot_ticket_ids @> ARRAY[?]::integer[]', object.id)
                                   .where('TRIM(channel_name) LIKE ?', '@Genuity%')
                                   .first

        return if bot_config.nil?

        if (dm_channel_with_ticket_id?(bot_config))
            if root.class.name == 'HelpTicket'
              # As we already have the message cached for ticket creation we extract creator
              created_by = message.match(/\*\*(.*?)\*\*/)[1]
              dm_message = message("<p>Hi <strong>#{created_by}</strong>, your ticket is received! We’ll keep you posted.</p>")
            end
          bot_config_client = Integrations::MsTeams::Client.new(bot_config)
          bot_config_client.call('automated_message', { message: dm_message || message, object_id: object.id, help_ticket: ticket_json, is_bot_dm: true })
        end     
      end

      def dm_channel_with_ticket_id?(generated_config)
        is_dm_channel_id?(generated_config.channel_name, generated_config.channel_id) && generated_config.bot_ticket_ids.include?(object.id)
      end

      def is_dm_channel_id?(name, id)
        # The channel id of dm start with 'a' but its not written in docs
        name.start_with?("@Genuity") && id.start_with?("a")
      end

      def recipients_name
        [ms_teams_config.channel_name+" #{ms_teams_config.team_name == 'Group Chat' ? 'Group Chat' : 'channel'}"]
      end

      def params
        @params = ActionController::Parameters.new(archived: '')
      end

      def help_ticket_data
        {}
      end

      def scoped_company
        object.company
      end

      def ms_teams_service
        @ms_teams_service ||= Integrations::MsTeams::Client.new(ms_teams_config)
      end

      def ms_teams_config_id
        json_value['ms_teams_config']['id']
      end

      def ms_teams_config
        Integrations::MsTeams::Config.find(ms_teams_config_id)
      end

      def json_value
        @json_data ||= JSON.parse(action.value)
      end

      def message(default_value = nil)
        body = StringInterpolate.new(root).call(default_value || json_value['message']).gsub('<div><!--block-->', '').gsub('</div>', '')
        HtmlToMarkdownConverter.new(parse_type: 'teams', content: body).call
      end      
    end
  end
end
