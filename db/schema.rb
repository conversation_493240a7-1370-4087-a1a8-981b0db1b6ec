# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_23_095727) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "citext"
  enable_extension "hstore"
  enable_extension "plpgsql"

  create_table "action_mailbox_inbound_emails", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "message_checksum", null: false
    t.string "message_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "updated_at", null: false
    t.index ["message_id", "message_checksum"], name: "index_action_mailbox_inbound_emails_uniqueness", unique: true
  end

  create_table "actionable_alerts", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "link"
    t.text "message"
    t.integer "module", default: 0, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_actionable_alerts_on_company_id"
    t.index ["workspace_id"], name: "index_actionable_alerts_on_workspace_id"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.string "name", null: false
    t.bigint "record_id", null: false
    t.string "record_type", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.string "content_type"
    t.datetime "created_at", precision: nil, null: false
    t.string "filename", null: false
    t.string "key", null: false
    t.text "metadata"
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_logs", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.json "description", default: {}
    t.string "record_class"
    t.bigint "record_id"
    t.integer "status"
    t.datetime "updated_at", null: false
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_overview_customizations", force: :cascade do |t|
    t.jsonb "agent_ids"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_admin_overview_customizations_on_company_id"
    t.index ["workspace_id"], name: "index_admin_overview_customizations_on_workspace_id"
  end

  create_table "agent_locations", force: :cascade do |t|
    t.string "app_version"
    t.bigint "company_id"
    t.string "computer_name"
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "cumulative_storage_info"
    t.string "ip_address"
    t.boolean "is_admin_app"
    t.boolean "is_enabled", default: true
    t.datetime "last_active", precision: nil
    t.datetime "last_scanned_at", precision: nil
    t.bigint "location_id"
    t.text "mac_addresses", default: [], array: true
    t.string "machine_serial_number"
    t.bigint "managed_by_contributor_id"
    t.string "manufacturer", default: ""
    t.string "os"
    t.text "secondary_mac_addresses", default: [], array: true
    t.string "system_uuid"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_agent_locations_on_company_id"
    t.index ["location_id"], name: "index_agent_locations_on_location_id"
    t.index ["managed_by_contributor_id"], name: "index_agent_locations_on_managed_by_contributor_id"
  end

  create_table "ai_summaries", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "help_ticket_id", null: false
    t.text "summary"
    t.datetime "updated_at", null: false
    t.index ["help_ticket_id"], name: "index_ai_summaries_on_help_ticket_id"
  end

  create_table "ai_usage_costs", force: :cascade do |t|
    t.string "ai_type"
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.string "model_type"
    t.string "month"
    t.datetime "updated_at", null: false
    t.jsonb "usage_by_day"
    t.index ["company_id"], name: "index_ai_usage_costs_on_company_id"
  end

  create_table "alert_dates", force: :cascade do |t|
    t.bigint "alertable_id"
    t.string "alertable_type"
    t.datetime "created_at", precision: nil, null: false
    t.date "date"
    t.boolean "is_seen", default: false
    t.string "notes"
    t.integer "recipients", default: [], array: true
    t.datetime "updated_at", precision: nil, null: false
    t.index ["alertable_type", "alertable_id"], name: "index_alert_dates_on_alertable_type_and_alertable_id"
  end

  create_table "alert_defaults", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "vendor_spend"
    t.index ["company_id"], name: "index_alert_defaults_on_company_id"
  end

  create_table "analytics_report_templates", force: :cascade do |t|
    t.jsonb "analytics_metrics"
    t.datetime "created_at", null: false
    t.bigint "default_analytics_report_template_id"
    t.text "description"
    t.string "name"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["default_analytics_report_template_id"], name: "index_template_on_default_analytics_report_template_id"
    t.index ["name", "workspace_id"], name: "index_analytics_report_templates_on_name_and_workspace_id", unique: true
    t.index ["workspace_id"], name: "index_analytics_report_templates_on_workspace_id"
  end

  create_table "app_downloads", force: :cascade do |t|
    t.string "application"
    t.string "company_guid"
    t.string "company_user_guid"
    t.datetime "created_at", precision: nil, null: false
    t.string "status"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
  end

  create_table "app_enableds", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.boolean "flag", default: true
    t.datetime "updated_at", null: false
  end

  create_table "app_releases", force: :cascade do |t|
    t.boolean "app_enabled", default: true
    t.string "app_name"
    t.integer "app_type", default: 0, null: false
    t.string "build_version"
    t.datetime "created_at", null: false
    t.string "description"
    t.string "installer_link"
    t.boolean "is_admin_app", default: true
    t.boolean "is_package_build", default: false
    t.datetime "updated_at", null: false
    t.string "version"
  end

  create_table "app_versions", id: :serial, force: :cascade do |t|
    t.integer "app_type", default: 0
    t.string "app_version", default: "", null: false
    t.datetime "created_at", precision: nil, null: false
    t.boolean "db_present", default: false
    t.string "description"
    t.string "installer_link"
    t.boolean "is_admin_app", default: true
    t.boolean "is_pkg_build", default: true
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.float "version"
  end

  create_table "applied_build_data_sets", force: :cascade do |t|
    t.integer "applied_companies", default: [], array: true
    t.integer "asset_types", default: [], array: true
    t.integer "automated_tasks", default: [], array: true
    t.integer "blocked_keywords", default: [], array: true
    t.bigint "build_by_id"
    t.integer "build_status"
    t.string "build_version"
    t.integer "categories", default: [], array: true
    t.bigint "company_build_id"
    t.datetime "created_at", null: false
    t.integer "custom_forms", default: [], array: true
    t.integer "documents", default: [], array: true
    t.integer "faqs", default: [], array: true
    t.integer "responses", default: [], array: true
    t.datetime "updated_at", null: false
    t.index ["build_by_id"], name: "index_applied_build_data_sets_on_build_by_id"
    t.index ["company_build_id"], name: "index_applied_build_data_sets_on_company_build_id"
  end

  create_table "apps_logs_options", force: :cascade do |t|
    t.bigint "company_id"
    t.boolean "enabled", default: true
    t.index ["company_id"], name: "index_apps_logs_options_on_company_id"
  end

  create_table "article_documents", force: :cascade do |t|
    t.integer "article_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "library_document_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "article_expanded_privileges", force: :cascade do |t|
    t.bigint "article_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "permission_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["article_id"], name: "index_article_expanded_privileges_on_article_id"
    t.index ["contributor_id"], name: "index_article_expanded_privileges_on_contributor_id"
  end

  create_table "article_privileges", force: :cascade do |t|
    t.bigint "article_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "permission_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["article_id"], name: "index_article_privileges_on_article_id"
    t.index ["contributor_id"], name: "index_article_privileges_on_contributor_id"
  end

  create_table "article_tags", force: :cascade do |t|
    t.bigint "article_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["article_id"], name: "index_article_tags_on_article_id"
    t.index ["company_id"], name: "index_article_tags_on_company_id"
  end

  create_table "articles", force: :cascade do |t|
    t.text "body"
    t.string "category"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "help_ticket_comment_id"
    t.bigint "help_ticket_id"
    t.bigint "location_id"
    t.integer "order", default: 0, null: false
    t.boolean "public", default: false
    t.date "review_date"
    t.string "slug"
    t.text "tags", default: [], array: true
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_articles_on_company_id"
    t.index ["company_user_id"], name: "index_articles_on_company_user_id"
    t.index ["help_ticket_comment_id"], name: "index_articles_on_help_ticket_comment_id"
    t.index ["help_ticket_id"], name: "index_articles_on_help_ticket_id"
    t.index ["location_id"], name: "index_articles_on_location_id"
    t.index ["workspace_id"], name: "index_articles_on_workspace_id"
  end

  create_table "asset_connector_logs", force: :cascade do |t|
    t.integer "action", null: false
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.integer "connector_name", null: false
    t.datetime "created_at", null: false
    t.jsonb "data", default: {}
    t.string "owner_name"
    t.integer "status", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_asset_connector_logs_on_company_id"
    t.index ["company_user_id"], name: "index_asset_connector_logs_on_company_user_id"
  end

  create_table "asset_insight_widgets", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.json "preference"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_asset_insight_widgets_on_company_user_id"
  end

  create_table "asset_lifecycles", force: :cascade do |t|
    t.integer "approaching_end_of_life"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.integer "lifecycle_type"
    t.bigint "linked_item_id"
    t.string "linked_item_type"
    t.string "name"
    t.string "po", default: ""
    t.float "purchase_price", default: 0.0
    t.float "replacement_cost", default: 0.0
    t.float "salvage", default: 0.0
    t.datetime "updated_at", null: false
    t.integer "useful_life"
    t.index ["company_id"], name: "index_asset_lifecycles_on_company_id"
    t.index ["linked_item_id", "linked_item_type", "company_id"], name: "index_lifecycle_linked_item_company_id", unique: true, where: "((linked_item_id IS NOT NULL) AND (linked_item_type IS NOT NULL))"
    t.index ["linked_item_type", "linked_item_id"], name: "index_asset_lifecycles_on_linked_item"
    t.index ["name", "company_id"], name: "index_lifecycle_name_company_id", unique: true, where: "(name IS NOT NULL)"
  end

  create_table "asset_preferences", force: :cascade do |t|
    t.json "analytics_preference"
    t.json "card_preference"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.json "preference"
    t.jsonb "qr_code_settings"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_asset_preferences_on_company_id"
  end

  create_table "asset_risk_center_widgets", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.string "description"
    t.boolean "enabled", default: false
    t.string "icon"
    t.boolean "is_custom", default: false
    t.string "label"
    t.string "name"
    t.string "selected_options", default: [], array: true
    t.datetime "updated_at", null: false
    t.index ["company_id", "name"], name: "index_asset_risk_center_widgets_on_company_id_and_name", unique: true
    t.index ["company_id"], name: "index_asset_risk_center_widgets_on_company_id"
    t.index ["name"], name: "index_asset_risk_center_widgets_on_name"
  end

  create_table "asset_softwares", force: :cascade do |t|
    t.datetime "archived_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_asset_id"
    t.date "install_date"
    t.boolean "is_manual", default: false
    t.bigint "managed_asset_id"
    t.string "name", default: ""
    t.string "product_key"
    t.string "software_type", default: "Operating System"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["discovered_asset_id", "software_type"], name: "index_asset_softwares_on_discovered_asset_id_and_software_type"
    t.index ["managed_asset_id", "software_type"], name: "index_aso_on_managed_asset_id_and_software_type_active", where: "(archived_at IS NULL)"
    t.index ["managed_asset_id", "software_type"], name: "index_asset_softwares_on_managed_asset_id_and_software_type"
  end

  create_table "asset_sources", force: :cascade do |t|
    t.jsonb "asset_data", null: false
    t.bigint "company_integration_id"
    t.datetime "created_at", precision: nil, default: -> { "now()" }, null: false
    t.bigint "discovered_asset_id"
    t.bigint "managed_asset_id"
    t.integer "source"
    t.datetime "updated_at", precision: nil, default: -> { "now()" }, null: false
    t.index ["company_integration_id"], name: "index_asset_sources_on_company_integration_id"
    t.index ["discovered_asset_id", "source"], name: "index_asset_sources_on_discovered_asset_id_and_source", unique: true
    t.index ["discovered_asset_id"], name: "index_asset_sources_on_discovered_asset_id"
    t.index ["managed_asset_id", "source"], name: "index_asset_sources_on_managed_asset_id_and_source", unique: true, where: "(source <> 10)"
    t.index ["managed_asset_id"], name: "index_asset_sources_on_managed_asset_id"
  end

  create_table "asset_types", id: :serial, force: :cascade do |t|
    t.string "ancestry"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.boolean "has_child", default: false
    t.string "name"
    t.boolean "nested_attributes", default: false
    t.integer "parent_type_id"
    t.hstore "private_asset_attributes"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["ancestry"], name: "index_asset_types_on_ancestry"
    t.index ["parent_type_id"], name: "index_asset_types_on_parent_type_id"
  end

  create_table "asset_usage_histories", force: :cascade do |t|
    t.bigint "company_asset_status_id"
    t.datetime "created_at", null: false
    t.date "current_state_since"
    t.bigint "department_id"
    t.date "expected_check_in"
    t.bigint "location_id"
    t.bigint "managed_asset_id"
    t.bigint "managed_by_contributor_id"
    t.string "phone_number"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.datetime "updated_at", null: false
    t.bigint "used_by_contributor_id"
    t.index ["company_asset_status_id"], name: "index_asset_usage_histories_on_company_asset_status_id"
    t.index ["location_id"], name: "index_asset_usage_histories_on_location_id"
    t.index ["managed_asset_id"], name: "index_asset_usage_histories_on_managed_asset_id"
    t.index ["managed_by_contributor_id"], name: "index_asset_usage_histories_on_managed_by_contributor_id"
    t.index ["used_by_contributor_id"], name: "index_asset_usage_histories_on_used_by_contributor_id"
  end

  create_table "asset_user_accounts", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_asset_id"
    t.string "full_name"
    t.string "last_login"
    t.bigint "managed_asset_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["discovered_asset_id"], name: "index_asset_user_accounts_on_discovered_asset_id"
    t.index ["managed_asset_id"], name: "index_asset_user_accounts_on_managed_asset_id"
  end

  create_table "assets_action_types", force: :cascade do |t|
    t.string "action_class"
    t.datetime "created_at", null: false
    t.string "icon"
    t.string "model"
    t.string "module"
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "assets_automated_tasks", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", null: false
    t.datetime "disabled_at"
    t.boolean "force_disabled", default: false
    t.boolean "is_default", default: false
    t.string "name"
    t.integer "order", default: 0
    t.integer "serial_number", default: 1, null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_assets_automated_tasks_on_company_id"
    t.index ["contributor_id"], name: "index_assets_automated_tasks_on_contributor_id"
  end

  create_table "assets_event_details", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "event_subject_type_id"
    t.bigint "task_event_id"
    t.datetime "updated_at", null: false
    t.text "value"
    t.index ["event_subject_type_id"], name: "index_assets_event_details_on_event_subject_type_id"
    t.index ["task_event_id"], name: "index_assets_event_details_on_task_event_id"
  end

  create_table "assets_event_subject_types", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "icon"
    t.string "key"
    t.string "name"
    t.bigint "parent_id"
    t.string "parent_type"
    t.string "subject_class"
    t.string "type"
    t.datetime "updated_at", null: false
    t.index ["parent_id", "parent_type"], name: "idx_assets_event_subject_types_parent"
  end

  create_table "assets_event_types", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "event_class"
    t.string "icon"
    t.string "model"
    t.string "module"
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "assets_task_actions", force: :cascade do |t|
    t.bigint "action_type_id"
    t.bigint "automated_task_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "value"
    t.index ["action_type_id"], name: "index_assets_task_actions_on_action_type_id"
    t.index ["automated_task_id"], name: "index_assets_task_actions_on_automated_task_id"
  end

  create_table "assets_task_events", force: :cascade do |t|
    t.bigint "automated_task_id"
    t.datetime "created_at", null: false
    t.bigint "event_type_id"
    t.datetime "updated_at", null: false
    t.hstore "value"
    t.index ["automated_task_id"], name: "index_assets_task_events_on_automated_task_id"
    t.index ["event_type_id"], name: "index_assets_task_events_on_event_type_id"
  end

  create_table "assignment_informations", id: :serial, force: :cascade do |t|
    t.datetime "assigned_on", precision: nil
    t.string "attached_file"
    t.datetime "created_at", precision: nil, null: false
    t.date "current_state_since"
    t.integer "department_id"
    t.date "expected_check_in"
    t.integer "location_id"
    t.integer "managed_asset_id"
    t.integer "managed_by_contributor_id"
    t.string "phone_number"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "used_by_contributor_id"
    t.index ["location_id"], name: "index_assignment_informations_on_location_id"
    t.index ["managed_asset_id"], name: "index_assignment_informations_on_managed_asset_id"
  end

  create_table "attachment_uploads", force: :cascade do |t|
    t.bigint "attachable_id"
    t.string "attachable_type"
    t.string "attachment_content_type"
    t.string "attachment_file_name"
    t.integer "attachment_file_size"
    t.datetime "attachment_updated_at", precision: nil
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["attachable_type", "attachable_id"], name: "index_attachment_uploads_on_attachable_type_and_attachable_id"
    t.index ["company_id"], name: "index_attachment_uploads_on_company_id"
  end

  create_table "attributes_descriptions", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "data"
    t.bigint "entity_id"
    t.string "entity_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["entity_type", "entity_id"], name: "index_attributes_descritions_on_entity"
  end

  create_table "audits", force: :cascade do |t|
    t.string "action"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "auditable_id"
    t.string "auditable_type"
    t.text "audited_changes"
    t.string "comment"
    t.datetime "created_at", precision: nil
    t.string "remote_address"
    t.string "request_uuid"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.integer "version", default: 0
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
  end

  create_table "automated_task_groups", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.bigint "task_template_id"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_automated_task_groups_on_company_id"
    t.index ["workspace_id"], name: "index_automated_task_groups_on_workspace_id"
  end

  create_table "automated_tasks_action_details", force: :cascade do |t|
    t.bigint "action_type_id"
    t.bigint "automated_task_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.hstore "value"
    t.index ["action_type_id"], name: "index_automated_tasks_action_details_on_action_type_id"
    t.index ["automated_task_id"], name: "index_automated_tasks_action_details_on_automated_task_id"
  end

  create_table "automated_tasks_action_types", force: :cascade do |t|
    t.string "action_class"
    t.datetime "created_at", precision: nil, null: false
    t.string "icon"
    t.string "model"
    t.string "module"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "automated_tasks_automated_tasks", force: :cascade do |t|
    t.boolean "assign_values", default: true
    t.bigint "automated_task_group_id"
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "disabled_at", precision: nil
    t.boolean "force_disabled", default: false
    t.bigint "msp_templates_automated_task_id"
    t.string "name"
    t.integer "order", default: 0
    t.integer "serial_number", default: 1, null: false
    t.integer "trigger_count", default: 0, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["automated_task_group_id"], name: "idx_auto_tasks_on_grouped_task_id"
    t.index ["company_id"], name: "index_automated_tasks_automated_tasks_on_company_id"
    t.index ["contributor_id"], name: "index_automated_tasks_automated_tasks_on_contributor_id"
    t.index ["msp_templates_automated_task_id"], name: "idx_automated_tasks_on_msp_automated_tasks"
    t.index ["workspace_id"], name: "index_automated_tasks_automated_tasks_on_workspace_id"
  end

  create_table "automated_tasks_event_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "event_subject_type_id"
    t.integer "parent_id"
    t.bigint "task_event_id"
    t.datetime "updated_at", precision: nil, null: false
    t.text "value"
    t.index ["event_subject_type_id"], name: "index_automated_tasks_event_details_on_event_subject_type_id"
    t.index ["task_event_id"], name: "index_automated_tasks_event_details_on_task_event_id"
  end

  create_table "automated_tasks_event_subject_types", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "icon"
    t.string "key"
    t.string "name"
    t.bigint "parent_id"
    t.string "parent_type"
    t.string "subject_class"
    t.string "type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["parent_id", "parent_type"], name: "idx_event_subject_types_parent"
  end

  create_table "automated_tasks_event_types", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "event_class"
    t.string "icon"
    t.string "model"
    t.string "module"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "automated_tasks_execution_dates", force: :cascade do |t|
    t.bigint "automated_task_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.date "date"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["automated_task_id"], name: "index_automated_tasks_execution_dates_on_automated_task_id"
    t.index ["company_id"], name: "index_automated_tasks_execution_dates_on_company_id"
    t.index ["date", "automated_task_id"], name: "idx_execution_dates_on_date_and_automated_task_id"
  end

  create_table "automated_tasks_execution_locks", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "key"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["key"], name: "index_automated_tasks_execution_locks_on_key", unique: true
  end

  create_table "automated_tasks_execution_logs", force: :cascade do |t|
    t.bigint "automated_tasks_automated_task_id"
    t.bigint "company_id"
    t.datetime "completed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.text "entity_attributes"
    t.integer "entity_id"
    t.string "entity_name"
    t.text "message"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["automated_tasks_automated_task_id"], name: "index_execution_log_on_automated_task_id"
    t.index ["company_id"], name: "index_automated_tasks_execution_logs_on_company_id"
    t.index ["workspace_id"], name: "index_automated_tasks_execution_logs_on_workspace_id"
  end

  create_table "automated_tasks_task_action_templates", force: :cascade do |t|
    t.bigint "automated_task_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "value", default: {}
    t.index ["automated_task_template_id"], name: "index_task_action_template_on_automated_task_template"
  end

  create_table "automated_tasks_task_actions", force: :cascade do |t|
    t.bigint "action_type_id"
    t.bigint "automated_task_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "email_template_id"
    t.datetime "updated_at", precision: nil, null: false
    t.text "value"
    t.index ["action_type_id"], name: "index_automated_tasks_task_actions_on_action_type_id"
    t.index ["automated_task_id"], name: "index_automated_tasks_task_actions_on_automated_task_id"
    t.index ["email_template_id"], name: "index_automated_tasks_task_actions_on_email_template_id"
  end

  create_table "automated_tasks_task_event_templates", force: :cascade do |t|
    t.bigint "automated_task_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "value", default: {}
    t.index ["automated_task_template_id"], name: "index_task_event_template_on_automated_task_template"
  end

  create_table "automated_tasks_task_events", force: :cascade do |t|
    t.bigint "automated_task_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "event_type_id"
    t.integer "parent_id"
    t.datetime "updated_at", precision: nil, null: false
    t.hstore "value"
    t.index ["automated_task_id"], name: "index_automated_tasks_task_events_on_automated_task_id"
    t.index ["event_type_id"], name: "index_automated_tasks_task_events_on_event_type_id"
  end

  create_table "automated_tasks_task_next_assignments", force: :cascade do |t|
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "task_action_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["task_action_id"], name: "index_automated_tasks_task_next_assignments_on_task_action_id", unique: true
  end

  create_table "automated_tasks_task_templates", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "name"
    t.integer "order"
    t.datetime "updated_at", null: false
  end

  create_table "aws_assets_configs", force: :cascade do |t|
    t.string "access_key"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "import_type", default: 1, null: false
    t.text "regions", default: [], array: true
    t.string "secret_key"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_aws_assets_configs_on_company_id"
    t.index ["company_user_id"], name: "index_aws_assets_configs_on_company_user_id"
  end

  create_table "aws_configs", force: :cascade do |t|
    t.string "access_key"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "region"
    t.string "secret_key"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_aws_configs_on_company_id"
  end

  create_table "azure_ad_assets_configs", force: :cascade do |t|
    t.string "code"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.integer "import_type", default: 1, null: false
    t.boolean "is_force_stopped", default: false
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_azure_ad_assets_configs_on_company_id"
    t.index ["company_user_id"], name: "index_azure_ad_assets_configs_on_company_user_id"
  end

  create_table "azure_ad_configs", force: :cascade do |t|
    t.boolean "allow_guest_users", default: true
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.jsonb "data"
    t.text "excluded_attributes", default: [], array: true
    t.datetime "expires_in"
    t.string "refresh_token"
    t.boolean "sync_all_users", default: false
    t.string "token"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_azure_ad_configs_on_company_id"
  end

  create_table "azure_ad_groups", force: :cascade do |t|
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", null: false
    t.string "external_id"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_azure_ad_groups_on_company_id"
  end

  create_table "azure_assets_configs", force: :cascade do |t|
    t.string "code"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.integer "import_type", default: 1, null: false
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_azure_assets_configs_on_company_id"
    t.index ["company_user_id"], name: "index_azure_assets_configs_on_company_user_id"
  end

  create_table "azure_configs", force: :cascade do |t|
    t.string "code"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_azure_configs_on_company_id"
  end

  create_table "azure_license_reports", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "azure_licenses", force: :cascade do |t|
    t.string "billing_plan"
    t.string "currency"
    t.string "description"
    t.float "internal_price"
    t.float "msrp"
    t.string "name"
  end

  create_table "bill_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "development_key"
    t.string "organization_id"
    t.string "password"
    t.boolean "select_all", default: false
    t.date "start_date"
    t.datetime "updated_at", precision: nil, null: false
    t.string "user_name"
    t.index ["company_id"], name: "index_bill_configs_on_company_id"
  end

  create_table "blocked_entities", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "entity"
    t.integer "entity_type"
    t.bigint "msp_templates_blocked_keyword_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_blocked_entities_on_company_id"
    t.index ["msp_templates_blocked_keyword_id"], name: "index_blocked_entities_on_msp_templates_blocked_keyword_id"
    t.index ["workspace_id"], name: "index_blocked_entities_on_workspace_id"
  end

  create_table "build_data_sets", force: :cascade do |t|
    t.integer "asset_types", default: [], array: true
    t.integer "automated_tasks", default: [], array: true
    t.integer "blocked_keywords", default: [], array: true
    t.integer "categories", default: [], array: true
    t.bigint "company_build_id"
    t.datetime "created_at", null: false
    t.integer "custom_forms", default: [], array: true
    t.integer "documents", default: [], array: true
    t.integer "faqs", default: [], array: true
    t.integer "groups", default: [], array: true
    t.integer "people", default: [], array: true
    t.integer "responses", default: [], array: true
    t.datetime "updated_at", null: false
    t.index ["company_build_id"], name: "index_build_data_sets_on_company_build_id"
  end

  create_table "business_hours", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.text "description"
    t.json "holidays"
    t.json "schedule"
    t.string "timezone"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_business_hours_on_company_id"
    t.index ["workspace_id"], name: "index_business_hours_on_workspace_id"
  end

  create_table "cards", force: :cascade do |t|
    t.jsonb "card_attributes"
    t.bigint "cardable_id"
    t.string "cardable_type"
    t.integer "chart_type"
    t.boolean "compare_previous_timeperiod", default: false, null: false
    t.datetime "created_at", null: false
    t.string "description"
    t.boolean "display_labels", default: false, null: false
    t.jsonb "metrics"
    t.string "name"
    t.jsonb "preview_attributes"
    t.jsonb "unselected_fields"
    t.datetime "updated_at", null: false
    t.integer "view_by"
    t.index ["cardable_type", "cardable_id"], name: "index_cards_on_cardable"
  end

  create_table "categories", id: :serial, force: :cascade do |t|
    t.integer "category_type"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "module_type"
    t.bigint "msp_templates_category_id"
    t.string "name"
    t.integer "position"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_categories_on_company_id"
    t.index ["msp_templates_category_id"], name: "index_categories_on_msp_templates_category_id"
    t.index ["workspace_id"], name: "index_categories_on_workspace_id"
  end

  create_table "closing_surveys", force: :cascade do |t|
    t.string "comment"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.bigint "help_ticket_id"
    t.boolean "positive", default: true
    t.datetime "updated_at", precision: nil, null: false
    t.index ["help_ticket_id"], name: "index_closing_surveys_on_help_ticket_id"
  end

  create_table "cloud_asset_attributes", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_asset_id"
    t.string "key"
    t.bigint "managed_asset_id"
    t.datetime "updated_at", precision: nil, null: false
    t.string "value"
    t.index ["discovered_asset_id"], name: "index_cloud_asset_attributes_on_discovered_asset_id"
    t.index ["managed_asset_id"], name: "index_cloud_asset_attributes_on_managed_asset_id"
  end

  create_table "cloud_usage_transactions", force: :cascade do |t|
    t.float "amount"
    t.bigint "company_id"
    t.bigint "company_integration_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_vendor_id"
    t.boolean "is_manual", default: false
    t.string "name"
    t.bigint "product_id"
    t.date "transaction_date"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.index ["company_id", "transaction_date", "name", "company_integration_id", "is_manual", "amount"], name: "index_cloud_usage_transactions_multiple_with_amount"
    t.index ["company_id", "transaction_date", "name", "company_integration_id", "is_manual"], name: "index_cloud_usage_transactions_multiple"
    t.index ["company_integration_id"], name: "index_cloud_usage_transactions_on_company_integration_id"
    t.index ["discovered_vendor_id"], name: "index_cloud_usage_transactions_on_discovered_vendor_id"
    t.index ["product_id"], name: "index_cloud_usage_transactions_on_product_id"
    t.index ["vendor_id"], name: "index_cloud_usage_transactions_on_vendor_id"
  end

  create_table "cognito_logs", force: :cascade do |t|
    t.string "api_type"
    t.datetime "created_at", null: false
    t.jsonb "response"
    t.string "status"
    t.datetime "updated_at", null: false
    t.string "user_email"
    t.bigint "user_id"
    t.index ["user_id"], name: "index_cognito_logs_on_user_id"
  end

  create_table "companies", id: :serial, force: :cascade do |t|
    t.boolean "activate_expired_plans", default: true
    t.string "app_direct_admin_uuid"
    t.string "app_direct_uuid"
    t.boolean "chat_support_enabled", default: true
    t.datetime "created_at", precision: nil, null: false
    t.string "default_logo_url", null: false
    t.string "email"
    t.datetime "fiscal_year", precision: nil
    t.bigint "free_trial_days", default: 30
    t.string "guid"
    t.integer "help_ticket_number", default: 1
    t.string "invite_token"
    t.boolean "is_legacy_company", default: false
    t.boolean "is_reseller_company", default: false
    t.boolean "is_sample_company", default: false
    t.boolean "isolate_workspaces", default: false
    t.datetime "last_logged_in_at", precision: nil
    t.boolean "location_wizard", default: true
    t.string "logo_file_name"
    t.boolean "microsoft_sso_enabled", default: false
    t.boolean "monitoring_ready", default: false
    t.string "name"
    t.string "original_logo_url"
    t.integer "parent_company_id"
    t.string "phone_number"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.string "phone_number_extension"
    t.string "prtg_passhash"
    t.string "prtg_username"
    t.integer "reseller_company_id"
    t.datetime "show_discovered_services", precision: nil
    t.boolean "show_free_modules", default: true
    t.boolean "show_new_plans", default: true
    t.boolean "startup_info_completed", default: false
    t.string "stripe_id"
    t.string "subdomain"
    t.string "timezone", default: "UTC"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.boolean "verified", default: true
    t.index ["name"], name: "index_companies_on_name", unique: true
    t.index ["subdomain"], name: "index_companies_on_subdomain", unique: true
  end

  create_table "company_asset_statuses", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "icon"
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_asset_statuses_on_company_id"
    t.index ["name", "company_id"], name: "index_company_asset_statuses_on_name_and_company_id", unique: true
  end

  create_table "company_asset_tags", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_asset_tags_on_company_id"
    t.index ["name", "company_id"], name: "index_company_asset_tags_on_name_and_company_id", unique: true
  end

  create_table "company_asset_types", force: :cascade do |t|
    t.string "ancestry"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.boolean "has_child", default: false
    t.bigint "msp_templates_asset_type_id"
    t.string "name"
    t.boolean "nested_attributes", default: false
    t.bigint "parent_type_id"
    t.hstore "private_asset_attributes"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["msp_templates_asset_type_id"], name: "index_company_asset_types_on_msp_templates_asset_type_id"
    t.index ["parent_type_id"], name: "index_company_asset_types_on_parent_type_id"
  end

  create_table "company_association_event_logs", force: :cascade do |t|
    t.bigint "child_company_id"
    t.datetime "created_at", null: false
    t.string "event_type"
    t.bigint "parent_company_id"
    t.string "performed_by"
    t.string "performer_user_email"
    t.string "status"
    t.datetime "updated_at", null: false
    t.index ["child_company_id"], name: "index_company_association_event_logs_on_child_company_id"
    t.index ["parent_company_id"], name: "index_company_association_event_logs_on_parent_company_id"
  end

  create_table "company_builds", force: :cascade do |t|
    t.string "build_color"
    t.string "changed_by"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "description"
    t.string "name"
    t.datetime "updated_at", null: false
    t.string "version_name"
    t.index ["company_id"], name: "index_company_builds_on_company_id"
  end

  create_table "company_cache_keys", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.jsonb "key"
    t.bigint "workspace_id", null: false
    t.index ["company_id"], name: "index_company_cache_keys_on_company_id"
    t.index ["workspace_id"], name: "index_company_cache_keys_on_workspace_id"
  end

  create_table "company_credit_cards", force: :cascade do |t|
    t.string "brand"
    t.bigint "company_id"
    t.string "exp_month"
    t.string "exp_year"
    t.string "last4"
    t.string "stripe_card_id"
    t.index ["company_id"], name: "index_company_credit_cards_on_company_id"
  end

  create_table "company_domains", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "domain_name"
    t.boolean "is_registered", default: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_domains_on_company_id"
  end

  create_table "company_integrations", force: :cascade do |t|
    t.boolean "active", default: true
    t.jsonb "alert_info", default: { "failed" => false, "new_asset" => false, "out_of_sync" => false, "not_reporting" => false }, null: false
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "error_message"
    t.integer "failure_count", default: 0, null: false
    t.bigint "integrable_id"
    t.string "integrable_type"
    t.bigint "integration_id"
    t.boolean "is_force_stopped", default: false
    t.datetime "last_synced_at", precision: nil
    t.boolean "notified"
    t.boolean "status"
    t.integer "sync_status"
    t.datetime "updated_at", precision: nil, null: false
    t.string "user_error_message"
    t.index ["company_id"], name: "index_company_integrations_on_company_id"
    t.index ["integrable_type", "integrable_id"], name: "index_company_integrations_on_integrable_type_and_integrable_id"
    t.index ["integration_id"], name: "index_company_integrations_on_integration_id"
  end

  create_table "company_invitations", force: :cascade do |t|
    t.bigint "child_company_id"
    t.datetime "created_at", null: false
    t.string "invite_token"
    t.bigint "parent_company_id"
    t.datetime "updated_at", null: false
    t.index ["child_company_id"], name: "index_company_invitations_on_child_company_id"
    t.index ["parent_company_id"], name: "index_company_invitations_on_parent_company_id"
  end

  create_table "company_mailers", force: :cascade do |t|
    t.bigint "automated_tasks_automated_task_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "default_mailer_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["automated_tasks_automated_task_id"], name: "index_company_mailers_on_automated_tasks_automated_task_id"
    t.index ["company_id", "default_mailer_id"], name: "index_company_mailers_on_company_id_and_default_mailer_id", unique: true
    t.index ["company_id"], name: "index_company_mailers_on_company_id"
    t.index ["default_mailer_id"], name: "index_company_mailers_on_default_mailer_id"
    t.index ["workspace_id", "company_id", "default_mailer_id"], name: "idx_uniq_company_mailers", unique: true
    t.index ["workspace_id"], name: "index_company_mailers_on_workspace_id"
  end

  create_table "company_trackings", force: :cascade do |t|
    t.string "campaign"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "gclid"
    t.string "medium"
    t.bigint "registration_emails_id"
    t.string "source"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_company_trackings_on_company_id"
    t.index ["registration_emails_id"], name: "index_company_trackings_on_registration_emails_id"
  end

  create_table "company_user_activities", force: :cascade do |t|
    t.integer "activity_action"
    t.integer "activity_type"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.jsonb "data", default: {}
    t.bigint "owner_id"
    t.string "source"
    t.datetime "updated_at", null: false
    t.index ["company_user_id"], name: "index_company_user_activities_on_company_user_id"
    t.index ["owner_id"], name: "index_company_user_activities_on_owner_id"
  end

  create_table "company_user_mailers", force: :cascade do |t|
    t.bigint "company_user_id"
    t.bigint "default_mailer_id"
    t.boolean "opted_in", default: true
    t.index ["company_user_id"], name: "index_company_user_mailers_on_company_user_id"
    t.index ["default_mailer_id"], name: "index_company_user_mailers_on_default_mailer_id"
  end

  create_table "company_users", id: :serial, force: :cascade do |t|
    t.boolean "active_status", default: true
    t.string "app_direct_uuid"
    t.datetime "archived_at", precision: nil
    t.string "avatar_url"
    t.integer "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_id"
    t.bigint "default_workspace_id"
    t.datetime "granted_access_at", precision: nil
    t.bigint "granted_by_id"
    t.string "guid"
    t.boolean "helpdesk_agent", default: false
    t.boolean "invitation_delivered"
    t.datetime "invitation_due_at", precision: nil
    t.string "invite_token"
    t.boolean "is_admin", default: false
    t.boolean "is_sample_company_user", default: false
    t.datetime "last_logged_in_at", precision: nil
    t.integer "location_id"
    t.boolean "locked"
    t.integer "mfa_attempts_remaining"
    t.string "mfa_code"
    t.datetime "mfa_code_generated_at", precision: nil
    t.boolean "mfa_enabled", default: false
    t.string "mfa_secret_key"
    t.boolean "mfa_verified", default: true
    t.datetime "mfa_verified_at", precision: nil
    t.string "mobile_phone"
    t.string "mobile_phone_country_code", default: "US"
    t.string "mobile_phone_country_code_number", default: "1"
    t.boolean "notify_companyuser", default: true
    t.boolean "notify_contract", default: true
    t.boolean "notify_helpticket", default: true
    t.boolean "notify_managedasset", default: true
    t.boolean "notify_telecomservice", default: true
    t.boolean "notify_vendor", default: true
    t.boolean "out_of_office", default: false
    t.bigint "parent_company_user_id"
    t.boolean "required_device_verification", default: true
    t.boolean "self_mfa_enabled", default: false
    t.integer "self_onboarding", default: 0
    t.jsonb "suggestions", default: { "helpdesk" => { "ticket_assignment_suggestion" => { "create_count" => 0, "dismiss_count" => 0 }, "ticket_list_priority_suggestion" => { "create_count" => 0, "dismiss_count" => 0 }, "ticket_comment_user_mention_suggestion" => { "create_count" => 0, "dismiss_count" => 0 } } }
    t.string "type", default: "CompanyMember"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.string "work_phone"
    t.string "work_phone_country_code", default: "US"
    t.string "work_phone_country_code_number", default: "1"
    t.string "work_phone_extension"
    t.index ["archived_at"], name: "index_company_users_on_archived_at"
    t.index ["company_id", "user_id"], name: "index_company_users_on_company_id_and_user_id", unique: true
    t.index ["company_id"], name: "index_company_users_on_company_id", where: "(archived_at IS NULL)"
    t.index ["contributor_id"], name: "index_company_users_on_contributor_id"
    t.index ["custom_form_id"], name: "index_company_users_on_custom_form_id"
    t.index ["granted_by_id"], name: "index_company_users_on_granted_by_id"
    t.index ["guid"], name: "index_company_users_on_guid", unique: true, where: "(archived_at IS NULL)"
    t.index ["parent_company_user_id"], name: "index_company_users_on_parent_company_user_id"
    t.index ["user_id"], name: "index_company_users_on_user_id", where: "(archived_at IS NULL)"
  end

  create_table "computer_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "device_id"
    t.string "disk_encryption"
    t.string "disk_free_space"
    t.string "domain_name"
    t.string "hard_drive"
    t.string "hardware_version"
    t.string "hostname"
    t.bigint "managed_asset_id"
    t.string "memory"
    t.string "network"
    t.string "processor"
    t.string "processor_architecture"
    t.string "processor_cores"
    t.string "processor_logical_cores"
    t.string "remote_ip_address"
    t.decimal "screen_size"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "contract_and_app_histories", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "company_user_id"
    t.bigint "contract_id"
    t.datetime "created_at", null: false
    t.string "record_type"
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["company_user_id"], name: "index_contract_and_app_histories_on_company_user_id"
    t.index ["contract_id"], name: "index_contract_and_app_histories_on_contract_id"
  end

  create_table "contract_attachments", id: :serial, force: :cascade do |t|
    t.integer "contract_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["contract_id"], name: "index_contract_attachments_on_contract_id"
  end

  create_table "contract_contacts", force: :cascade do |t|
    t.bigint "company_user_id"
    t.bigint "contract_id"
    t.index ["company_user_id"], name: "index_contract_contacts_on_company_user_id"
    t.index ["contract_id"], name: "index_contract_contacts_on_contract_id"
  end

  create_table "contract_departments", force: :cascade do |t|
    t.bigint "contract_id"
    t.datetime "created_at", null: false
    t.bigint "department_id"
    t.datetime "updated_at", null: false
    t.index ["contract_id"], name: "index_contract_departments_on_contract_id"
    t.index ["department_id"], name: "index_contract_departments_on_department_id"
  end

  create_table "contract_locations", force: :cascade do |t|
    t.bigint "contract_id"
    t.bigint "location_id"
    t.index ["contract_id"], name: "index_contract_locations_on_contract_id"
    t.index ["location_id"], name: "index_contract_locations_on_location_id"
  end

  create_table "contract_preferences", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.json "preference"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_contract_preferences_on_company_id", unique: true
  end

  create_table "contract_tags", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "contract_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_contract_tags_on_company_id"
    t.index ["contract_id"], name: "index_contract_tags_on_contract_id"
  end

  create_table "contracts", id: :serial, force: :cascade do |t|
    t.boolean "all_locations", default: false
    t.boolean "archived", default: false
    t.integer "category_id", null: false
    t.integer "company_id"
    t.integer "contract_type", default: 0
    t.float "contract_value_amount", default: 0.0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.date "end_date"
    t.string "guid"
    t.boolean "is_cloned", default: false
    t.float "monthly_cost", default: 0.0
    t.string "name"
    t.string "notes"
    t.integer "notice_period"
    t.bigint "parent_id"
    t.integer "position"
    t.text "product"
    t.bigint "product_id"
    t.string "reference"
    t.date "start_date"
    t.boolean "track_spend", default: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.string "vendor_name"
    t.text "vendor_notes"
    t.index ["category_id"], name: "index_contracts_on_category_id"
    t.index ["company_id"], name: "index_contracts_on_company_id"
    t.index ["creator_id"], name: "index_contracts_on_creator_id"
    t.index ["guid"], name: "index_contracts_on_guid", unique: true
    t.index ["product_id"], name: "index_contracts_on_product_id"
    t.index ["vendor_id"], name: "index_contracts_on_vendor_id"
  end

  create_table "contributor_actionable_alerts", force: :cascade do |t|
    t.bigint "actionable_alert_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "dismissed_at", precision: nil
    t.datetime "updated_at", precision: nil, null: false
    t.index ["actionable_alert_id"], name: "index_contributor_actionable_alerts_on_actionable_alert_id"
    t.index ["contributor_id"], name: "index_contributor_actionable_alerts_on_contributor_id"
  end

  create_table "contributors", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_contributors_on_company_id"
  end

  create_table "costs", id: :serial, force: :cascade do |t|
    t.integer "approaching_end_of_life"
    t.bigint "asset_lifecycle_id"
    t.float "cost"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "depreciation_id"
    t.integer "lifecycle_type"
    t.integer "managed_asset_id"
    t.string "po", default: ""
    t.float "purchase_price", default: 0.0
    t.float "replacement_cost", default: 0.0
    t.float "salvage", default: 0.0
    t.datetime "updated_at", precision: nil, null: false
    t.integer "useful_life"
    t.index ["asset_lifecycle_id"], name: "index_costs_on_asset_lifecycle_id"
    t.index ["depreciation_id"], name: "index_costs_on_depreciation_id"
    t.index ["managed_asset_id"], name: "index_costs_on_managed_asset_id", unique: true
  end

  create_table "cron_locks", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "ended_at", precision: nil
    t.string "key"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["key"], name: "index_cron_locks_on_key", unique: true
  end

  create_table "custom_domains", force: :cascade do |t|
    t.boolean "active_domain", default: false
    t.integer "certificate_type", default: 0
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "intermediary_certificate"
    t.bigint "load_balancer_id"
    t.string "name"
    t.string "primary_certificate"
    t.string "primary_key"
    t.datetime "updated_at", null: false
    t.boolean "verified", default: false
    t.index ["company_id"], name: "index_custom_domains_on_company_id"
    t.index ["load_balancer_id"], name: "index_custom_domains_on_load_balancer_id"
  end

  create_table "custom_form_attachments", force: :cascade do |t|
    t.string "attachment_content_type"
    t.string "attachment_file_name"
    t.integer "attachment_file_size"
    t.datetime "attachment_updated_at", precision: nil
    t.boolean "auto_add", default: false
    t.bigint "company_id"
    t.string "content_id", default: ""
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_value_id"
    t.integer "library_document_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_custom_form_attachments_on_company_id"
    t.index ["custom_form_value_id"], name: "index_custom_form_attachments_on_custom_form_value_id"
  end

  create_table "custom_form_field_permissions", force: :cascade do |t|
    t.boolean "can_edit", default: false
    t.boolean "can_view", default: false
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_field_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["contributor_id"], name: "index_custom_form_field_permissions_on_contributor_id"
    t.index ["custom_form_field_id"], name: "index_custom_form_field_permissions_on_custom_form_field_id"
  end

  create_table "custom_form_field_templates", force: :cascade do |t|
    t.string "audience"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_template_id"
    t.string "default_value"
    t.integer "field_attribute_type"
    t.string "label"
    t.string "name"
    t.text "note"
    t.text "options"
    t.integer "order_position"
    t.boolean "permit_edit_default", default: true
    t.boolean "permit_view_default", default: true
    t.boolean "private", default: false
    t.boolean "required", default: false
    t.text "sort_list"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["custom_form_template_id"], name: "index_custom_form_field_templates_on_custom_form_template_id"
  end

  create_table "custom_form_fields", force: :cascade do |t|
    t.string "audience"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_id"
    t.string "default_value"
    t.integer "field_attribute_type"
    t.boolean "is_followers", default: false
    t.boolean "is_followers_field", default: false
    t.string "label"
    t.text "list_type"
    t.bigint "msp_templates_custom_form_field_id"
    t.string "name"
    t.text "note"
    t.text "options"
    t.integer "order_position"
    t.boolean "permit_edit_default", default: true
    t.boolean "permit_view_default", default: true
    t.boolean "private", default: false
    t.boolean "required", default: false
    t.boolean "required_to_close", default: false
    t.text "sort_list"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["custom_form_id"], name: "index_custom_form_fields_on_custom_form_id"
    t.index ["field_attribute_type"], name: "idx_custom_form_fields_on_field_attribute_type"
    t.index ["label"], name: "index_custom_form_fields_on_label"
    t.index ["msp_templates_custom_form_field_id"], name: "index_custom_form_fields_on_msp_templates_custom_form_field_id"
    t.index ["name"], name: "index_custom_form_fields_on_name"
  end

  create_table "custom_form_groups", force: :cascade do |t|
    t.bigint "custom_form_id"
    t.bigint "group_id"
    t.index ["custom_form_id"], name: "index_custom_form_groups_on_custom_form_id"
    t.index ["group_id"], name: "index_custom_form_groups_on_group_id"
  end

  create_table "custom_form_templates", force: :cascade do |t|
    t.integer "company_module"
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.string "form_name"
    t.string "image_url"
    t.boolean "is_active"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "custom_form_values", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.bigint "custom_form_field_id"
    t.bigint "custom_form_id"
    t.bigint "help_ticket_id"
    t.bigint "module_id"
    t.string "module_type"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "value_int"
    t.text "value_str"
    t.tsvector "value_str_tokens"
    t.index ["company_id"], name: "index_custom_form_values_on_company_id"
    t.index ["creator_id"], name: "index_custom_form_values_on_creator_id"
    t.index ["custom_form_field_id"], name: "index_custom_form_values_on_custom_form_field_id"
    t.index ["custom_form_id"], name: "index_custom_form_values_on_custom_form_id"
    t.index ["help_ticket_id"], name: "index_custom_form_values_on_help_ticket_id"
    t.index ["module_id", "module_type"], name: "index_custom_form_values_on_module_id_and_module_type"
    t.index ["module_type", "module_id"], name: "index_custom_form_values_on_module_type_and_module_id"
    t.index ["value_int"], name: "index_custom_form_values_on_value_int"
    t.index ["value_str_tokens"], name: "idx_value_str_tokens", using: :gin
  end

  create_table "custom_forms", force: :cascade do |t|
    t.bigint "automated_tasks_automated_task_id"
    t.boolean "collect_closing_survey", default: false
    t.string "color"
    t.bigint "company_id"
    t.integer "company_module"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "default", default: false
    t.string "description"
    t.string "form_name"
    t.string "icon"
    t.boolean "is_active", default: true
    t.boolean "is_draft", default: false
    t.bigint "msp_templates_custom_form_id"
    t.integer "order", default: 1
    t.bigint "original_form_id"
    t.boolean "show_in_open_portal", default: true
    t.boolean "show_people_list", default: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["automated_tasks_automated_task_id"], name: "index_custom_forms_on_automated_tasks_automated_task_id"
    t.index ["company_id"], name: "index_custom_forms_on_company_id"
    t.index ["msp_templates_custom_form_id"], name: "index_custom_forms_on_msp_templates_custom_form_id"
    t.index ["original_form_id"], name: "index_custom_forms_on_original_form_id"
    t.index ["workspace_id"], name: "index_custom_forms_on_workspace_id"
  end

  create_table "custom_reports", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.string "download_link", default: ""
    t.date "end_date"
    t.boolean "is_analytics_report", default: false
    t.integer "module_name"
    t.string "name"
    t.float "percentage"
    t.boolean "ready_to_download", default: false
    t.string "report_content_type"
    t.string "report_file_name"
    t.date "start_date"
    t.integer "timeframe"
    t.string "track_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_custom_reports_on_company_id"
    t.index ["creator_id"], name: "index_custom_reports_on_creator_id"
    t.index ["workspace_id"], name: "index_custom_reports_on_workspace_id"
  end

  create_table "custom_survey_actions", force: :cascade do |t|
    t.string "action_type"
    t.datetime "created_at", null: false
    t.bigint "custom_survey_question_id"
    t.bigint "custom_survey_rule_id"
    t.datetime "updated_at", null: false
    t.index ["custom_survey_question_id"], name: "index_custom_survey_actions_on_custom_survey_question_id"
    t.index ["custom_survey_rule_id"], name: "index_custom_survey_actions_on_custom_survey_rule_id"
  end

  create_table "custom_survey_choices", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "custom_survey_question_id"
    t.string "emoji"
    t.integer "order", default: 0
    t.integer "score"
    t.string "text"
    t.datetime "updated_at", null: false
    t.index ["custom_survey_question_id"], name: "index_custom_survey_choices_on_custom_survey_question_id"
  end

  create_table "custom_survey_questions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "custom_survey_id"
    t.string "question_text"
    t.string "question_type"
    t.datetime "updated_at", null: false
    t.index ["custom_survey_id"], name: "index_custom_survey_questions_on_custom_survey_id"
  end

  create_table "custom_survey_responses", force: :cascade do |t|
    t.string "comment", default: [], array: true
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", null: false
    t.bigint "custom_survey_id"
    t.bigint "help_ticket_id"
    t.string "name"
    t.jsonb "questions_data", default: []
    t.string "score"
    t.string "status"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_custom_survey_responses_on_company_id"
    t.index ["contributor_id"], name: "index_custom_survey_responses_on_contributor_id"
    t.index ["custom_survey_id"], name: "index_custom_survey_responses_on_custom_survey_id"
    t.index ["help_ticket_id"], name: "index_custom_survey_responses_on_help_ticket_id"
    t.index ["workspace_id"], name: "index_custom_survey_responses_on_workspace_id"
  end

  create_table "custom_survey_rules", force: :cascade do |t|
    t.string "condition"
    t.datetime "created_at", null: false
    t.bigint "custom_survey_choice_id"
    t.bigint "custom_survey_id"
    t.bigint "custom_survey_question_id"
    t.datetime "updated_at", null: false
    t.index ["custom_survey_choice_id"], name: "index_custom_survey_rules_on_custom_survey_choice_id"
    t.index ["custom_survey_id"], name: "index_custom_survey_rules_on_custom_survey_id"
    t.index ["custom_survey_question_id"], name: "index_custom_survey_rules_on_custom_survey_question_id"
  end

  create_table "custom_survey_triggers", force: :cascade do |t|
    t.boolean "allow_agents"
    t.string "button_text"
    t.json "conditions"
    t.datetime "created_at", null: false
    t.bigint "custom_survey_id"
    t.bigint "custom_survey_question_id"
    t.boolean "display_question"
    t.boolean "match_all"
    t.boolean "regulate_emails"
    t.datetime "updated_at", null: false
    t.index ["custom_survey_id"], name: "index_custom_survey_triggers_on_custom_survey_id"
    t.index ["custom_survey_question_id"], name: "index_custom_survey_triggers_on_custom_survey_question_id"
  end

  create_table "custom_surveys", force: :cascade do |t|
    t.string "background_color"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.boolean "custom_design"
    t.integer "custom_form_ids", default: [], array: true
    t.string "description"
    t.boolean "is_default", default: false
    t.string "title"
    t.datetime "updated_at", null: false
    t.boolean "visible"
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_custom_surveys_on_company_id"
    t.index ["workspace_id"], name: "index_custom_surveys_on_workspace_id"
  end

  create_table "dashboard_preferences", force: :cascade do |t|
    t.string "cards"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_dashboard_preferences_on_company_user_id"
  end

  create_table "default_analytics_report_templates", force: :cascade do |t|
    t.jsonb "analytics_metrics"
    t.datetime "created_at", null: false
    t.text "description"
    t.string "name"
    t.string "report_type"
    t.datetime "updated_at", null: false
  end

  create_table "default_asset_statuses", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "icon"
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_default_asset_statuses_on_name", unique: true
  end

  create_table "default_categories", force: :cascade do |t|
    t.string "name"
  end

  create_table "default_departments", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "name", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_default_departments_on_name", unique: true
  end

  create_table "default_helpdesk_settings", force: :cascade do |t|
    t.text "description"
    t.integer "order", default: 0
    t.string "section_name"
    t.string "setting_type"
    t.string "title"
  end

  create_table "default_mailers", force: :cascade do |t|
    t.text "description"
    t.string "event"
    t.string "module_name"
  end

  create_table "default_products", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "default_category_id"
    t.bigint "default_vendor_id"
    t.string "logo_url"
    t.string "name"
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.index ["default_category_id"], name: "index_default_products_on_default_category_id"
    t.index ["default_vendor_id"], name: "index_default_products_on_default_vendor_id"
  end

  create_table "default_vendors", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "default_category_id", null: false
    t.text "friendly_name", default: [], array: true
    t.boolean "is_cloud_platform", default: false
    t.string "logo_url"
    t.string "name"
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
  end

  create_table "departments", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.integer "position"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_departments_on_company_id"
    t.index ["name", "company_id"], name: "index_departments_on_name_and_company_id", unique: true
  end

  create_table "depreciations", force: :cascade do |t|
    t.bigint "company_id"
    t.float "depreciation_factor", default: 1.0
    t.integer "depreciation_type"
    t.text "description"
    t.string "name"
    t.integer "useful_life_in_years"
    t.index ["company_id"], name: "index_depreciations_on_company_id"
  end

  create_table "discovered_asset_histories", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.jsonb "data", default: {}, null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_discovered_asset_histories_on_company_id"
    t.index ["company_user_id"], name: "index_discovered_asset_histories_on_company_user_id"
  end

  create_table "discovered_asset_preferences", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.json "preference"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_discovered_asset_preferences_on_company_id"
  end

  create_table "discovered_asset_types", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at"
    t.string "name"
    t.datetime "updated_at"
    t.index ["company_id"], name: "index_discovered_asset_types_on_company_id"
  end

  create_table "discovered_assets", id: :serial, force: :cascade do |t|
    t.string "asset_tag"
    t.string "asset_type"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "discovered_asset_type"
    t.bigint "discovered_asset_type_id"
    t.jsonb "discovery_details", default: ""
    t.string "display_name"
    t.string "firmware"
    t.bigint "integrations_locations_id"
    t.string "ip_address"
    t.datetime "last_check_in_time"
    t.datetime "last_synced_at", precision: nil
    t.string "mac_address"
    t.text "mac_addresses", default: [], array: true
    t.jsonb "mac_addresses_info"
    t.string "machine_serial_no", default: ""
    t.bigint "managed_asset_id"
    t.string "managed_by"
    t.string "manufacturer"
    t.string "meraki_network_id"
    t.string "model"
    t.jsonb "optional_details", default: ""
    t.jsonb "optional_details_old"
    t.string "os"
    t.string "os_name"
    t.string "os_serial_no"
    t.string "os_version"
    t.bigint "probe_location_id"
    t.jsonb "protocols"
    t.text "secondary_mac_addresses", default: [], array: true
    t.integer "source", default: 0
    t.integer "status", default: 1
    t.string "system_up_time"
    t.string "system_uuid", default: ""
    t.datetime "updated_at", precision: nil, null: false
    t.string "used_by"
    t.index ["company_id", "machine_serial_no", "status"], name: "index_discovered_assets_on_company_id_serial_no_status"
    t.index ["discovered_asset_type_id"], name: "index_discovered_assets_on_discovered_asset_type_id"
    t.index ["integrations_locations_id"], name: "index_discovered_assets_on_integrations_locations_id"
    t.index ["machine_serial_no", "company_id"], name: "index_discovered_assets_on_serial_number_and_company_id", unique: true, where: "((machine_serial_no)::text <> ''::text)"
    t.index ["managed_asset_id"], name: "index_discovered_assets_on_managed_asset_id", unique: true
    t.index ["probe_location_id"], name: "index_discovered_assets_on_probe_location_id"
  end

  create_table "discovered_assets_hardware_details", force: :cascade do |t|
    t.string "available_memory"
    t.string "black_toner_left_percent"
    t.string "black_toner_name"
    t.datetime "created_at", null: false
    t.string "cyan_toner_left_percent"
    t.string "cyan_toner_name"
    t.string "device_id"
    t.bigint "discovered_asset_id"
    t.string "disk_encryption"
    t.string "disk_free_space"
    t.string "dns"
    t.string "domain_name"
    t.string "gateway"
    t.string "hard_drive"
    t.string "hardware_version"
    t.string "hostname"
    t.string "imei"
    t.string "magenta_toner_left_percent"
    t.string "magenta_toner_name"
    t.string "memory"
    t.string "network"
    t.string "personalities_installed"
    t.string "ports"
    t.string "processor"
    t.string "processor_architecture"
    t.string "processor_cores"
    t.string "processor_logical_cores"
    t.string "product_number"
    t.string "public_ip"
    t.string "remote_ip_address"
    t.string "screen_size"
    t.string "service_id"
    t.string "ssids"
    t.string "total_page_count"
    t.string "total_pcl_five_page_count"
    t.string "total_pcl_six_page_count"
    t.string "total_post_script_page_count"
    t.datetime "updated_at", null: false
    t.string "used_memory"
    t.string "wan_ips"
    t.string "yellow_toner_left_percent"
    t.string "yellow_toner_name"
    t.index ["discovered_asset_id"], name: "index_discovered_assets_hardware_details_on_discovered_asset_id"
  end

  create_table "discovered_data_services", force: :cascade do |t|
    t.string "as"
    t.string "city"
    t.bigint "company_id"
    t.string "country"
    t.string "country_code"
    t.datetime "created_at", precision: nil, null: false
    t.string "isp"
    t.float "lat"
    t.float "lon"
    t.string "org"
    t.string "query"
    t.string "region"
    t.string "region_name"
    t.string "status"
    t.string "timezone"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "zip"
    t.index ["company_id"], name: "index_discovered_data_services_on_company_id"
  end

  create_table "discovered_user_histories", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.bigint "discovered_user_id"
    t.datetime "updated_at", null: false
    t.jsonb "values", default: {}, null: false
    t.index ["company_id"], name: "index_discovered_user_histories_on_company_id"
    t.index ["discovered_user_id"], name: "index_discovered_user_histories_on_discovered_user_id"
  end

  create_table "discovered_user_sources", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_user_id"
    t.integer "source"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["discovered_user_id"], name: "index_discovered_user_sources_on_discovered_user_id"
  end

  create_table "discovered_users", force: :cascade do |t|
    t.string "account_name"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "department"
    t.string "email"
    t.string "first_name"
    t.boolean "is_viewed", default: false
    t.string "last_name"
    t.datetime "last_synced_at", precision: nil
    t.bigint "location_id"
    t.string "mobile_phone"
    t.string "principal_name"
    t.integer "status", default: 0
    t.string "supervisor"
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
    t.string "work_phone"
    t.index "company_id, lower((email)::text)", name: "index_discovered_users_on_company_id_lower_email_id"
    t.index ["company_id"], name: "index_discovered_users_on_company_id"
    t.index ["email", "company_id"], name: "index_discovered_users_on_email_and_company_id", unique: true
  end

  create_table "discovered_vendors", force: :cascade do |t|
    t.bigint "category_id"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.text "friendly_name", default: [], array: true
    t.boolean "is_cloud_platform", default: false
    t.string "logo_url"
    t.citext "name"
    t.integer "status", default: 0
    t.string "tag"
    t.datetime "updated_at", null: false
    t.string "url"
    t.bigint "vendor_id"
    t.index ["category_id"], name: "index_discovered_vendors_on_category_id"
    t.index ["company_id"], name: "index_discovered_vendors_on_company_id"
    t.index ["name", "company_id"], name: "discovered_vendors_name_company_id_key", unique: true
    t.index ["vendor_id"], name: "index_discovered_vendors_on_vendor_id"
  end

  create_table "discovered_voice_services", force: :cascade do |t|
    t.string "carrier_mobile_country_code"
    t.string "carrier_mobile_network_code"
    t.string "carrier_name"
    t.string "carrier_type"
    t.bigint "company_id"
    t.string "country_code"
    t.datetime "created_at", precision: nil, null: false
    t.string "national_format"
    t.bigint "phone_number_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_discovered_voice_services_on_company_id"
    t.index ["phone_number_id"], name: "index_discovered_voice_services_on_phone_number_id"
  end

  create_table "discovery_apps", id: :serial, force: :cascade do |t|
    t.integer "app_type", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.string "discovery_app_link"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.float "version"
  end

  create_table "email_domains", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "domain", null: false
    t.integer "status"
    t.datetime "updated_at", null: false
  end

  create_table "email_formats", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "enabled", default: false
    t.jsonb "footer_customization"
    t.text "footer_text"
    t.jsonb "header_customization"
    t.text "header_text"
    t.boolean "hide_footer", default: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_email_formats_on_company_id"
  end

  create_table "email_templates", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "email_body"
    t.boolean "is_default", default: false
    t.string "subject_title"
    t.string "template_name"
    t.string "template_title"
    t.string "template_type"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_email_templates_on_company_id"
    t.index ["workspace_id"], name: "index_email_templates_on_workspace_id"
  end

  create_table "error_notifications", force: :cascade do |t|
    t.string "class_name"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "external_id"
    t.bigint "fallible_id"
    t.string "fallible_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["fallible_type", "fallible_id"], name: "index_error_notifications_on_fallible_type_and_fallible_id"
  end

  create_table "event_logs", force: :cascade do |t|
    t.integer "activity_type"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "data", default: {}
    t.bigint "entity_id"
    t.string "entity_type"
    t.integer "module_name"
    t.bigint "owner_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_event_logs_on_company_id"
    t.index ["entity_type", "entity_id"], name: "index_event_logs_on_entity"
    t.index ["owner_id"], name: "index_event_logs_on_owner_id"
    t.index ["workspace_id"], name: "index_event_logs_on_workspace_id"
  end

  create_table "expanded_form_field_permissions", force: :cascade do |t|
    t.bigint "contributor_id"
    t.datetime "created_at", null: false
    t.bigint "custom_form_field_id"
    t.string "permission_type"
    t.datetime "updated_at", null: false
    t.index ["contributor_id"], name: "index_expanded_form_field_permissions_on_contributor_id"
    t.index ["custom_form_field_id"], name: "index_expanded_form_field_permissions_on_custom_form_field_id"
  end

  create_table "expanded_privileges", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil
    t.string "name"
    t.string "permission_type"
    t.string "sources", default: [], array: true
    t.datetime "updated_at", precision: nil
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_expanded_privileges_on_company_id"
    t.index ["contributor_id", "workspace_id", "company_id", "name", "permission_type"], name: "idx_expanded_privileges_unique", unique: true
    t.index ["contributor_id"], name: "index_expanded_privileges_on_contributor_id"
    t.index ["workspace_id"], name: "index_expanded_privileges_on_workspace_id"
  end

  create_table "expensify_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "user_id"
    t.string "user_secret"
    t.index ["company_id"], name: "index_expensify_configs_on_company_id"
  end

  create_table "experience_points", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "points", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_experience_points_on_company_user_id"
  end

  create_table "feature_request_attachments", force: :cascade do |t|
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.bigint "feature_request_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["creator_id"], name: "index_feature_request_attachments_on_creator_id"
    t.index ["feature_request_id"], name: "index_feature_request_attachments_on_feature_request_id"
  end

  create_table "feature_request_comments", force: :cascade do |t|
    t.text "comment_body"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.bigint "feature_request_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["creator_id"], name: "index_feature_request_comments_on_creator_id"
    t.index ["feature_request_id"], name: "index_feature_request_comments_on_feature_request_id"
  end

  create_table "feature_request_images", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "feature_request_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["feature_request_id"], name: "index_feature_request_images_on_feature_request_id"
  end

  create_table "feature_request_votes", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "feature_request_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "vote_type", default: 0, null: false
    t.index ["company_user_id"], name: "index_feature_request_votes_on_company_user_id"
    t.index ["feature_request_id"], name: "index_feature_request_votes_on_feature_request_id"
  end

  create_table "feature_requests", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.text "description"
    t.integer "feature_state", default: 0, null: false
    t.integer "module_type"
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["creator_id"], name: "index_feature_requests_on_creator_id"
  end

  create_table "field_mappings", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "entity_id"
    t.string "entity_type"
    t.jsonb "mapping"
    t.datetime "updated_at", null: false
    t.index ["entity_type", "entity_id"], name: "index_field_mappings_on_entity"
  end

  create_table "field_position_templates", force: :cascade do |t|
    t.bigint "custom_form_field_template_id"
    t.integer "position"
    t.index ["custom_form_field_template_id"], name: "index_field_position_templates_on_custom_form_field_template_id"
  end

  create_table "field_positions", force: :cascade do |t|
    t.bigint "custom_form_field_id"
    t.integer "position"
    t.index ["custom_form_field_id"], name: "index_field_positions_on_custom_form_field_id"
  end

  create_table "gamification_achievements", force: :cascade do |t|
    t.integer "achievement_type"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "earned", default: false
    t.integer "level"
    t.string "name"
    t.integer "points"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "value"
  end

  create_table "gamification_achievements_assignments", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "gamification_achievement_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_gamification_achievements_assignments_on_company_user_id"
    t.index ["gamification_achievement_id"], name: "index_achievements_assignments_on_gamification_achievement_id"
  end

  create_table "general_transaction_invoices", force: :cascade do |t|
    t.bigint "general_transaction_id"
    t.bigint "invoice_id"
    t.index ["general_transaction_id"], name: "index_general_transaction_invoices_on_general_transaction_id"
    t.index ["invoice_id"], name: "index_general_transaction_invoices_on_invoice_id"
  end

  create_table "general_transaction_tags", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "general_transaction_id"
    t.string "tag"
    t.index ["company_id"], name: "index_general_transaction_tags_on_company_id"
    t.index ["general_transaction_id", "tag"], name: "idx_uniq_general_transactions", unique: true
    t.index ["general_transaction_id"], name: "index_general_transaction_tags_on_general_transaction_id"
  end

  create_table "general_transactions", force: :cascade do |t|
    t.float "amount"
    t.integer "archival_preference", default: 0, null: false
    t.string "category_id"
    t.bigint "company_id"
    t.bigint "company_integration_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.bigint "discovered_vendor_id"
    t.string "import_guid"
    t.boolean "is_active", default: true
    t.boolean "is_manual", default: false
    t.bigint "location_id"
    t.boolean "manually_edited", default: false
    t.text "notes"
    t.float "percent_match"
    t.bigint "plaid_account_id"
    t.string "primary_category"
    t.bigint "product_id"
    t.string "product_name"
    t.string "recommended_match"
    t.date "recurrance_end_date"
    t.boolean "recurred", default: false
    t.boolean "recurring", default: false
    t.boolean "scheduled", default: false
    t.string "secondary_category"
    t.integer "status", default: 2
    t.string "tertiary_category"
    t.text "transaction_category"
    t.date "transaction_date", null: false
    t.string "transaction_id"
    t.integer "transaction_recurred_from_id"
    t.string "transaction_type"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.index ["company_integration_id"], name: "index_general_transactions_on_company_integration_id"
    t.index ["discovered_vendor_id"], name: "index_general_transactions_on_discovered_vendor_id"
    t.index ["location_id"], name: "index_general_transactions_on_location_id"
    t.index ["product_id"], name: "index_general_transactions_on_product_id"
  end

  create_table "global_email_blockings", force: :cascade do |t|
    t.integer "company_ids", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "google_assets_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "import_type", default: 1, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_google_assets_configs_on_company_id"
    t.index ["company_user_id"], name: "index_google_assets_configs_on_company_user_id"
  end

  create_table "google_assets_projects", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_asset_id"
    t.string "error_message"
    t.bigint "google_assets_config_id"
    t.boolean "is_valid", default: true
    t.bigint "managed_asset_id"
    t.string "project_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["discovered_asset_id"], name: "index_google_assets_projects_on_discovered_asset_id"
    t.index ["google_assets_config_id"], name: "index_google_assets_projects_on_google_assets_config_id"
    t.index ["managed_asset_id"], name: "index_google_assets_projects_on_managed_asset_id"
    t.index ["project_id", "google_assets_config_id"], name: "unique_google_project_index", unique: true
  end

  create_table "google_workspace_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.datetime "expires_at"
    t.integer "import_type", default: 1, null: false
    t.boolean "is_force_stopped", default: false
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_google_workspace_configs_on_company_id"
    t.index ["company_user_id"], name: "index_google_workspace_configs_on_company_user_id"
  end

  create_table "group_members", force: :cascade do |t|
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "group_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["contributor_id"], name: "index_group_members_on_contributor_id"
    t.index ["group_id"], name: "index_group_members_on_group_id"
  end

  create_table "group_privileges", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "group_id"
    t.string "name"
    t.string "permission_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["group_id"], name: "index_group_privileges_on_group_id"
  end

  create_table "groups", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "default", default: false
    t.bigint "granted_by_id"
    t.string "guid"
    t.boolean "include_all", default: false
    t.boolean "mfa_enabled", default: false
    t.bigint "msp_templates_group_id"
    t.string "name", null: false
    t.bigint "parent_company_group_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_groups_on_company_id"
    t.index ["contributor_id"], name: "index_groups_on_contributor_id"
    t.index ["granted_by_id"], name: "index_groups_on_granted_by_id"
    t.index ["guid"], name: "index_groups_on_guid", unique: true
    t.index ["msp_templates_group_id"], name: "index_groups_on_msp_templates_group_id"
    t.index ["name", "company_id"], name: "groups_name_company_id_key", unique: true
    t.index ["workspace_id"], name: "index_groups_on_workspace_id"
  end

  create_table "gsuite_ad_configs", force: :cascade do |t|
    t.text "client_id"
    t.string "code"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "data"
    t.text "excluded_attributes", default: [], array: true
    t.text "expiration_time"
    t.string "refresh_token"
    t.string "scope"
    t.boolean "sync_all_users", default: false
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_ad_configs_on_company_id"
  end

  create_table "gsuite_ad_groups", force: :cascade do |t|
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "external_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_ad_groups_on_company_id"
  end

  create_table "gsuite_ad_organizations", force: :cascade do |t|
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "org_unit_path"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_ad_organizations_on_company_id"
  end

  create_table "gsuite_app_users", force: :cascade do |t|
    t.bigint "app_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "user_id"
    t.index ["user_id", "app_id"], name: "index_gsuite_app_users_on_user_id_app_id"
  end

  create_table "gsuite_apps", force: :cascade do |t|
    t.integer "app_type"
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "external_client_id"
    t.jsonb "meta_data"
    t.string "name"
    t.integer "number_users"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_apps_on_company_id"
    t.index ["config_id", "name"], name: "index_gsuite_apps_on_config_id_serial_name"
    t.index ["name", "company_id"], name: "index_gsuite_apps_on_name_and_company_id", unique: true
  end

  create_table "gsuite_configs", force: :cascade do |t|
    t.boolean "ad_enabled", default: false
    t.text "client_id"
    t.string "code"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "expiration_time"
    t.string "refresh_token"
    t.string "scope"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_configs_on_company_id"
  end

  create_table "gsuite_users", force: :cascade do |t|
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "creation_time", precision: nil
    t.string "email"
    t.string "external_customer_id"
    t.boolean "is_admin"
    t.datetime "last_login_time", precision: nil
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_gsuite_users_on_company_id"
    t.index ["email", "company_id"], name: "index_gsuite_users_on_email_and_company_id", unique: true
  end

  create_table "guests", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_guests_on_company_id"
    t.index ["contributor_id"], name: "index_guests_on_contributor_id"
    t.index ["workspace_id"], name: "index_guests_on_workspace_id"
  end

  create_table "help_center_logos", force: :cascade do |t|
    t.integer "company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_help_center_logos_on_company_id"
  end

  create_table "help_desk_reports", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.string "name"
    t.string "report_content_type"
    t.string "report_file_name"
    t.integer "report_file_size"
    t.integer "report_type"
    t.datetime "report_updated_at", precision: nil
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_help_desk_reports_on_company_id"
    t.index ["creator_id"], name: "index_help_desk_reports_on_creator_id"
  end

  create_table "help_ticket_activities", id: :serial, force: :cascade do |t|
    t.integer "activity_action"
    t.integer "activity_type"
    t.string "activity_type_temporary"
    t.datetime "created_at", precision: nil, null: false
    t.hstore "data", default: {}
    t.integer "help_ticket_id"
    t.string "message_id"
    t.integer "owner_id"
    t.boolean "private_user_flag"
    t.text "private_user_ids", default: [], array: true
    t.hstore "temp_data", default: {}
    t.datetime "updated_at", precision: nil, null: false
    t.index ["help_ticket_id"], name: "index_help_ticket_activities_on_help_ticket_id"
    t.index ["owner_id"], name: "index_help_ticket_activities_on_owner_id"
  end

  create_table "help_ticket_comments", force: :cascade do |t|
    t.text "comment_body"
    t.tsvector "comment_body_tokens"
    t.string "comment_text"
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_attachment_id"
    t.string "email"
    t.bigint "help_ticket_activity_id"
    t.bigint "help_ticket_id"
    t.bigint "merged_ticket_id"
    t.string "message_id"
    t.boolean "mute_notification", default: false
    t.bigint "parent_comment_id"
    t.text "private_company_user_ids", default: [], array: true
    t.text "private_contributor_ids", default: [], array: true
    t.boolean "private_flag", default: false
    t.boolean "private_user_flag", default: false
    t.boolean "resolution_flag", default: false
    t.integer "source", default: 0
    t.integer "status", default: 0, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index "to_tsvector('english'::regconfig, comment_body)", name: "idx_help_ticket_comments_comment_body", using: :gin
    t.index ["comment_body_tokens"], name: "idx_comment_body_tokens", using: :gin
    t.index ["contributor_id"], name: "index_help_ticket_comments_on_contributor_id"
    t.index ["custom_form_attachment_id"], name: "index_help_ticket_comments_on_custom_form_attachment_id"
    t.index ["help_ticket_activity_id"], name: "index_help_ticket_comments_on_help_ticket_activity_id"
    t.index ["help_ticket_id"], name: "index_help_ticket_comments_on_help_ticket_id"
    t.index ["merged_ticket_id"], name: "index_help_ticket_comments_on_merged_ticket_id"
    t.index ["parent_comment_id"], name: "index_help_ticket_comments_on_parent_comment_id"
  end

  create_table "help_ticket_drafts", force: :cascade do |t|
    t.jsonb "comments", default: {}
    t.bigint "company_id", null: false
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.jsonb "fields_data", default: {}
    t.bigint "help_ticket_id", null: false
    t.jsonb "tasks_data", default: {}
    t.jsonb "time_spents", default: {}
    t.datetime "updated_at", null: false
    t.bigint "workspace_id", null: false
    t.index ["company_id", "company_user_id"], name: "index_help_ticket_drafts_on_company_and_user"
    t.index ["help_ticket_id", "company_id", "company_user_id"], name: "index_help_ticket_drafts_on_help_ticket_and_user", unique: true
    t.index ["help_ticket_id"], name: "index_help_ticket_drafts_on_help_ticket_id"
    t.index ["workspace_id"], name: "index_help_ticket_drafts_on_workspace_id"
  end

  create_table "help_ticket_task_checklists", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "help_ticket_id"
    t.bigint "task_checklist_id"
    t.datetime "updated_at", null: false
    t.index ["help_ticket_id"], name: "index_help_ticket_task_checklists_on_help_ticket_id"
    t.index ["task_checklist_id"], name: "index_help_ticket_task_checklists_on_task_checklist_id"
  end

  create_table "help_tickets", id: :serial, force: :cascade do |t|
    t.integer "active_ticket_id"
    t.boolean "archived", default: false
    t.integer "assigned_user_ids", default: [], array: true
    t.boolean "closed", default: false
    t.datetime "closed_at"
    t.integer "comment_id"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "creator_id"
    t.integer "creator_ids", default: [], array: true
    t.bigint "custom_form_id"
    t.integer "days_opened"
    t.text "description"
    t.tsvector "description_body_tokens"
    t.string "email"
    t.datetime "first_response_at"
    t.text "group_member_ids"
    t.string "guid"
    t.text "help_paragraph"
    t.boolean "is_first_response_from_agent", default: false
    t.boolean "is_new", default: true
    t.boolean "is_seen", default: false
    t.string "message_id"
    t.string "ms_teams_message_ids", default: [], array: true
    t.boolean "mute_notification", default: false
    t.datetime "opened_at"
    t.text "priority"
    t.integer "related_asset_ids", default: [], array: true
    t.integer "related_contract_ids", default: [], array: true
    t.integer "related_group_ids", default: [], array: true
    t.integer "related_location_ids", default: [], array: true
    t.integer "related_people_ids", default: [], array: true
    t.integer "related_telecom_ids", default: [], array: true
    t.integer "related_vendor_ids", default: [], array: true
    t.integer "secondary_source"
    t.hstore "sla_response"
    t.string "slack_message_ids", default: [], array: true
    t.integer "source", default: 0
    t.string "status"
    t.text "subject"
    t.string "ticket_number"
    t.integer "unread_comments_count", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id", null: false
    t.index ["company_id"], name: "index_help_tickets_on_company_id"
    t.index ["creator_id"], name: "index_help_tickets_on_creator_id"
    t.index ["custom_form_id"], name: "index_help_tickets_on_custom_form_id"
    t.index ["description_body_tokens"], name: "index_help_tickets_on_description_body_tokens", using: :gin
    t.index ["guid"], name: "index_help_tickets_on_guid", unique: true
    t.index ["priority"], name: "index_help_tickets_on_priority"
    t.index ["related_asset_ids"], name: "index_help_tickets_on_related_asset_ids", using: :gin
    t.index ["related_contract_ids"], name: "index_help_tickets_on_related_contract_ids", using: :gin
    t.index ["related_group_ids"], name: "index_help_tickets_on_related_group_ids", using: :gin
    t.index ["related_location_ids"], name: "index_help_tickets_on_related_location_ids", using: :gin
    t.index ["related_people_ids"], name: "index_help_tickets_on_related_people_ids", using: :gin
    t.index ["related_telecom_ids"], name: "index_help_tickets_on_related_telecom_ids", using: :gin
    t.index ["related_vendor_ids"], name: "index_help_tickets_on_related_vendor_ids", using: :gin
    t.index ["status"], name: "index_help_tickets_on_status"
    t.index ["ticket_number", "company_id", "workspace_id"], name: "index_help_tickets_on_ticket_number_and_company_and_workspace", unique: true
    t.index ["workspace_id", "archived"], name: "index_help_tickets_on_workspace_id_and_archived"
    t.index ["workspace_id", "closed"], name: "index_help_tickets_on_workspace_id_and_closed"
    t.index ["workspace_id"], name: "index_help_tickets_on_workspace_id"
  end

  create_table "helpdesk_custom_emails", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "verified"
    t.index ["company_id"], name: "index_helpdesk_custom_emails_on_company_id"
  end

  create_table "helpdesk_custom_forms", force: :cascade do |t|
    t.boolean "collect_closing_survey"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_id", null: false
    t.boolean "default"
    t.string "email"
    t.bigint "helpdesk_custom_email_id"
    t.boolean "require_time_spent_to_close", default: false
    t.boolean "show_checklist", default: true
    t.boolean "show_in_open_portal"
    t.boolean "show_people_list", default: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["custom_form_id"], name: "index_helpdesk_custom_forms_on_custom_form_id", unique: true
    t.index ["helpdesk_custom_email_id"], name: "index_helpdesk_custom_forms_on_helpdesk_custom_email_id"
  end

  create_table "helpdesk_email_formats", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.boolean "enabled", default: false
    t.jsonb "footer_customization", default: {}
    t.text "footer_text", default: ""
    t.jsonb "header_customization", default: {}
    t.text "header_text", default: ""
    t.boolean "hide_footer", default: false
    t.datetime "updated_at", null: false
    t.bigint "workspace_id", null: false
    t.index ["company_id"], name: "index_helpdesk_email_formats_on_company_id"
    t.index ["workspace_id"], name: "index_helpdesk_email_formats_on_workspace_id"
  end

  create_table "helpdesk_faqs", force: :cascade do |t|
    t.text "answer_body"
    t.bigint "category_id"
    t.bigint "company_id"
    t.bigint "msp_templates_helpdesk_faq_id"
    t.integer "order"
    t.boolean "public", default: false
    t.text "question_body"
    t.bigint "workspace_id"
    t.index ["category_id"], name: "index_helpdesk_faqs_on_category_id"
    t.index ["company_id"], name: "index_helpdesk_faqs_on_company_id"
    t.index ["msp_templates_helpdesk_faq_id"], name: "index_helpdesk_faqs_on_msp_templates_helpdesk_faq_id"
    t.index ["workspace_id"], name: "index_helpdesk_faqs_on_workspace_id"
  end

  create_table "helpdesk_reports", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_helpdesk_reports_on_company_id"
    t.index ["workspace_id"], name: "index_helpdesk_reports_on_workspace_id"
  end

  create_table "helpdesk_settings", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil
    t.string "custom_name"
    t.bigint "default_helpdesk_setting_id"
    t.boolean "enabled", default: false
    t.jsonb "options", default: {}
    t.string "selected_option"
    t.datetime "updated_at", precision: nil
    t.bigint "workspace_id"
    t.index ["company_id", "workspace_id", "default_helpdesk_setting_id"], name: "idx_uniq_helpdesk_settings", unique: true
    t.index ["company_id"], name: "index_helpdesk_settings_on_company_id"
    t.index ["default_helpdesk_setting_id"], name: "index_helpdesk_settings_on_default_helpdesk_setting_id"
    t.index ["workspace_id"], name: "index_helpdesk_settings_on_workspace_id"
  end

  create_table "integrated_vendors", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.bigint "integrable_id"
    t.string "integrable_type"
    t.boolean "is_new", default: false
    t.boolean "sync_status", default: false
    t.datetime "updated_at", null: false
    t.string "vendor_id"
    t.string "vendor_name"
    t.index ["company_id"], name: "index_integrated_vendors_on_company_id"
    t.index ["integrable_type", "integrable_id"], name: "index_integrated_vendors_on_integrable"
  end

  create_table "integration_configurations", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "key"
    t.datetime "updated_at", precision: nil, null: false
    t.string "value"
  end

  create_table "integrations", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.string "icon"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "integrations_app_sources", force: :cascade do |t|
    t.integer "app_id"
    t.bigint "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "integration_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["integration_id"], name: "index_integrations_app_sources_on_integration_id"
  end

  create_table "integrations_app_usages", force: :cascade do |t|
    t.integer "app_id"
    t.integer "company_integration_id"
    t.datetime "created_at", precision: nil, null: false
    t.date "month"
    t.integer "num_of_users"
    t.integer "source_id"
    t.integer "total_users"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["app_id", "month"], name: "index_integrations_app_usage_on_app_id_and_month"
  end

  create_table "integrations_app_users", force: :cascade do |t|
    t.integer "app_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.index ["app_id", "user_id"], name: "index_integrations_app_users_on_app_id_and_user_id"
  end

  create_table "integrations_apps", force: :cascade do |t|
    t.integer "access_type", default: 0
    t.integer "app_type"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.datetime "fetched_logo_at", precision: nil
    t.string "friendly_name"
    t.string "logo_url"
    t.jsonb "meta_data"
    t.boolean "mfa_enabled"
    t.string "name"
    t.integer "num_of_users"
    t.bigint "product_id"
    t.integer "risk"
    t.integer "security_state"
    t.string "source", default: "integration"
    t.integer "status"
    t.integer "suspended_licenses", default: 0
    t.integer "total_users", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.integer "used", default: 0
    t.bigint "vendor_id"
    t.index ["company_id"], name: "index_integrations_apps_on_company_id"
    t.index ["name", "company_id", "app_type"], name: "index_integrations_apps_on_name_company_id_and_app_type", unique: true
    t.index ["vendor_id"], name: "index_integrations_apps_on_vendor_id"
  end

  create_table "integrations_locations", force: :cascade do |t|
    t.string "address"
    t.string "city"
    t.bigint "company_id"
    t.string "country"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.bigint "location_id"
    t.string "postal_code"
    t.string "source"
    t.string "state"
    t.string "street_address"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_integrations_locations_on_company_id"
    t.index ["location_id"], name: "index_integrations_locations_on_location_id"
  end

  create_table "integrations_services", force: :cascade do |t|
    t.bigint "company_id"
    t.string "friendly_name"
    t.bigint "integrations_apps_id"
    t.string "name"
    t.index ["company_id"], name: "index_integrations_services_on_company_id"
    t.index ["integrations_apps_id"], name: "index_integrations_services_on_integrations_apps_id"
  end

  create_table "integrations_user_sources", force: :cascade do |t|
    t.boolean "active"
    t.bigint "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "integration_id"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.index ["integration_id"], name: "index_integrations_user_sources_on_integration_id"
    t.index ["user_id", "integration_id", "config_id"], name: "index_integrations_user_sources_on_all"
  end

  create_table "integrations_users", force: :cascade do |t|
    t.boolean "active"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.datetime "first_login_at", precision: nil
    t.datetime "last_login_at", precision: nil
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "user_created_at", precision: nil
    t.index ["company_id"], name: "index_integrations_users_on_company_id"
    t.index ["company_user_id"], name: "index_integrations_users_on_company_user_id"
    t.index ["email", "company_id"], name: "index_integrations_users_on_email_and_company_id", unique: true
  end

  create_table "invoices", id: :serial, force: :cascade do |t|
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_invoices_on_company_id"
  end

  create_table "ip_addresses", id: :serial, force: :cascade do |t|
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "friendly_name"
    t.string "ip"
    t.integer "location_id"
    t.text "notes"
    t.integer "telecom_provider_id"
    t.integer "telecom_service_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.index ["ip", "company_id"], name: "ip_addresses_ip_company_id_key", unique: true
    t.index ["location_id"], name: "index_ip_addresses_on_location_id"
    t.index ["telecom_service_id"], name: "index_ip_addresses_on_telecom_service_id"
    t.index ["vendor_id"], name: "index_ip_addresses_on_vendor_id"
  end

  create_table "it_report_preferences", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.jsonb "module_contents"
    t.jsonb "module_data"
    t.jsonb "module_headers"
    t.string "report_heading"
    t.boolean "report_heading_include"
    t.string "report_month"
    t.string "report_year"
    t.jsonb "security_contents"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_it_report_preferences_on_company_id"
  end

  create_table "it_reports", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.bigint "creator_id"
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_it_reports_on_company_id"
    t.index ["creator_id"], name: "index_it_reports_on_creator_id"
  end

  create_table "jamf_pro_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.datetime "expires_in"
    t.integer "import_type", default: 1, null: false
    t.string "instance_name"
    t.string "password"
    t.string "token"
    t.datetime "updated_at", null: false
    t.string "username"
    t.index ["company_id"], name: "index_jamf_pro_configs_on_company_id"
    t.index ["company_user_id"], name: "index_jamf_pro_configs_on_company_user_id"
  end

  create_table "kandji_configs", force: :cascade do |t|
    t.string "api_token"
    t.string "api_url"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.integer "import_type", default: 1, null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_kandji_configs_on_company_id"
    t.index ["company_user_id"], name: "index_kandji_configs_on_company_user_id"
  end

  create_table "kaseya_configs", force: :cascade do |t|
    t.string "access_token"
    t.string "client_id"
    t.string "client_secret"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.integer "import_type", default: 1, null: false
    t.string "integrator_username"
    t.string "refresh_token"
    t.datetime "token_expires_at"
    t.datetime "updated_at", null: false
    t.string "vsa_url"
    t.index ["company_id"], name: "index_kaseya_configs_on_company_id"
    t.index ["company_user_id"], name: "index_kaseya_configs_on_company_user_id"
  end

  create_table "library_documents", force: :cascade do |t|
    t.string "attached_file_content_type"
    t.string "attached_file_file_name"
    t.integer "attached_file_file_size"
    t.datetime "attached_file_updated_at", precision: nil
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.bigint "msp_templates_document_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id", "name", "workspace_id"], name: "idx_name_workspace_on_library_documents", unique: true
    t.index ["company_id"], name: "index_library_documents_on_company_id"
    t.index ["creator_id"], name: "index_library_documents_on_creator_id"
    t.index ["msp_templates_document_id"], name: "index_library_documents_on_msp_templates_document_id"
    t.index ["workspace_id"], name: "index_library_documents_on_workspace_id"
  end

  create_table "linkable_links", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "source_id", null: false
    t.integer "target_id", null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["source_id", "target_id"], name: "index_linkable_links_on_source_id_and_target_id", unique: true
  end

  create_table "linkables", force: :cascade do |t|
    t.string "category"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "detail"
    t.string "image_url"
    t.bigint "linkable_id"
    t.string "linkable_type"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id", "name", "linkable_type"], name: "linkables_combined_lookup"
    t.index ["company_id"], name: "index_linkables_on_company_id"
    t.index ["linkable_id", "linkable_type"], name: "index_linkables_on_linkable_id_and_linkable_type", unique: true
  end

  create_table "load_balancers", force: :cascade do |t|
    t.string "arn"
    t.integer "certificate_count"
    t.datetime "created_at", null: false
    t.string "dns_name"
    t.string "listener_arn"
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "locations", id: :serial, force: :cascade do |t|
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_id"
    t.string "default_avatar_url"
    t.string "guid"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_locations_on_company_id"
    t.index ["custom_form_id"], name: "index_locations_on_custom_form_id"
    t.index ["guid"], name: "index_locations_on_guid", unique: true
  end

  create_table "mac_mini_helpdesk_versions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "installer_link"
    t.string "name"
    t.datetime "updated_at", null: false
    t.string "version", default: ""
  end

  create_table "managed_asset_activities", force: :cascade do |t|
    t.integer "activity_action"
    t.integer "activity_type"
    t.datetime "created_at", null: false
    t.jsonb "data", default: {}
    t.bigint "managed_asset_id"
    t.datetime "updated_at", null: false
    t.index ["managed_asset_id"], name: "index_managed_asset_activities_on_managed_asset_id"
  end

  create_table "managed_asset_attachments", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "library_document_id"
    t.integer "managed_asset_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "managed_asset_tags", force: :cascade do |t|
    t.bigint "company_asset_tag_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "managed_asset_id"
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_asset_tag_id"], name: "index_managed_asset_tags_on_company_asset_tag_id"
    t.index ["company_id"], name: "index_managed_asset_tags_on_company_id"
    t.index ["managed_asset_id"], name: "index_managed_asset_tags_on_managed_asset_id"
  end

  create_table "managed_assets", id: :serial, force: :cascade do |t|
    t.date "acquisition_date"
    t.bigint "agent_location_id"
    t.boolean "archived", default: false
    t.string "asset_tag", default: ""
    t.bigint "company_asset_status_id"
    t.bigint "company_asset_type_id"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.datetime "deleted_at", precision: nil
    t.string "department"
    t.string "description", default: ""
    t.jsonb "details", default: ""
    t.string "details_old", default: ""
    t.string "firmware"
    t.string "guid"
    t.bigint "hardware_detail_id"
    t.string "hardware_detail_type"
    t.integer "impact"
    t.date "install_date"
    t.string "ip_address", default: ""
    t.boolean "is_combined_asset", default: false
    t.date "last_update"
    t.integer "location_id"
    t.text "mac_addresses", default: [], array: true
    t.string "machine_serial_number", default: ""
    t.string "manufactured_by"
    t.string "manufacturer", default: ""
    t.boolean "merged", default: false
    t.string "model", default: ""
    t.string "name"
    t.string "operating_system"
    t.string "os_version"
    t.boolean "primary_asset", default: false
    t.hstore "private_asset_attributes"
    t.bigint "product_id"
    t.string "product_number", default: ""
    t.string "sku"
    t.integer "source", default: 0
    t.integer "status", default: 0
    t.string "system_up_time"
    t.string "system_uuid"
    t.datetime "update_by_source_at", precision: nil
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.date "warranty_expiration"
    t.date "warranty_fetch_scheduled_at"
    t.boolean "warranty_info_fetched", default: false
    t.index "to_tsvector('english'::regconfig, (asset_tag)::text)", name: "idx_managed_assets_asset_tag", using: :gin
    t.index ["agent_location_id"], name: "index_managed_assets_on_agent_location_id"
    t.index ["company_asset_status_id"], name: "index_managed_assets_on_company_asset_status_id"
    t.index ["company_asset_type_id"], name: "index_managed_assets_on_company_asset_type_id"
    t.index ["company_id", "is_combined_asset"], name: "index_assets_on_company_id_and_is_combined_asset"
    t.index ["company_id", "merged", "archived", "name"], name: "index_managed_assets_on_company_merged_archived"
    t.index ["company_id"], name: "index_managed_assets_on_company_id", where: "(deleted_at IS NULL)"
    t.index ["creator_id"], name: "index_managed_assets_on_creator_id"
    t.index ["deleted_at"], name: "index_managed_assets_on_deleted_at"
    t.index ["hardware_detail_type", "hardware_detail_id"], name: "hardware_detail_index"
    t.index ["location_id"], name: "index_managed_assets_on_location_id", where: "(deleted_at IS NULL)"
    t.index ["mac_addresses"], name: "index_assets_on_mac_addresses"
    t.index ["machine_serial_number", "company_id", "merged"], name: "index_assets_on_serial_number_and_company_id_and_not_merged", unique: true, where: "(((machine_serial_number)::text <> ''::text) AND (merged <> true))"
    t.index ["machine_serial_number"], name: "index_assets_on_machine_serial_number"
    t.index ["name"], name: "index_assets_on_name"
    t.index ["product_id"], name: "index_managed_assets_on_product_id"
    t.index ["vendor_id"], name: "index_managed_assets_on_vendor_id", where: "(deleted_at IS NULL)"
  end

  create_table "meraki_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "import_type", default: 1, null: false
    t.boolean "is_force_stopped", default: false
    t.string "name"
    t.boolean "save_cameras", default: false
    t.boolean "save_sm_devices", default: false
    t.string "sync_by"
    t.string "sync_networks"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_meraki_configs_on_company_id"
    t.index ["company_user_id"], name: "index_meraki_configs_on_company_user_id"
  end

  create_table "meraki_records", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "existing_locations"
    t.jsonb "integrated_networks"
    t.integer "meraki_config_id"
    t.jsonb "networks"
    t.jsonb "total_location"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_meraki_records_on_company_id"
  end

  create_table "merge_sources", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "mergeable_id"
    t.string "mergeable_type"
    t.bigint "source_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["mergeable_type", "mergeable_id"], name: "index_merge_sources_on_mergeable_type_and_mergeable_id"
    t.index ["source_id"], name: "index_merge_sources_on_source_id"
  end

  create_table "merged_selected_attributes", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.bigint "source_id"
    t.string "source_type"
    t.bigint "target_id"
    t.datetime "updated_at", precision: nil, null: false
    t.string "value"
    t.index ["source_type", "source_id"], name: "merge_selected_attributes_sources_index"
  end

  create_table "mfa_sessions", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.integer "mfa_attempts_remaining"
    t.string "mfa_code"
    t.datetime "mfa_code_generated_at"
    t.boolean "mfa_verified", default: false
    t.datetime "mfa_verified_at"
    t.datetime "updated_at", null: false
    t.bigint "user_device_id"
    t.index ["company_user_id"], name: "index_mfa_sessions_on_company_user_id"
    t.index ["user_device_id"], name: "index_mfa_sessions_on_user_device_id"
  end

  create_table "mfa_settings", force: :cascade do |t|
    t.boolean "app_auth"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "email_auth"
    t.boolean "enabled_for_all_admins", default: false
    t.boolean "enabled_for_all_users"
    t.boolean "enabled_for_groups", default: false
    t.boolean "enabled_for_self_user"
    t.boolean "mfa_enabled"
    t.boolean "msg_auth", default: true
    t.boolean "remember_device"
    t.boolean "restrict_user_access", default: false
    t.integer "timeout_period"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_mfa_settings_on_company_id"
  end

  create_table "mfa_super_admin_sessions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.integer "mfa_attempts_remaining"
    t.string "mfa_code"
    t.datetime "mfa_code_generated_at"
    t.boolean "mfa_verified", default: false
    t.datetime "mfa_verified_at"
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_mfa_super_admin_sessions_on_user_id"
  end

  create_table "microsoft_app_users", force: :cascade do |t|
    t.integer "microsoft_app_id"
    t.integer "microsoft_user_id"
    t.index ["microsoft_user_id", "microsoft_app_id"], name: "index_microsoft_app_users_on_user_id_and_app_id"
  end

  create_table "microsoft_apps", force: :cascade do |t|
    t.boolean "account_enabled"
    t.string "app_display_name"
    t.string "app_id"
    t.integer "app_type"
    t.integer "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "display_name"
    t.string "logo_url"
    t.jsonb "meta_data"
    t.string "microsoft_id"
    t.string "publisher_name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["app_display_name", "company_id"], name: "index_microsoft_apps_on_app_display_name_and_company_id", unique: true
    t.index ["company_id", "app_type"], name: "index_microsoft_apps_on_company_id_app_type"
  end

  create_table "microsoft_configs", force: :cascade do |t|
    t.boolean "ad_enabled", default: false
    t.boolean "allow_guest_users", default: true
    t.string "code"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_microsoft_configs_on_company_id"
  end

  create_table "microsoft_service_informations", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "friendly_name"
    t.string "guid"
    t.string "string_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "microsoft_services", force: :cascade do |t|
    t.bigint "company_id"
    t.string "external_id"
    t.string "friendly_name"
    t.bigint "microsoft_apps_id"
    t.string "name"
    t.index ["company_id"], name: "index_microsoft_services_on_company_id"
    t.index ["microsoft_apps_id"], name: "index_microsoft_services_on_microsoft_apps_id"
  end

  create_table "microsoft_users", force: :cascade do |t|
    t.integer "company_id"
    t.bigint "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "department"
    t.string "display_name"
    t.string "email"
    t.datetime "first_login_at", precision: nil
    t.string "given_name"
    t.string "job_title"
    t.datetime "last_login_at", precision: nil
    t.bigint "location_id"
    t.string "microsoft_id"
    t.string "mobile_phone"
    t.string "office_location"
    t.string "supervisor"
    t.string "surname"
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "user_created_at", precision: nil
    t.string "user_principal_name"
    t.index ["email", "company_id"], name: "index_microsoft_users_on_email_and_company_id", unique: true
  end

  create_table "mobile_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "imei"
    t.bigint "managed_asset_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "module_alert_contributors", force: :cascade do |t|
    t.integer "company_id"
    t.integer "contributor_id"
    t.bigint "module_alert_id"
    t.index ["module_alert_id"], name: "index_module_alert_contributors_on_module_alert_id"
  end

  create_table "module_alert_notifications", force: :cascade do |t|
    t.decimal "actual"
    t.datetime "alert_date", precision: nil
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "dismissed_at", precision: nil
    t.datetime "email_sent_at", precision: nil
    t.decimal "expected"
    t.decimal "limit"
    t.bigint "module_alert_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_module_alert_notifications_on_company_id"
    t.index ["module_alert_id"], name: "index_module_alert_notifications_on_module_alert_id"
  end

  create_table "module_alerts", force: :cascade do |t|
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.boolean "is_default"
    t.integer "limit"
    t.bigint "monitorable_id"
    t.string "monitorable_type"
    t.string "type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["monitorable_type", "monitorable_id"], name: "index_module_alerts_on_monitorable_type_and_monitorable_id"
  end

  create_table "mosyle_configs", force: :cascade do |t|
    t.string "access_token"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.datetime "expires_in"
    t.string "password"
    t.string "refresh_token"
    t.datetime "updated_at", null: false
    t.string "username"
    t.index ["company_id"], name: "index_mosyle_configs_on_company_id"
  end

  create_table "ms_intune_assets_configs", force: :cascade do |t|
    t.string "code"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.integer "import_type", default: 1, null: false
    t.boolean "is_force_stopped", default: false
    t.string "refresh_token"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_ms_intune_assets_configs_on_company_id"
    t.index ["company_user_id"], name: "index_ms_intune_assets_configs_on_company_user_id"
  end

  create_table "ms_teams_apps", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.string "installer_link"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.float "version"
  end

  create_table "ms_teams_configs", force: :cascade do |t|
    t.boolean "active", default: true
    t.string "channel_id"
    t.string "channel_name"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "team_id"
    t.string "team_name"
    t.string "tenant_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_ms_teams_configs_on_company_id"
    t.index ["workspace_id"], name: "index_ms_teams_configs_on_workspace_id"
  end

  create_table "msp_event_logs", force: :cascade do |t|
    t.integer "activity_type"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.jsonb "data", default: {}
    t.bigint "entity_id"
    t.string "entity_type"
    t.integer "module_name"
    t.bigint "owner_id"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_msp_event_logs_on_company_id"
    t.index ["entity_type", "entity_id"], name: "index_msp_event_logs_on_entity"
    t.index ["owner_id"], name: "index_msp_event_logs_on_owner_id"
  end

  create_table "msp_templates_asset_types", force: :cascade do |t|
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "last_updated_by_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_asset_types_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_asset_types_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_asset_types_on_last_updated_by_id"
  end

  create_table "msp_templates_automated_tasks", force: :cascade do |t|
    t.integer "automated_task_id"
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "last_updated_by_id"
    t.string "name"
    t.integer "order"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_automated_tasks_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_automated_tasks_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_automated_tasks_on_last_updated_by_id"
  end

  create_table "msp_templates_blocked_keywords", force: :cascade do |t|
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "keyword"
    t.bigint "last_updated_by_id"
    t.datetime "updated_at", null: false
    t.index ["company_build_id"], name: "index_msp_templates_blocked_keywords_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_blocked_keywords_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_blocked_keywords_on_last_updated_by_id"
  end

  create_table "msp_templates_categories", force: :cascade do |t|
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "last_updated_by_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_categories_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_categories_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_categories_on_last_updated_by_id"
  end

  create_table "msp_templates_custom_form_fields", force: :cascade do |t|
    t.string "audience"
    t.datetime "created_at", precision: nil, null: false
    t.string "default_value"
    t.integer "field_attribute_type"
    t.string "label"
    t.text "list_type"
    t.bigint "msp_templates_custom_form_id"
    t.string "name"
    t.text "note"
    t.text "options"
    t.integer "order_position"
    t.boolean "permit_edit_default"
    t.boolean "permit_view_default"
    t.boolean "private"
    t.boolean "required"
    t.boolean "required_to_close", default: false
    t.text "sort_list"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["msp_templates_custom_form_id", "name"], name: "index_msp_templates_custom_fields_on_name_and_form_id", unique: true
    t.index ["msp_templates_custom_form_id"], name: "idx_msp_custom_form_fields_on_custom_form"
  end

  create_table "msp_templates_custom_forms", force: :cascade do |t|
    t.string "color"
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.integer "company_module"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.string "form_name"
    t.string "icon"
    t.bigint "last_updated_by_id"
    t.boolean "show_in_open_portal"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_custom_forms_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_custom_forms_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_custom_forms_on_last_updated_by_id"
  end

  create_table "msp_templates_documents", force: :cascade do |t|
    t.string "attached_file_content_type"
    t.string "attached_file_file_name"
    t.integer "attached_file_file_size"
    t.datetime "attached_file_updated_at", precision: nil
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "last_updated_by_id"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_documents_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_documents_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_documents_on_last_updated_by_id"
  end

  create_table "msp_templates_event_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "event_subject_type_id"
    t.integer "parent_id"
    t.bigint "task_event_id"
    t.datetime "updated_at", precision: nil, null: false
    t.text "value"
  end

  create_table "msp_templates_field_positions", force: :cascade do |t|
    t.bigint "msp_templates_custom_form_field_id"
    t.integer "position"
    t.index ["msp_templates_custom_form_field_id"], name: "idx_msp_field_positions_on_custom_form_field"
  end

  create_table "msp_templates_group_members", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.bigint "msp_templates_group_id"
    t.datetime "updated_at", null: false
    t.index ["company_user_id"], name: "index_msp_templates_group_members_on_company_user_id"
    t.index ["msp_templates_group_id"], name: "index_msp_templates_group_members_on_msp_templates_group_id"
  end

  create_table "msp_templates_group_privileges", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "msp_templates_group_id"
    t.string "name"
    t.string "permission_type"
    t.datetime "updated_at", null: false
    t.index ["msp_templates_group_id"], name: "index_msp_templates_group_privileges_on_msp_templates_group_id"
  end

  create_table "msp_templates_groups", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_msp_templates_groups_on_company_id"
  end

  create_table "msp_templates_helpdesk_custom_forms", force: :cascade do |t|
    t.boolean "collect_closing_survey"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "msp_templates_custom_form_id"
    t.boolean "show_in_open_portal"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["msp_templates_custom_form_id"], name: "idx_msp_custom_form_on_msp_helpdesk_custom_form"
  end

  create_table "msp_templates_helpdesk_faqs", force: :cascade do |t|
    t.text "answer_body"
    t.integer "category_id"
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "helpdesk_faq_id"
    t.bigint "last_updated_by_id"
    t.integer "order"
    t.text "question_body"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_helpdesk_faqs_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_helpdesk_faqs_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_helpdesk_faqs_on_last_updated_by_id"
  end

  create_table "msp_templates_snippets", force: :cascade do |t|
    t.bigint "company_build_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.bigint "last_updated_by_id"
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_build_id"], name: "index_msp_templates_snippets_on_company_build_id"
    t.index ["company_id"], name: "index_msp_templates_snippets_on_company_id"
    t.index ["last_updated_by_id"], name: "index_msp_templates_snippets_on_last_updated_by_id"
  end

  create_table "msp_templates_task_actions", force: :cascade do |t|
    t.bigint "action_type_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "msp_templates_automated_task_id"
    t.datetime "updated_at", precision: nil, null: false
    t.text "value"
  end

  create_table "msp_templates_task_events", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "event_type_id"
    t.bigint "msp_templates_automated_task_id"
    t.datetime "updated_at", precision: nil, null: false
    t.hstore "value"
    t.index ["msp_templates_automated_task_id"], name: "idx_msp_task_events_on_automated_task_id"
  end

  create_table "netsuite_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "tenant"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_netsuite_configs_on_company_id"
  end

  create_table "networking_device_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "dns"
    t.string "gateway"
    t.string "hostname"
    t.bigint "managed_asset_id"
    t.string "ports"
    t.string "public_ip"
    t.string "ssids"
    t.datetime "updated_at", precision: nil, null: false
    t.string "wan_ips"
  end

  create_table "nmap_options", force: :cascade do |t|
    t.integer "company_id"
    t.boolean "enabled"
  end

  create_table "okta_app_users", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "okta_app_id"
    t.integer "okta_user_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["okta_user_id", "okta_app_id"], name: "index_okta_app_users_on_user_id_app_id"
  end

  create_table "okta_apps", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.string "okta_app_id"
    t.integer "okta_config_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_okta_apps_on_company_id"
    t.index ["name", "company_id"], name: "index_okta_apps_on_name_and_company_id", unique: true
  end

  create_table "okta_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_okta_configs_on_company_id"
  end

  create_table "okta_users", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.string "first_name"
    t.datetime "last_login", precision: nil
    t.string "last_name"
    t.integer "okta_config_id"
    t.string "okta_user_id"
    t.integer "status", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_okta_users_on_company_id"
    t.index ["email", "company_id"], name: "index_okta_users_on_email_and_company_id", unique: true
  end

  create_table "one_login_app_users", force: :cascade do |t|
    t.integer "one_login_app_id"
    t.integer "one_login_user_id"
    t.index ["one_login_user_id", "one_login_app_id"], name: "index_one_login_app_users_on_user_id_app_id"
  end

  create_table "one_login_apps", force: :cascade do |t|
    t.string "app_name"
    t.integer "company_id"
    t.integer "config_id"
    t.integer "connector_id"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "extension"
    t.integer "external_app_id"
    t.string "icon_url"
    t.boolean "provisioning"
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "visible"
    t.index ["app_name", "company_id"], name: "index_one_login_apps_on_app_name_and_company_id", unique: true
  end

  create_table "one_login_configs", force: :cascade do |t|
    t.string "client_id"
    t.string "client_secret"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "region"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "one_login_users", force: :cascade do |t|
    t.integer "company_id"
    t.bigint "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.integer "external_user_id"
    t.string "firstname"
    t.datetime "last_login_at", precision: nil
    t.string "lastname"
    t.string "open_id_name"
    t.integer "state"
    t.integer "status"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_one_login_users_on_company_id"
    t.index ["email", "company_id"], name: "index_one_login_users_on_email_and_company_id", unique: true
  end

  create_table "other_details", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "managed_asset_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "phone_details", force: :cascade do |t|
    t.string "connection_interface"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "managed_asset_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "phone_numbers", id: :serial, force: :cascade do |t|
    t.integer "company_id"
    t.string "country_code", default: "US"
    t.string "country_code_number", default: "1"
    t.datetime "created_at", precision: nil, null: false
    t.string "friendly_name"
    t.bigint "location_id"
    t.text "notes"
    t.string "number"
    t.integer "telecom_provider_id"
    t.integer "telecom_service_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.index ["location_id"], name: "index_phone_numbers_on_location_id"
    t.index ["number", "company_id"], name: "phone_numbers_number_company_id_key", unique: true
    t.index ["telecom_service_id"], name: "index_phone_numbers_on_telecom_service_id"
    t.index ["vendor_id"], name: "index_phone_numbers_on_vendor_id"
  end

  create_table "plaid_accounts", force: :cascade do |t|
    t.string "account_id"
    t.integer "account_type"
    t.datetime "activated", precision: nil
    t.string "bank_name"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "error_sent_at", precision: nil
    t.string "guid"
    t.integer "health", default: 0
    t.string "institution_id"
    t.datetime "last_synced_at", precision: nil
    t.string "mask"
    t.string "name"
    t.string "official_name"
    t.bigint "plaid_item_id"
    t.integer "status", default: 0
    t.string "subtype"
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "plaid_categories", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "default_category_id"
    t.string "plaid_category_code"
    t.string "tag"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "plaid_credit_card_transactions", force: :cascade do |t|
    t.float "amount"
    t.string "category_id"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "general_service_id"
    t.boolean "is_active", default: true
    t.bigint "plaid_credit_card_id"
    t.bigint "plaid_product_id"
    t.string "primary_category"
    t.string "product_name"
    t.string "secondary_category"
    t.string "tertiary_category"
    t.date "transaction_date"
    t.string "transaction_id"
    t.string "transaction_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_plaid_credit_card_transactions_on_company_id"
    t.index ["company_user_id"], name: "index_plaid_credit_card_transactions_on_company_user_id"
    t.index ["general_service_id"], name: "index_plaid_credit_card_transactions_on_general_service_id"
    t.index ["plaid_credit_card_id"], name: "index_plaid_credit_card_transactions_on_plaid_credit_card_id"
    t.index ["plaid_product_id"], name: "index_plaid_credit_card_transactions_on_plaid_product_id"
  end

  create_table "plaid_credit_cards", force: :cascade do |t|
    t.string "account_id"
    t.datetime "activated", precision: nil
    t.string "bank_name"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.string "mask"
    t.string "name"
    t.string "official_name"
    t.bigint "plaid_item_id"
    t.integer "status", default: 0
    t.string "subtype"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_plaid_credit_cards_on_company_id", where: "(deleted_at IS NULL)"
    t.index ["company_user_id"], name: "index_plaid_credit_cards_on_company_user_id", where: "(deleted_at IS NULL)"
    t.index ["plaid_item_id"], name: "index_plaid_credit_cards_on_plaid_item_id", where: "(deleted_at IS NULL)"
  end

  create_table "plaid_items", force: :cascade do |t|
    t.string "access_token"
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.string "item_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_plaid_items_on_company_user_id", where: "(deleted_at IS NULL)"
  end

  create_table "predicted_cloud_transactions", force: :cascade do |t|
    t.float "amount"
    t.bigint "company_id"
    t.bigint "company_integration_id"
    t.datetime "created_at", precision: nil, null: false
    t.date "date"
    t.string "name"
    t.bigint "product_id"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.index ["company_id"], name: "index_predicted_cloud_transactions_on_company_id"
    t.index ["company_integration_id"], name: "index_predicted_cloud_transactions_on_company_integration_id"
    t.index ["product_id"], name: "index_predicted_cloud_transactions_on_product_id"
    t.index ["vendor_id"], name: "index_predicted_cloud_transactions_on_vendor_id"
  end

  create_table "printer_details", force: :cascade do |t|
    t.string "available_memory"
    t.string "black_toner_left_percent"
    t.string "black_toner_name"
    t.datetime "created_at", precision: nil, null: false
    t.string "cyan_toner_left_percent"
    t.string "cyan_toner_name"
    t.string "magenta_toner_left_percent"
    t.string "magenta_toner_name"
    t.bigint "managed_asset_id"
    t.string "memory"
    t.string "personalities_installed"
    t.string "product_number"
    t.string "service_id"
    t.string "ssids"
    t.string "total_page_count"
    t.string "total_pcl_five_page_count"
    t.string "total_pcl_six_page_count"
    t.string "total_post_script_page_count"
    t.datetime "updated_at", precision: nil, null: false
    t.string "used_memory"
    t.string "yellow_toner_left_percent"
    t.string "yellow_toner_name"
  end

  create_table "printer_types", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "privileges", id: :serial, force: :cascade do |t|
    t.integer "company_user_id"
    t.bigint "contributor_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.string "permission_type"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_user_id", "name", "permission_type"], name: "index_privileges_combined"
    t.index ["contributor_id"], name: "index_privileges_on_contributor_id"
    t.index ["workspace_id"], name: "index_privileges_on_workspace_id"
  end

  create_table "probe_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "ip_range"
    t.datetime "last_synced_at", precision: nil
    t.bigint "probe_location_id"
    t.string "probe_name"
    t.jsonb "schedule_setting"
    t.jsonb "snmp_setting"
    t.jsonb "snmpv_three_setting"
    t.jsonb "ssh_setting"
    t.datetime "updated_at", precision: nil, null: false
    t.jsonb "wmi_setting"
    t.index ["company_id"], name: "index_probe_configs_on_company_id"
    t.index ["probe_location_id"], name: "index_probe_configs_on_probe_location_id"
  end

  create_table "probe_locations", force: :cascade do |t|
    t.string "app_version"
    t.bigint "company_id"
    t.string "computer_name"
    t.datetime "created_at", precision: nil, null: false
    t.string "ip_address"
    t.boolean "is_enabled", default: true
    t.datetime "last_scanned_at", precision: nil
    t.bigint "location_id"
    t.text "mac_addresses", default: [], array: true
    t.string "machine_serial_number"
    t.bigint "managed_by_contributor_id"
    t.string "manufacturer", default: ""
    t.text "secondary_mac_addresses", default: [], array: true
    t.string "system_uuid"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_probe_locations_on_company_id"
    t.index ["location_id"], name: "index_probe_locations_on_location_id"
    t.index ["managed_by_contributor_id"], name: "index_probe_locations_on_managed_by_contributor_id"
  end

  create_table "product_informations", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "guid"
    t.string "logo_url"
    t.string "product_name"
    t.string "source"
    t.string "string_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "product_vendors", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "product_id"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "vendor_id"
    t.index ["product_id"], name: "index_product_vendors_on_product_id"
    t.index ["vendor_id"], name: "index_product_vendors_on_vendor_id"
  end

  create_table "products", id: :serial, force: :cascade do |t|
    t.integer "billing_cycle", default: 0
    t.bigint "category_id"
    t.bigint "company_id"
    t.integer "consumed_licenses"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "default_product_id"
    t.text "default_tags", default: [], array: true
    t.datetime "expected_arrival_date", precision: nil
    t.string "logo_url"
    t.string "name"
    t.string "product_type"
    t.string "tag"
    t.bigint "telecom_service_id"
    t.integer "total_licenses"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.bigint "vendor_id"
    t.index ["category_id"], name: "index_products_on_category_id"
    t.index ["company_id"], name: "index_products_on_company_id"
    t.index ["default_product_id"], name: "index_products_on_default_product_id"
    t.index ["telecom_service_id"], name: "index_products_on_telecom_service_id"
    t.index ["vendor_id"], name: "index_products_on_vendor_id"
  end

  create_table "project_tasks", force: :cascade do |t|
    t.bigint "assignee_id"
    t.text "checklist_ids", default: [], array: true
    t.bigint "company_id"
    t.datetime "completed_at", precision: nil
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.string "description"
    t.datetime "due_at", precision: nil
    t.float "estimated_duration"
    t.bigint "help_ticket_id"
    t.integer "parent_id"
    t.integer "parent_project_task_id"
    t.integer "position"
    t.bigint "predefined_task_id"
    t.integer "priority", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.index ["assignee_id"], name: "index_project_tasks_on_assignee_id"
    t.index ["company_id"], name: "index_project_tasks_on_company_id"
    t.index ["contributor_id"], name: "index_project_tasks_on_contributor_id"
    t.index ["creator_id"], name: "index_project_tasks_on_creator_id"
    t.index ["help_ticket_id"], name: "index_project_tasks_on_help_ticket_id"
  end

  create_table "prompts", force: :cascade do |t|
    t.text "additional_instructions", default: ""
    t.integer "admin_default_prompt", default: [], array: true
    t.datetime "created_at", null: false
    t.text "example_output", default: ""
    t.text "fields_description", default: ""
    t.boolean "is_active", default: false
    t.string "model"
    t.string "name"
    t.bigint "prompts_template_id"
    t.text "response_format", default: ""
    t.float "temperature", default: 0.5
    t.datetime "updated_at", null: false
    t.index ["prompts_template_id"], name: "index_prompts_on_prompts_template_id"
  end

  create_table "prompts_templates", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.boolean "is_active", default: false
    t.string "name"
    t.text "template_text", default: ""
    t.datetime "updated_at", null: false
  end

  create_table "quick_view_filters", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.json "filters_data"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_quick_view_filters_on_company_id"
    t.index ["company_user_id"], name: "index_quick_view_filters_on_company_user_id"
    t.index ["workspace_id"], name: "index_quick_view_filters_on_workspace_id"
  end

  create_table "quickbooks_configs", force: :cascade do |t|
    t.string "access_token"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "realm_id"
    t.datetime "reconnect_token_at", precision: nil
    t.string "refresh_token"
    t.boolean "select_all", default: false
    t.date "start_date"
    t.datetime "token_expires_at", precision: nil
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_quickbooks_configs_on_company_id"
  end

  create_table "referral_payments", force: :cascade do |t|
    t.float "amount"
    t.date "date_sent"
    t.text "notes"
    t.bigint "sent_to_id"
    t.index ["sent_to_id"], name: "index_referral_payments_on_sent_to_id"
  end

  create_table "referrals", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "referral_payment_id"
    t.string "referred_email"
    t.bigint "referred_id"
    t.string "referrer_email"
    t.bigint "referrer_id"
    t.boolean "reminder_email_sent", default: false
    t.integer "status", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.index ["referral_payment_id"], name: "index_referrals_on_referral_payment_id"
    t.index ["referred_id"], name: "index_referrals_on_referred_id"
    t.index ["referrer_id"], name: "index_referrals_on_referrer_id"
  end

  create_table "registration_emails", force: :cascade do |t|
    t.boolean "adblocker", default: false
    t.string "attempted_emails", default: [], array: true
    t.string "company_name"
    t.string "company_url"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.string "email"
    t.string "first_name"
    t.string "full_name"
    t.string "ip_address"
    t.boolean "is_partner_stack", default: false
    t.string "last_name"
    t.string "lead_id"
    t.string "subdomain"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["deleted_at"], name: "index_registration_emails_on_deleted_at"
    t.index ["email"], name: "index_registration_emails_on_email"
  end

  create_table "report_templates", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "creator_id"
    t.string "description"
    t.jsonb "filters"
    t.string "icon_class"
    t.bigint "last_edited_by_id"
    t.string "name"
    t.datetime "updated_at", null: false
  end

  create_table "reports", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.bigint "creator_id"
    t.string "description"
    t.jsonb "filters"
    t.string "icon_class"
    t.boolean "is_default"
    t.boolean "is_scheduled_report", default: false
    t.bigint "last_edited_by_id"
    t.string "name"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_reports_on_company_id"
    t.index ["creator_id"], name: "index_reports_on_creator_id"
    t.index ["last_edited_by_id"], name: "index_reports_on_last_edited_by_id"
    t.index ["workspace_id"], name: "index_reports_on_workspace_id"
  end

  create_table "risk_center_widget_options", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.string "name"
    t.datetime "updated_at", null: false
    t.string "widget_type"
    t.index ["company_id"], name: "index_risk_center_widget_options_on_company_id"
  end

  create_table "sage_accounting_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.date "expires_in"
    t.string "refresh_token"
    t.date "refresh_token_expires_in"
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_sage_accounting_configs_on_company_id"
  end

  create_table "sage_intacct_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "tenant"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_sage_intacct_configs_on_company_id"
  end

  create_table "salesforce_app_users", force: :cascade do |t|
    t.integer "salesforce_app_id"
    t.integer "salesforce_user_id"
    t.index ["salesforce_user_id", "salesforce_app_id"], name: "index_salesforce_app_users_on_user_id_app_id"
  end

  create_table "salesforce_apps", force: :cascade do |t|
    t.string "app_name"
    t.integer "app_type"
    t.bigint "company_id"
    t.integer "config_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.string "developer_name"
    t.string "icon_url"
    t.string "logo_url"
    t.jsonb "meta_data"
    t.string "sales_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["app_name", "company_id"], name: "index_salesforce_apps_on_app_name_and_company_id", unique: true
    t.index ["company_id", "sales_id"], name: "index_salesforce_apps_on_company_id_sales_id"
  end

  create_table "salesforce_configs", force: :cascade do |t|
    t.string "code"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "id_token"
    t.string "instance_url"
    t.string "issued_at"
    t.string "refresh_token"
    t.string "salesforce_id"
    t.string "scope"
    t.string "signature"
    t.string "token"
    t.string "token_type"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_salesforce_configs_on_company_id"
  end

  create_table "salesforce_users", force: :cascade do |t|
    t.string "account_id"
    t.string "call_center_id"
    t.bigint "company_id"
    t.string "company_name"
    t.bigint "config_id"
    t.string "contact_id"
    t.string "country"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "created_date", precision: nil
    t.string "email"
    t.string "first_name"
    t.boolean "is_active"
    t.datetime "last_login_date", precision: nil
    t.string "last_name"
    t.string "name"
    t.string "sales_id"
    t.datetime "updated_at", precision: nil, null: false
    t.string "user_name"
    t.string "user_type"
    t.index ["company_id"], name: "index_salesforce_users_on_company_id"
    t.index ["email", "company_id"], name: "index_salesforce_users_on_email_and_company_id", unique: true
  end

  create_table "scheduled_automated_tasks", force: :cascade do |t|
    t.bigint "automated_tasks_automated_task_id"
    t.datetime "created_at", null: false
    t.bigint "custom_form_value_id"
    t.bigint "help_ticket_id"
    t.integer "status"
    t.datetime "trigger_at"
    t.datetime "updated_at", null: false
    t.index ["automated_tasks_automated_task_id"], name: "index_scheduled_on_automated_task_id"
    t.index ["custom_form_value_id"], name: "index_scheduled_automated_tasks_on_custom_form_value_id"
    t.index ["help_ticket_id"], name: "index_scheduled_automated_tasks_on_help_ticket_id"
  end

  create_table "scheduled_reports", force: :cascade do |t|
    t.bigint "analytics_report_template_id"
    t.datetime "created_at", null: false
    t.integer "email_delivery_frequency"
    t.datetime "end_date"
    t.integer "file_type"
    t.boolean "include_table_in_report"
    t.datetime "last_triggered_at"
    t.text "recipient_ids", default: [], array: true
    t.bigint "report_id"
    t.datetime "scheduled_at"
    t.datetime "updated_at", null: false
    t.index ["analytics_report_template_id"], name: "index_scheduled_reports_on_analytics_report_template_id"
    t.index ["report_id"], name: "index_scheduled_reports_on_report_id"
  end

  create_table "scheduled_slas", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "help_ticket_id"
    t.datetime "scheduled_time"
    t.bigint "sla_detail_id"
    t.bigint "sla_email_id"
    t.datetime "updated_at", null: false
    t.index ["help_ticket_id"], name: "index_scheduled_slas_on_help_ticket_id"
    t.index ["sla_detail_id"], name: "index_scheduled_slas_on_sla_detail_id"
    t.index ["sla_email_id"], name: "index_scheduled_slas_on_sla_email_id"
  end

  create_table "scheduled_task_notifications", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.boolean "enabled", default: false
    t.string "notification_type"
    t.bigint "scheduled_task_id"
    t.datetime "updated_at", null: false
    t.json "value"
    t.index ["scheduled_task_id"], name: "index_scheduled_task_notifications_on_scheduled_task_id"
  end

  create_table "scheduled_task_recurrences", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.json "recurrence_date"
    t.json "recurrence_pattern"
    t.bigint "scheduled_task_id"
    t.datetime "updated_at", null: false
    t.index ["scheduled_task_id"], name: "index_scheduled_task_recurrences_on_scheduled_task_id"
  end

  create_table "scheduled_tasks", force: :cascade do |t|
    t.bigint "assignee_id"
    t.string "comment_subject", default: ""
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "description"
    t.bigint "help_ticket_comment_id"
    t.string "name"
    t.integer "recurrence_count", default: 0
    t.boolean "recurring", default: false
    t.date "scheduled_at"
    t.boolean "send_email_to_creator_comment", default: false
    t.boolean "send_to_creator_comment", default: false
    t.json "task_ended_at"
    t.json "task_started_at"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["assignee_id"], name: "index_scheduled_tasks_on_assignee_id"
    t.index ["company_id"], name: "index_scheduled_tasks_on_company_id"
    t.index ["help_ticket_comment_id"], name: "index_scheduled_tasks_on_help_ticket_comment_id"
    t.index ["workspace_id"], name: "index_scheduled_tasks_on_workspace_id"
  end

  create_table "scrap_hp_assets", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.binary "warranty_file"
  end

  create_table "service_options", force: :cascade do |t|
    t.integer "company_ids", default: [], array: true
    t.datetime "created_at", null: false
    t.string "service_name", null: false
    t.integer "service_type", default: 0
    t.boolean "status", default: false
    t.datetime "updated_at", null: false
  end

  create_table "sla_conditions", force: :cascade do |t|
    t.integer "condition_type"
    t.datetime "created_at", null: false
    t.string "field_type"
    t.bigint "sla_policy_id"
    t.datetime "updated_at", null: false
    t.jsonb "value"
    t.index ["sla_policy_id"], name: "index_sla_conditions_on_sla_policy_id"
  end

  create_table "sla_details", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.boolean "escalation"
    t.string "first_response_time"
    t.integer "operational_hours"
    t.string "priority"
    t.string "resolution_time"
    t.bigint "sla_policy_id"
    t.datetime "updated_at", null: false
    t.index ["sla_policy_id"], name: "index_sla_details_on_sla_policy_id"
  end

  create_table "sla_emails", force: :cascade do |t|
    t.string "approaches_in"
    t.datetime "created_at", null: false
    t.string "email_body"
    t.integer "email_type"
    t.string "name"
    t.bigint "sla_policy_id"
    t.string "subject"
    t.text "target", default: [], array: true
    t.datetime "updated_at", null: false
    t.index ["sla_policy_id"], name: "index_sla_emails_on_sla_policy_id"
  end

  create_table "sla_policies", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.bigint "custom_form_id"
    t.string "description"
    t.string "name"
    t.integer "order", default: 0
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_sla_policies_on_company_id"
    t.index ["custom_form_id"], name: "index_sla_policies_on_custom_form_id"
  end

  create_table "sla_schedules", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "help_ticket_id"
    t.datetime "scheduled_time"
    t.bigint "sla_detail_id"
    t.bigint "sla_email_id"
    t.datetime "updated_at", null: false
    t.index ["help_ticket_id"], name: "index_sla_schedules_on_help_ticket_id"
    t.index ["sla_detail_id"], name: "index_sla_schedules_on_sla_detail_id"
    t.index ["sla_email_id"], name: "index_sla_schedules_on_sla_email_id"
  end

  create_table "slack_configs", force: :cascade do |t|
    t.string "access_token"
    t.boolean "active", default: true
    t.string "authed_user_id"
    t.bigint "authorized_config_id"
    t.integer "bot_ticket_ids", default: [], array: true
    t.string "bot_user_id"
    t.string "channel_id"
    t.string "channel_name"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "dm_visitors", default: [], array: true
    t.jsonb "meta_data", default: {}, null: false
    t.string "team_id"
    t.string "team_name"
    t.datetime "updated_at", precision: nil, null: false
    t.string "webhook_configuration_url"
    t.string "webhook_url"
    t.bigint "workspace_id"
    t.index ["authorized_config_id"], name: "index_slack_configs_on_authorized_config_id"
    t.index ["company_id"], name: "index_slack_configs_on_company_id"
    t.index ["workspace_id"], name: "index_slack_configs_on_workspace_id"
  end

  create_table "smart_alerts", force: :cascade do |t|
    t.boolean "active", default: false
    t.datetime "created_at", null: false
    t.bigint "managed_asset_id"
    t.integer "recipients", default: [], array: true
    t.string "threshold", default: "15%"
    t.datetime "updated_at", null: false
    t.index ["managed_asset_id"], name: "index_smart_alerts_on_managed_asset_id"
  end

  create_table "snippets", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.bigint "msp_templates_snippet_id"
    t.string "title"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_snippets_on_company_id"
    t.index ["msp_templates_snippet_id"], name: "index_snippets_on_msp_templates_snippet_id"
    t.index ["workspace_id"], name: "index_snippets_on_workspace_id"
  end

  create_table "sophos_configs", force: :cascade do |t|
    t.string "access_token"
    t.string "client_id"
    t.string "client_secret"
    t.bigint "company_id"
    t.bigint "company_user_id"
    t.datetime "created_at", null: false
    t.datetime "expires_in"
    t.integer "import_type", default: 1, null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_sophos_configs_on_company_id"
    t.index ["company_user_id"], name: "index_sophos_configs_on_company_user_id"
  end

  create_table "sophos_tenants", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "data_region_url"
    t.bigint "sophos_config_id", null: false
    t.string "tenant_id"
    t.datetime "updated_at", null: false
    t.index ["sophos_config_id"], name: "index_sophos_tenants_on_sophos_config_id"
  end

  create_table "stop_watch_timers", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "end_time"
    t.bigint "help_ticket_id"
    t.boolean "is_start_time", default: false
    t.string "start_time"
    t.string "time_spent"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "stripe_transactions", force: :cascade do |t|
    t.float "amount"
    t.string "balance_transaction_id"
    t.string "charge_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "customer_id"
    t.string "description"
    t.string "failure_message", default: ""
    t.datetime "initiated_at", precision: nil
    t.boolean "refunded", default: false
    t.integer "status"
    t.bigint "subscription_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_stripe_transactions_on_company_id"
    t.index ["subscription_id"], name: "index_stripe_transactions_on_subscription_id"
  end

  create_table "subscription_activities", force: :cascade do |t|
    t.integer "activity_action"
    t.integer "activity_type"
    t.bigint "company_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.hstore "data", default: {}
    t.bigint "owner_id"
    t.bigint "subscription_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_subscription_activities_on_company_id"
    t.index ["owner_id"], name: "index_subscription_activities_on_owner_id"
    t.index ["subscription_id"], name: "index_subscription_activities_on_subscription_id"
  end

  create_table "subscription_plans", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "interval"
    t.string "name"
    t.float "price"
    t.integer "status"
    t.string "stripe_identifier"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "subscriptions", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "email"
    t.date "end_date"
    t.string "module_type"
    t.json "secondary_emails"
    t.date "start_date"
    t.integer "status"
    t.string "stripe_subscription_id"
    t.string "stripe_subscription_schedule_id"
    t.bigint "subscriber_id"
    t.bigint "subscription_plan_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_subscriptions_on_company_id"
    t.index ["subscriber_id"], name: "index_subscriptions_on_subscriber_id"
    t.index ["subscription_plan_id"], name: "index_subscriptions_on_subscription_plan_id"
  end

  create_table "system_details", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "detail_category"
    t.jsonb "detail_data"
    t.bigint "discovered_asset_id"
    t.bigint "managed_asset_id"
    t.datetime "updated_at", null: false
    t.index ["discovered_asset_id"], name: "index_system_details_on_discovered_asset_id"
    t.index ["managed_asset_id"], name: "index_system_details_on_managed_asset_id"
  end

  create_table "task_assignees", force: :cascade do |t|
    t.bigint "contributor_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "project_task_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["contributor_id"], name: "index_task_assignees_on_contributor_id"
    t.index ["project_task_id"], name: "index_task_assignees_on_project_task_id"
  end

  create_table "task_checklists", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "name"
    t.integer "order", default: 0
    t.text "task_ids", default: [], array: true
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_task_checklists_on_company_id"
    t.index ["workspace_id"], name: "index_task_checklists_on_workspace_id"
  end

  create_table "task_criteria", force: :cascade do |t|
    t.string "attribute"
    t.bigint "automated_task_id"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "operator"
    t.integer "subject_id"
    t.string "subject_type"
    t.datetime "updated_at", precision: nil, null: false
    t.string "value"
    t.index ["automated_task_id"], name: "index_task_criteria_on_automated_task_id"
    t.index ["company_id"], name: "index_task_criteria_on_company_id"
  end

  create_table "tasks", force: :cascade do |t|
    t.text "assignees", default: [], array: true
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "description"
    t.integer "order", default: 0
    t.integer "priority", default: 0
    t.bigint "task_checklist_id"
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_tasks_on_company_id"
    t.index ["task_checklist_id"], name: "index_tasks_on_task_checklist_id"
    t.index ["workspace_id"], name: "index_tasks_on_workspace_id"
  end

  create_table "telecom_providers", force: :cascade do |t|
    t.boolean "archived", default: false
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.string "logo_url"
    t.string "name", null: false
    t.boolean "show_insights_box", default: true
    t.integer "status", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.bigint "vendor_id"
    t.index ["creator_id"], name: "index_telecom_providers_on_creator_id"
  end

  create_table "telecom_service_locations", force: :cascade do |t|
    t.bigint "location_id"
    t.bigint "telecom_service_id"
    t.index ["location_id"], name: "index_telecom_service_locations_on_location_id"
    t.index ["telecom_service_id"], name: "index_telecom_service_locations_on_telecom_service_id"
  end

  create_table "telecom_service_price_changes", force: :cascade do |t|
    t.integer "company_id"
    t.float "cost"
    t.datetime "created_at", precision: nil, null: false
    t.integer "telecom_service_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "telecom_services", id: :serial, force: :cascade do |t|
    t.string "account_number"
    t.boolean "all_locations"
    t.datetime "archived_at", precision: nil
    t.boolean "auto_monthly_cost"
    t.integer "category_id"
    t.string "circuit_id"
    t.integer "company_id"
    t.string "connection"
    t.integer "contract_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.text "description"
    t.string "dns1"
    t.string "dns2"
    t.string "equipment"
    t.string "gateway"
    t.string "guid"
    t.float "monthly_cost"
    t.string "name"
    t.string "network_speed"
    t.integer "number_of_lines"
    t.string "pin"
    t.integer "priority"
    t.bigint "product_id"
    t.integer "service_type"
    t.string "subnet"
    t.text "subtypes", default: [], array: true
    t.integer "telecom_provider_id"
    t.integer "term_type"
    t.float "total_spend"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "vendor_id"
    t.string "vendor_name"
    t.index ["company_id"], name: "index_telecom_services_on_company_id"
    t.index ["creator_id"], name: "index_telecom_services_on_creator_id"
    t.index ["guid"], name: "index_telecom_services_on_guid", unique: true
  end

  create_table "temporary_company_subscriptions", force: :cascade do |t|
    t.bigint "company_id"
    t.string "company_subdomain"
    t.datetime "created_at", null: false
    t.string "current_interval"
    t.string "desired_interval"
    t.string "desired_modules", default: [], array: true
    t.boolean "do_not_change_plan", default: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_temporary_company_subscriptions_on_company_id"
  end

  create_table "ticket_email_attachments", force: :cascade do |t|
    t.string "attached_file_content_type"
    t.string "attached_file_file_name"
    t.integer "attached_file_file_size"
    t.datetime "attached_file_updated_at", precision: nil
    t.string "attachment"
    t.string "content_id", default: ""
    t.datetime "created_at", precision: nil, null: false
    t.boolean "is_hidden", default: false
    t.string "name"
    t.integer "ticket_email_id"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "ticket_emails", force: :cascade do |t|
    t.text "body_html"
    t.text "body_text"
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "custom_form_id"
    t.string "from"
    t.integer "help_ticket_id"
    t.string "message_id", null: false
    t.text "recipients", default: [], array: true
    t.string "s3_message_id"
    t.string "subject"
    t.boolean "ticket_created", default: false
    t.string "to"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "workspace_id"
    t.index ["custom_form_id"], name: "index_ticket_emails_on_custom_form_id"
    t.index ["message_id", "company_id"], name: "unique_ticket_email_except_pmikyoto_specific_message", unique: true, where: "(NOT ((company_id = 9879) AND ((message_id)::text ~~ '%**************%'::text)))"
    t.index ["workspace_id"], name: "index_ticket_emails_on_workspace_id"
  end

  create_table "ticket_list_columns", force: :cascade do |t|
    t.json "columns"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "user_id"
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_ticket_list_columns_on_company_id"
    t.index ["user_id"], name: "index_ticket_list_columns_on_user_id"
    t.index ["workspace_id"], name: "index_ticket_list_columns_on_workspace_id"
  end

  create_table "ticket_sessions", force: :cascade do |t|
    t.string "company_user_avatar", default: ""
    t.bigint "company_user_id"
    t.string "company_user_name", default: ""
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expired_at", precision: nil
    t.bigint "help_ticket_id"
    t.string "ip"
    t.json "query_params"
    t.string "rails_guid"
    t.string "request_session_id", default: ""
    t.integer "ticket_count"
    t.text "ticket_ids", default: [], array: true
    t.datetime "updated_at", precision: nil, null: false
    t.string "window_guid"
    t.index ["company_user_id"], name: "index_ticket_sessions_on_company_user_id"
    t.index ["help_ticket_id"], name: "index_ticket_sessions_on_help_ticket_id"
    t.index ["window_guid"], name: "index_ticket_sessions_on_window_guid"
  end

  create_table "time_spent_entries", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.integer "hours_spent"
    t.integer "minutes_spent"
    t.datetime "started_at", precision: nil
    t.bigint "time_spent_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["time_spent_id"], name: "index_time_spent_entries_on_time_spent_id"
  end

  create_table "time_spents", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "end_time", default: "00:00"
    t.bigint "help_ticket_activity_id"
    t.bigint "help_ticket_comment_id"
    t.bigint "help_ticket_id"
    t.integer "hours_spent"
    t.integer "minutes_spent"
    t.string "start_time", default: "00:00"
    t.datetime "started_at", precision: nil
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_time_spents_on_company_user_id"
    t.index ["help_ticket_activity_id"], name: "index_time_spents_on_help_ticket_activity_id"
    t.index ["help_ticket_comment_id"], name: "index_time_spents_on_help_ticket_comment_id"
    t.index ["help_ticket_id"], name: "index_time_spents_on_help_ticket_id"
  end

  create_table "transaction_departments", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.bigint "department_id"
    t.bigint "general_transaction_id"
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_transaction_departments_on_department_id"
    t.index ["general_transaction_id"], name: "index_transaction_departments_on_general_transaction_id"
  end

  create_table "transaction_products_mappings", force: :cascade do |t|
    t.float "amount"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "preference"
    t.bigint "product_id"
    t.string "transaction_name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_transaction_products_mappings_on_company_id"
    t.index ["product_id"], name: "index_transaction_products_mappings_on_product_id"
  end

  create_table "ubiquiti_configs", force: :cascade do |t|
    t.integer "company_id"
    t.bigint "company_user_id"
    t.integer "import_type", default: 1, null: false
    t.index ["company_user_id"], name: "index_ubiquiti_configs_on_company_user_id"
  end

  create_table "ubiquiti_controller_sites", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "integrations_location_id"
    t.bigint "ubiquiti_controller_id"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["integrations_location_id"], name: "index_ubiquiti_controller_sites_on_integrations_location_id"
    t.index ["ubiquiti_controller_id"], name: "index_ubiquiti_controller_sites_on_ubiquiti_controller_id"
  end

  create_table "ubiquiti_controllers", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "password"
    t.text "sites", default: [], array: true
    t.bigint "ubiquiti_configs_id"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.string "username"
    t.index ["ubiquiti_configs_id"], name: "index_ubiquiti_controllers_on_ubiquiti_configs_id"
  end

  create_table "update_subscription_logs", force: :cascade do |t|
    t.boolean "activate_expired_plans", default: false
    t.bigint "company_id"
    t.string "company_subdomain"
    t.datetime "created_at", null: false
    t.string "existing_plan"
    t.string "new_plan"
    t.string "response"
    t.string "status"
    t.datetime "updated_at", null: false
    t.string "user_email"
  end

  create_table "user_accesses", id: :serial, force: :cascade do |t|
    t.datetime "accessed_at", precision: nil
    t.string "auth_token"
    t.integer "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expired_at", precision: nil
    t.string "redirect_to"
    t.string "super_admin_guid"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "user_devices", force: :cascade do |t|
    t.string "app_version"
    t.datetime "created_at", null: false
    t.string "device_type"
    t.string "os"
    t.string "screen_size"
    t.string "token"
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["token"], name: "index_user_devices_on_token", unique: true
    t.index ["user_id"], name: "index_user_devices_on_user_id"
  end

  create_table "user_locations", force: :cascade do |t|
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "city"
    t.string "country"
    t.date "deleted_at"
    t.boolean "primary", default: false
    t.string "state"
    t.bigint "user_id"
    t.string "zip"
    t.index ["user_id"], name: "index_user_locations_on_user_id"
  end

  create_table "user_pinned_companies", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["company_id"], name: "index_user_pinned_companies_on_company_id"
    t.index ["user_id"], name: "index_user_pinned_companies_on_user_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.text "access_id"
    t.integer "admin_id"
    t.boolean "archived", default: false
    t.string "auth_token", default: ""
    t.string "avatar_content_type"
    t.string "avatar_file_name"
    t.integer "avatar_file_size"
    t.datetime "avatar_updated_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: ""
    t.string "first_name"
    t.string "guid"
    t.boolean "has_confirmed_email", default: false
    t.boolean "has_seen_module_onboarding", default: false
    t.jsonb "has_seen_module_walkthroughs", default: {"assets"=>false, "company"=>false, "telecom"=>false, "vendors"=>false, "helpdesk"=>false, "contracts"=>false, "vertical_view"=>false, "comment_ai_tools"=>false, "ticket_ai_summary"=>false, "ticket_modern_view"=>false}
    t.boolean "has_seen_onboarding", default: false
    t.boolean "has_seen_sample_onboarding", default: false
    t.datetime "invitation_accepted_at", precision: nil
    t.datetime "invitation_created_at", precision: nil
    t.integer "invitation_limit"
    t.datetime "invitation_sent_at", precision: nil
    t.string "invitation_token"
    t.integer "invitations_count", default: 0
    t.integer "invited_by_id"
    t.string "invited_by_type"
    t.string "last_name"
    t.datetime "last_sign_in_at", precision: nil
    t.inet "last_sign_in_ip"
    t.string "onboarding_module_selected"
    t.text "refresh_id"
    t.datetime "remember_created_at", precision: nil
    t.datetime "reset_password_sent_at", precision: nil
    t.string "reset_password_token"
    t.integer "role"
    t.string "session_token"
    t.integer "sign_in_count", default: 0, null: false
    t.boolean "sso_password_provided", default: false
    t.boolean "sso_user", default: false
    t.integer "sso_user_source", default: 0, null: false
    t.boolean "super_admin", default: false
    t.string "temp_email", default: ""
    t.boolean "terms_of_services", default: false
    t.string "timezone", default: "UTC"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["admin_id"], name: "index_users_on_admin_id"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["guid"], name: "index_users_on_guid", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invitations_count"], name: "index_users_on_invitations_count"
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "valid_email_extensions", force: :cascade do |t|
    t.integer "category_type", default: 0
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "extensions", default: [], array: true
    t.datetime "updated_at", null: false
    t.bigint "workspace_id"
    t.index ["company_id"], name: "index_valid_email_extensions_on_company_id"
    t.index ["workspace_id"], name: "index_valid_email_extensions_on_workspace_id"
  end

  create_table "vendor_contacts", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.string "department"
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.text "notes"
    t.string "phone_number"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.string "role"
    t.datetime "updated_at", precision: nil, null: false
    t.integer "vendor_id"
    t.index ["vendor_id"], name: "index_vendor_contacts_on_vendor_id"
  end

  create_table "vendors", id: :serial, force: :cascade do |t|
    t.string "account_number"
    t.text "address1"
    t.boolean "archived", default: false
    t.bigint "base_product_id"
    t.text "billing_requirements"
    t.string "business_unit"
    t.integer "category_id", null: false
    t.string "city"
    t.integer "company_id"
    t.integer "contract_id"
    t.string "country_name"
    t.datetime "created_at", precision: nil, null: false
    t.bigint "creator_id"
    t.text "default_tags", default: [], array: true
    t.string "department"
    t.text "description"
    t.string "email"
    t.text "friendly_name", default: [], array: true
    t.string "guid"
    t.boolean "hide_setup_alert_notifications", default: false
    t.boolean "ignore_telecom_notification"
    t.datetime "imported", precision: nil
    t.boolean "is_cloud_platform", default: false
    t.bigint "location_id"
    t.string "logo_url"
    t.citext "name"
    t.text "notes"
    t.text "payment_terms"
    t.string "phone_number"
    t.string "phone_number_country_code", default: "US"
    t.string "phone_number_country_code_number", default: "1"
    t.boolean "pinned", default: false
    t.bigint "primary_internal_contact_id"
    t.bigint "secondary_internal_contact_id"
    t.string "state"
    t.float "total_spend"
    t.datetime "updated_at", precision: nil, null: false
    t.string "url"
    t.bigint "vendor_source_id"
    t.string "vendor_source_type"
    t.string "zip"
    t.index ["base_product_id"], name: "index_vendors_on_base_product_id"
    t.index ["company_id"], name: "index_vendors_on_company_id"
    t.index ["creator_id"], name: "index_vendors_on_creator_id"
    t.index ["guid"], name: "index_vendors_on_guid", unique: true
    t.index ["name", "company_id"], name: "vendors_name_company_id_key", unique: true
    t.index ["vendor_source_type", "vendor_source_id"], name: "index_vendors_on_vendor_source_type_and_vendor_source_id"
  end

  create_table "warranty_apis", force: :cascade do |t|
    t.text "config"
    t.datetime "created_at", precision: nil, null: false
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "window_scripts", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.string "name"
    t.string "script_link"
    t.datetime "updated_at", precision: nil, null: false
    t.string "version"
  end

  create_table "windows_apps_update_logs", force: :cascade do |t|
    t.string "app_version"
    t.string "company_guid"
    t.string "company_user_guid"
    t.datetime "created_at", precision: nil, null: false
    t.string "source"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "windows_exes", force: :cascade do |t|
    t.integer "app_type", default: 0
    t.string "app_version", default: "", null: false
    t.datetime "created_at", precision: nil, null: false
    t.text "description"
    t.string "exe_link"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.float "version"
  end

  create_table "windows_mini_helpdesk_versions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.text "description"
    t.string "installer_link"
    t.string "latest_yml"
    t.string "name"
    t.string "new_blockmap"
    t.string "old_blockmap"
    t.datetime "updated_at", null: false
    t.string "version", default: ""
  end

  create_table "workspaces", force: :cascade do |t|
    t.string "color_class"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "description"
    t.string "guid"
    t.string "icon_class"
    t.string "name"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id", "name"], name: "index_workspaces_on_company_id_and_name", unique: true
    t.index ["company_id"], name: "index_workspaces_on_company_id"
    t.index ["guid"], name: "index_workspaces_on_guid", unique: true
  end

  create_table "xero_configs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "expires_in", precision: nil
    t.string "refresh_token"
    t.string "tenant_ids", default: [], array: true
    t.string "token"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_xero_configs_on_company_id"
  end

  create_table "xls_import_rows", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "data"
    t.string "error"
    t.integer "error_type"
    t.boolean "is_duplicate", default: false
    t.boolean "is_valid", default: true
    t.integer "issue_fixed", default: 0
    t.jsonb "raw_data"
    t.boolean "status"
    t.datetime "updated_at", precision: nil, null: false
    t.string "worksheet_name"
    t.bigint "xls_imports_id"
    t.bigint "xls_sheet_id"
    t.index ["xls_imports_id"], name: "index_xls_import_rows_on_xls_imports_id"
    t.index ["xls_sheet_id"], name: "index_xls_import_rows_on_xls_sheet_id"
  end

  create_table "xls_imports", force: :cascade do |t|
    t.string "attached_file_content_type"
    t.string "attached_file_file_name"
    t.integer "attached_file_file_size"
    t.datetime "attached_file_updated_at", precision: nil
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.integer "duplicate_rows_count", default: 0
    t.jsonb "headers"
    t.integer "imported_rows_count", default: 0
    t.string "module_name"
    t.string "record_type"
    t.boolean "status"
    t.integer "total_rows_count", default: 0
    t.datetime "updated_at", precision: nil, null: false
    t.integer "xls_imports_status"
  end

  create_table "xls_sheets", force: :cascade do |t|
    t.jsonb "correct_mapping"
    t.datetime "created_at", precision: nil, null: false
    t.boolean "is_processing", default: false
    t.jsonb "raw_headers"
    t.string "sheet_name"
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "xls_imports_id"
    t.index ["xls_imports_id"], name: "index_xls_sheets_on_xls_imports_id"
  end

  create_table "xml_discovery_logs", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.bigint "discovered_asset_id"
    t.datetime "updated_at", precision: nil, null: false
    t.xml "xml_discovery_log"
    t.index ["discovered_asset_id"], name: "index_xml_discovery_logs_on_discovered_asset_id"
  end

  add_foreign_key "actionable_alerts", "companies"
  add_foreign_key "actionable_alerts", "workspaces"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "admin_overview_customizations", "companies"
  add_foreign_key "admin_overview_customizations", "workspaces"
  add_foreign_key "agent_locations", "companies"
  add_foreign_key "agent_locations", "contributors", column: "managed_by_contributor_id"
  add_foreign_key "agent_locations", "locations"
  add_foreign_key "ai_summaries", "help_tickets"
  add_foreign_key "ai_usage_costs", "companies"
  add_foreign_key "alert_defaults", "companies"
  add_foreign_key "analytics_report_templates", "default_analytics_report_templates"
  add_foreign_key "analytics_report_templates", "workspaces"
  add_foreign_key "applied_build_data_sets", "company_builds"
  add_foreign_key "applied_build_data_sets", "company_users", column: "build_by_id"
  add_foreign_key "apps_logs_options", "companies"
  add_foreign_key "article_expanded_privileges", "articles"
  add_foreign_key "article_expanded_privileges", "contributors"
  add_foreign_key "article_privileges", "articles"
  add_foreign_key "article_privileges", "contributors"
  add_foreign_key "article_tags", "articles"
  add_foreign_key "article_tags", "companies"
  add_foreign_key "articles", "companies"
  add_foreign_key "articles", "company_users"
  add_foreign_key "articles", "help_ticket_comments"
  add_foreign_key "articles", "help_tickets"
  add_foreign_key "articles", "locations"
  add_foreign_key "articles", "workspaces"
  add_foreign_key "asset_connector_logs", "companies"
  add_foreign_key "asset_connector_logs", "company_users"
  add_foreign_key "asset_insight_widgets", "company_users"
  add_foreign_key "asset_lifecycles", "companies"
  add_foreign_key "asset_preferences", "companies"
  add_foreign_key "asset_risk_center_widgets", "companies"
  add_foreign_key "discovered_asset_preferences", "companies"
  add_foreign_key "asset_softwares", "managed_assets"
  add_foreign_key "asset_sources", "company_integrations"
  add_foreign_key "asset_sources", "discovered_assets"
  add_foreign_key "asset_sources", "managed_assets"
  add_foreign_key "asset_usage_histories", "company_asset_statuses"
  add_foreign_key "asset_usage_histories", "contributors", column: "managed_by_contributor_id"
  add_foreign_key "asset_usage_histories", "contributors", column: "used_by_contributor_id"
  add_foreign_key "asset_usage_histories", "locations"
  add_foreign_key "asset_usage_histories", "managed_assets"
  add_foreign_key "asset_user_accounts", "discovered_assets"
  add_foreign_key "asset_user_accounts", "managed_assets"
  add_foreign_key "assets_automated_tasks", "companies"
  add_foreign_key "assets_automated_tasks", "contributors"
  add_foreign_key "assets_event_details", "assets_event_subject_types", column: "event_subject_type_id"
  add_foreign_key "assets_event_details", "assets_task_events", column: "task_event_id"
  add_foreign_key "assets_task_actions", "assets_action_types", column: "action_type_id"
  add_foreign_key "assets_task_actions", "assets_automated_tasks", column: "automated_task_id"
  add_foreign_key "assets_task_events", "assets_automated_tasks", column: "automated_task_id"
  add_foreign_key "assets_task_events", "assets_event_types", column: "event_type_id"
  add_foreign_key "assignment_informations", "locations"
  add_foreign_key "assignment_informations", "managed_assets"
  add_foreign_key "attachment_uploads", "companies"
  add_foreign_key "automated_tasks_action_details", "automated_tasks_action_types", column: "action_type_id"
  add_foreign_key "automated_tasks_action_details", "automated_tasks_automated_tasks", column: "automated_task_id"
  add_foreign_key "automated_tasks_automated_tasks", "automated_task_groups"
  add_foreign_key "automated_tasks_automated_tasks", "companies"
  add_foreign_key "automated_tasks_automated_tasks", "contributors"
  add_foreign_key "automated_tasks_automated_tasks", "msp_templates_automated_tasks"
  add_foreign_key "automated_tasks_automated_tasks", "workspaces"
  add_foreign_key "automated_tasks_event_details", "automated_tasks_event_subject_types", column: "event_subject_type_id"
  add_foreign_key "automated_tasks_execution_dates", "companies"
  add_foreign_key "automated_tasks_execution_logs", "automated_tasks_automated_tasks"
  add_foreign_key "automated_tasks_execution_logs", "companies"
  add_foreign_key "automated_tasks_execution_logs", "workspaces"
  add_foreign_key "automated_tasks_task_action_templates", "automated_tasks_task_templates", column: "automated_task_template_id"
  add_foreign_key "automated_tasks_task_actions", "automated_tasks_action_types", column: "action_type_id"
  add_foreign_key "automated_tasks_task_actions", "email_templates"
  add_foreign_key "automated_tasks_task_event_templates", "automated_tasks_task_templates", column: "automated_task_template_id"
  add_foreign_key "aws_assets_configs", "companies"
  add_foreign_key "aws_assets_configs", "company_users"
  add_foreign_key "aws_configs", "companies"
  add_foreign_key "azure_ad_assets_configs", "companies"
  add_foreign_key "azure_ad_assets_configs", "company_users"
  add_foreign_key "azure_ad_configs", "companies"
  add_foreign_key "azure_ad_groups", "azure_ad_configs", column: "config_id", name: "azure_ad_groups_configs"
  add_foreign_key "azure_ad_groups", "companies"
  add_foreign_key "azure_assets_configs", "companies"
  add_foreign_key "azure_assets_configs", "company_users"
  add_foreign_key "azure_configs", "companies"
  add_foreign_key "bill_configs", "companies"
  add_foreign_key "blocked_entities", "companies"
  add_foreign_key "blocked_entities", "msp_templates_blocked_keywords"
  add_foreign_key "blocked_entities", "workspaces"
  add_foreign_key "build_data_sets", "company_builds"
  add_foreign_key "business_hours", "companies"
  add_foreign_key "business_hours", "workspaces"
  add_foreign_key "categories", "msp_templates_categories"
  add_foreign_key "categories", "workspaces"
  add_foreign_key "closing_surveys", "help_tickets"
  add_foreign_key "cloud_asset_attributes", "discovered_assets"
  add_foreign_key "cloud_asset_attributes", "managed_assets"
  add_foreign_key "cloud_usage_transactions", "companies"
  add_foreign_key "cloud_usage_transactions", "company_integrations"
  add_foreign_key "cloud_usage_transactions", "discovered_vendors"
  add_foreign_key "cloud_usage_transactions", "products"
  add_foreign_key "cloud_usage_transactions", "vendors"
  add_foreign_key "cognito_logs", "users"
  add_foreign_key "company_asset_types", "msp_templates_asset_types"
  add_foreign_key "company_association_event_logs", "companies", column: "child_company_id"
  add_foreign_key "company_association_event_logs", "companies", column: "parent_company_id"
  add_foreign_key "company_cache_keys", "companies"
  add_foreign_key "company_cache_keys", "workspaces"
  add_foreign_key "company_credit_cards", "companies"
  add_foreign_key "company_domains", "companies"
  add_foreign_key "company_integrations", "companies"
  add_foreign_key "company_integrations", "integrations"
  add_foreign_key "company_invitations", "companies", column: "child_company_id"
  add_foreign_key "company_invitations", "companies", column: "parent_company_id"
  add_foreign_key "company_mailers", "companies"
  add_foreign_key "company_mailers", "default_mailers"
  add_foreign_key "company_mailers", "workspaces"
  add_foreign_key "company_trackings", "companies"
  add_foreign_key "company_trackings", "registration_emails", column: "registration_emails_id"
  add_foreign_key "company_user_activities", "company_users"
  add_foreign_key "company_user_mailers", "company_users"
  add_foreign_key "company_user_mailers", "default_mailers"
  add_foreign_key "company_users", "companies"
  add_foreign_key "company_users", "contributors"
  add_foreign_key "company_users", "custom_forms"
  add_foreign_key "company_users", "users"
  add_foreign_key "company_users", "workspaces", column: "default_workspace_id"
  add_foreign_key "contract_attachments", "contracts"
  add_foreign_key "contract_contacts", "company_users"
  add_foreign_key "contract_contacts", "contracts"
  add_foreign_key "contract_departments", "contracts"
  add_foreign_key "contract_departments", "departments"
  add_foreign_key "contract_locations", "contracts"
  add_foreign_key "contract_locations", "locations"
  add_foreign_key "contract_preferences", "companies"
  add_foreign_key "contract_tags", "companies"
  add_foreign_key "contract_tags", "contracts"
  add_foreign_key "contracts", "categories"
  add_foreign_key "contracts", "companies"
  add_foreign_key "contracts", "contributors", column: "creator_id"
  add_foreign_key "contracts", "products"
  add_foreign_key "contracts", "vendors"
  add_foreign_key "contributor_actionable_alerts", "actionable_alerts"
  add_foreign_key "contributor_actionable_alerts", "contributors"
  add_foreign_key "contributors", "companies"
  add_foreign_key "costs", "asset_lifecycles"
  add_foreign_key "costs", "depreciations"
  add_foreign_key "costs", "managed_assets"
  add_foreign_key "custom_domains", "companies"
  add_foreign_key "custom_domains", "load_balancers"
  add_foreign_key "custom_form_attachments", "companies"
  add_foreign_key "custom_form_attachments", "custom_form_values"
  add_foreign_key "custom_form_field_permissions", "contributors"
  add_foreign_key "custom_form_field_permissions", "custom_form_fields"
  add_foreign_key "custom_form_field_templates", "custom_form_templates"
  add_foreign_key "custom_form_fields", "custom_forms"
  add_foreign_key "custom_form_fields", "msp_templates_custom_form_fields"
  add_foreign_key "custom_form_groups", "custom_forms"
  add_foreign_key "custom_form_groups", "groups"
  add_foreign_key "custom_form_values", "companies"
  add_foreign_key "custom_forms", "automated_tasks_automated_tasks"
  add_foreign_key "custom_forms", "companies"
  add_foreign_key "custom_forms", "custom_forms", column: "original_form_id", on_delete: :nullify
  add_foreign_key "custom_forms", "msp_templates_custom_forms"
  add_foreign_key "custom_forms", "workspaces"
  add_foreign_key "custom_reports", "companies"
  add_foreign_key "custom_reports", "company_users", column: "creator_id"
  add_foreign_key "custom_reports", "workspaces"
  add_foreign_key "custom_survey_actions", "custom_survey_questions"
  add_foreign_key "custom_survey_actions", "custom_survey_rules"
  add_foreign_key "custom_survey_choices", "custom_survey_questions"
  add_foreign_key "custom_survey_questions", "custom_surveys"
  add_foreign_key "custom_survey_responses", "companies"
  add_foreign_key "custom_survey_responses", "contributors"
  add_foreign_key "custom_survey_responses", "custom_surveys"
  add_foreign_key "custom_survey_responses", "help_tickets"
  add_foreign_key "custom_survey_responses", "workspaces"
  add_foreign_key "custom_survey_rules", "custom_survey_choices"
  add_foreign_key "custom_survey_rules", "custom_survey_questions"
  add_foreign_key "custom_survey_rules", "custom_surveys"
  add_foreign_key "custom_survey_triggers", "custom_survey_questions"
  add_foreign_key "custom_survey_triggers", "custom_surveys"
  add_foreign_key "custom_surveys", "companies"
  add_foreign_key "custom_surveys", "workspaces"
  add_foreign_key "dashboard_preferences", "company_users"
  add_foreign_key "default_products", "categories", column: "default_category_id"
  add_foreign_key "default_products", "default_vendors"
  add_foreign_key "depreciations", "companies"
  add_foreign_key "discovered_asset_histories", "companies"
  add_foreign_key "discovered_asset_histories", "company_users", on_delete: :nullify
  add_foreign_key "discovered_asset_types", "companies"
  add_foreign_key "discovered_assets", "companies"
  add_foreign_key "discovered_assets", "integrations_locations", column: "integrations_locations_id"
  add_foreign_key "discovered_assets", "managed_assets"
  add_foreign_key "discovered_assets", "probe_locations"
  add_foreign_key "discovered_assets_hardware_details", "discovered_assets"
  add_foreign_key "discovered_data_services", "companies"
  add_foreign_key "discovered_user_histories", "companies"
  add_foreign_key "discovered_user_histories", "discovered_users"
  add_foreign_key "discovered_user_sources", "discovered_users"
  add_foreign_key "discovered_users", "integrations_locations", column: "location_id"
  add_foreign_key "discovered_vendors", "categories"
  add_foreign_key "discovered_vendors", "companies"
  add_foreign_key "discovered_vendors", "vendors"
  add_foreign_key "discovered_voice_services", "companies"
  add_foreign_key "email_formats", "companies"
  add_foreign_key "event_logs", "companies"
  add_foreign_key "event_logs", "workspaces"
  add_foreign_key "expanded_form_field_permissions", "contributors"
  add_foreign_key "expanded_form_field_permissions", "custom_form_fields"
  add_foreign_key "expanded_privileges", "companies"
  add_foreign_key "expanded_privileges", "contributors"
  add_foreign_key "expanded_privileges", "workspaces"
  add_foreign_key "expensify_configs", "companies"
  add_foreign_key "feature_request_attachments", "company_users", column: "creator_id"
  add_foreign_key "feature_request_attachments", "feature_requests"
  add_foreign_key "feature_request_comments", "company_users", column: "creator_id"
  add_foreign_key "feature_request_comments", "feature_requests"
  add_foreign_key "feature_request_images", "feature_requests"
  add_foreign_key "feature_request_votes", "company_users"
  add_foreign_key "feature_request_votes", "feature_requests"
  add_foreign_key "feature_requests", "company_users", column: "creator_id"
  add_foreign_key "field_position_templates", "custom_form_field_templates"
  add_foreign_key "field_positions", "custom_form_fields"
  add_foreign_key "general_transaction_invoices", "general_transactions"
  add_foreign_key "general_transaction_invoices", "invoices"
  add_foreign_key "general_transaction_tags", "companies"
  add_foreign_key "general_transaction_tags", "general_transactions"
  add_foreign_key "general_transactions", "company_integrations"
  add_foreign_key "general_transactions", "discovered_vendors"
  add_foreign_key "general_transactions", "locations"
  add_foreign_key "general_transactions", "products"
  add_foreign_key "google_assets_configs", "companies"
  add_foreign_key "google_assets_configs", "company_users"
  add_foreign_key "google_assets_projects", "discovered_assets"
  add_foreign_key "google_assets_projects", "google_assets_configs"
  add_foreign_key "google_assets_projects", "managed_assets"
  add_foreign_key "google_workspace_configs", "companies"
  add_foreign_key "google_workspace_configs", "company_users"
  add_foreign_key "group_members", "contributors"
  add_foreign_key "group_members", "groups"
  add_foreign_key "group_privileges", "groups"
  add_foreign_key "groups", "companies"
  add_foreign_key "groups", "company_users", column: "granted_by_id"
  add_foreign_key "groups", "contributors"
  add_foreign_key "groups", "msp_templates_groups"
  add_foreign_key "groups", "workspaces"
  add_foreign_key "gsuite_ad_configs", "companies"
  add_foreign_key "gsuite_ad_groups", "companies"
  add_foreign_key "gsuite_ad_groups", "gsuite_ad_configs", column: "config_id", name: "gsuite_ad_groups_configs"
  add_foreign_key "gsuite_ad_organizations", "companies"
  add_foreign_key "gsuite_ad_organizations", "gsuite_ad_configs", column: "config_id", name: "gsuite_ad_organizations_configs"
  add_foreign_key "gsuite_app_users", "gsuite_apps", column: "app_id", name: "gsuite_apps"
  add_foreign_key "gsuite_app_users", "gsuite_users", column: "user_id", name: "gsuite_users"
  add_foreign_key "gsuite_apps", "companies"
  add_foreign_key "gsuite_apps", "gsuite_configs", column: "config_id", name: "gsuite_apps_configs"
  add_foreign_key "gsuite_configs", "companies"
  add_foreign_key "gsuite_users", "companies"
  add_foreign_key "gsuite_users", "gsuite_configs", column: "config_id", name: "gsuite_users_configs"
  add_foreign_key "guests", "companies"
  add_foreign_key "guests", "contributors"
  add_foreign_key "help_center_logos", "companies"
  add_foreign_key "help_desk_reports", "companies"
  add_foreign_key "help_desk_reports", "company_users", column: "creator_id"
  add_foreign_key "help_ticket_activities", "help_tickets"
  add_foreign_key "help_ticket_comments", "custom_form_attachments"
  add_foreign_key "help_ticket_comments", "help_ticket_activities"
  add_foreign_key "help_ticket_comments", "help_ticket_comments", column: "parent_comment_id"
  add_foreign_key "help_ticket_comments", "help_tickets"
  add_foreign_key "help_ticket_comments", "help_tickets", column: "merged_ticket_id"
  add_foreign_key "help_ticket_drafts", "companies"
  add_foreign_key "help_ticket_drafts", "company_users"
  add_foreign_key "help_ticket_drafts", "help_tickets"
  add_foreign_key "help_ticket_drafts", "workspaces"
  add_foreign_key "help_ticket_task_checklists", "help_tickets"
  add_foreign_key "help_ticket_task_checklists", "task_checklists"
  add_foreign_key "help_tickets", "companies"
  add_foreign_key "help_tickets", "custom_forms"
  add_foreign_key "help_tickets", "workspaces"
  add_foreign_key "helpdesk_custom_emails", "companies"
  add_foreign_key "helpdesk_custom_forms", "custom_forms"
  add_foreign_key "helpdesk_email_formats", "companies"
  add_foreign_key "helpdesk_email_formats", "workspaces"
  add_foreign_key "helpdesk_faqs", "categories"
  add_foreign_key "helpdesk_faqs", "companies"
  add_foreign_key "helpdesk_faqs", "msp_templates_helpdesk_faqs"
  add_foreign_key "helpdesk_faqs", "workspaces"
  add_foreign_key "helpdesk_reports", "companies"
  add_foreign_key "helpdesk_reports", "workspaces"
  add_foreign_key "helpdesk_settings", "companies"
  add_foreign_key "helpdesk_settings", "workspaces"
  add_foreign_key "integrated_vendors", "companies"
  add_foreign_key "integrations_app_sources", "integrations"
  add_foreign_key "integrations_app_sources", "integrations_apps", column: "app_id", name: "integrations_source_on_app_id"
  add_foreign_key "integrations_app_usages", "company_integrations", name: "integrations_usage_on_company_id"
  add_foreign_key "integrations_app_usages", "integrations", column: "source_id", name: "integrations_usage_on_source_id"
  add_foreign_key "integrations_app_usages", "integrations_apps", column: "app_id", name: "integrations_usage_on_app_id"
  add_foreign_key "integrations_app_users", "integrations_apps", column: "app_id", name: "integrations_user_on_app_id"
  add_foreign_key "integrations_app_users", "integrations_users", column: "user_id", name: "integrations_app_on_users_id"
  add_foreign_key "integrations_apps", "companies"
  add_foreign_key "integrations_apps", "products", name: "integrations_apps_on_products"
  add_foreign_key "integrations_apps", "vendors"
  add_foreign_key "integrations_locations", "companies"
  add_foreign_key "integrations_locations", "locations"
  add_foreign_key "integrations_services", "companies"
  add_foreign_key "integrations_services", "integrations_apps", column: "integrations_apps_id"
  add_foreign_key "integrations_user_sources", "integrations"
  add_foreign_key "integrations_user_sources", "integrations_users", column: "user_id", name: "integrations_source_on_user_id"
  add_foreign_key "integrations_users", "companies"
  add_foreign_key "integrations_users", "company_users"
  add_foreign_key "ip_addresses", "locations"
  add_foreign_key "ip_addresses", "telecom_services"
  add_foreign_key "ip_addresses", "vendors"
  add_foreign_key "it_report_preferences", "companies"
  add_foreign_key "it_reports", "companies"
  add_foreign_key "it_reports", "company_users", column: "creator_id"
  add_foreign_key "jamf_pro_configs", "companies"
  add_foreign_key "jamf_pro_configs", "company_users"
  add_foreign_key "kandji_configs", "companies"
  add_foreign_key "kandji_configs", "company_users"
  add_foreign_key "kaseya_configs", "companies"
  add_foreign_key "kaseya_configs", "company_users"
  add_foreign_key "library_documents", "company_users", column: "creator_id"
  add_foreign_key "library_documents", "msp_templates_documents"
  add_foreign_key "library_documents", "workspaces"
  add_foreign_key "linkable_links", "linkables", column: "source_id"
  add_foreign_key "linkable_links", "linkables", column: "target_id"
  add_foreign_key "linkables", "companies"
  add_foreign_key "locations", "companies"
  add_foreign_key "locations", "custom_forms"
  add_foreign_key "managed_asset_activities", "managed_assets"
  add_foreign_key "managed_asset_tags", "companies"
  add_foreign_key "managed_asset_tags", "company_asset_tags"
  add_foreign_key "managed_asset_tags", "managed_assets"
  add_foreign_key "managed_assets", "agent_locations"
  add_foreign_key "managed_assets", "companies"
  add_foreign_key "managed_assets", "company_asset_statuses"
  add_foreign_key "managed_assets", "company_asset_types"
  add_foreign_key "managed_assets", "contributors", column: "creator_id"
  add_foreign_key "managed_assets", "locations"
  add_foreign_key "managed_assets", "vendors"
  add_foreign_key "meraki_configs", "companies"
  add_foreign_key "meraki_configs", "company_users"
  add_foreign_key "meraki_records", "companies"
  add_foreign_key "mfa_sessions", "company_users"
  add_foreign_key "mfa_sessions", "user_devices"
  add_foreign_key "mfa_settings", "companies"
  add_foreign_key "mfa_super_admin_sessions", "users"
  add_foreign_key "microsoft_apps", "microsoft_configs", column: "config_id", name: "microsoft_apps_on_configs_id"
  add_foreign_key "microsoft_configs", "companies"
  add_foreign_key "microsoft_services", "companies"
  add_foreign_key "microsoft_services", "microsoft_apps", column: "microsoft_apps_id"
  add_foreign_key "microsoft_users", "integrations_locations", column: "location_id"
  add_foreign_key "module_alert_contributors", "module_alerts"
  add_foreign_key "mosyle_configs", "companies"
  add_foreign_key "ms_intune_assets_configs", "companies"
  add_foreign_key "ms_intune_assets_configs", "company_users"
  add_foreign_key "ms_teams_configs", "companies"
  add_foreign_key "ms_teams_configs", "workspaces"
  add_foreign_key "msp_event_logs", "companies"
  add_foreign_key "msp_templates_asset_types", "company_builds"
  add_foreign_key "msp_templates_asset_types", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_automated_tasks", "companies"
  add_foreign_key "msp_templates_automated_tasks", "company_builds"
  add_foreign_key "msp_templates_automated_tasks", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_blocked_keywords", "company_builds"
  add_foreign_key "msp_templates_blocked_keywords", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_categories", "companies"
  add_foreign_key "msp_templates_categories", "company_builds"
  add_foreign_key "msp_templates_categories", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_custom_form_fields", "msp_templates_custom_forms"
  add_foreign_key "msp_templates_custom_forms", "companies"
  add_foreign_key "msp_templates_custom_forms", "company_builds"
  add_foreign_key "msp_templates_custom_forms", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_documents", "companies"
  add_foreign_key "msp_templates_documents", "company_builds"
  add_foreign_key "msp_templates_documents", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_field_positions", "msp_templates_custom_form_fields"
  add_foreign_key "msp_templates_group_members", "msp_templates_groups"
  add_foreign_key "msp_templates_group_privileges", "msp_templates_groups"
  add_foreign_key "msp_templates_groups", "companies"
  add_foreign_key "msp_templates_helpdesk_custom_forms", "msp_templates_custom_forms"
  add_foreign_key "msp_templates_helpdesk_faqs", "companies"
  add_foreign_key "msp_templates_helpdesk_faqs", "company_builds"
  add_foreign_key "msp_templates_helpdesk_faqs", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_snippets", "companies"
  add_foreign_key "msp_templates_snippets", "company_builds"
  add_foreign_key "msp_templates_snippets", "company_users", column: "last_updated_by_id"
  add_foreign_key "msp_templates_task_events", "msp_templates_automated_tasks"
  add_foreign_key "netsuite_configs", "companies"
  add_foreign_key "okta_app_users", "okta_apps", name: "okta_apps_user_on_app_id"
  add_foreign_key "okta_app_users", "okta_users", name: "okta_app_users_on_user_id"
  add_foreign_key "okta_apps", "companies"
  add_foreign_key "okta_apps", "okta_configs", name: "okta_apps_on_configs_id"
  add_foreign_key "okta_configs", "companies"
  add_foreign_key "okta_users", "companies"
  add_foreign_key "okta_users", "okta_configs", name: "okta_users_on_config_id"
  add_foreign_key "one_login_apps", "one_login_configs", column: "config_id", name: "one_login_apps_on_configs_id"
  add_foreign_key "one_login_users", "companies", name: "company_id_on_onelogin_users"
  add_foreign_key "phone_numbers", "locations"
  add_foreign_key "phone_numbers", "telecom_services"
  add_foreign_key "phone_numbers", "vendors"
  add_foreign_key "predicted_cloud_transactions", "companies"
  add_foreign_key "predicted_cloud_transactions", "company_integrations"
  add_foreign_key "predicted_cloud_transactions", "products"
  add_foreign_key "predicted_cloud_transactions", "vendors"
  add_foreign_key "privileges", "contributors"
  add_foreign_key "privileges", "workspaces"
  add_foreign_key "probe_configs", "companies"
  add_foreign_key "probe_configs", "probe_locations"
  add_foreign_key "probe_locations", "contributors", column: "managed_by_contributor_id"
  add_foreign_key "probe_locations", "locations"
  add_foreign_key "product_vendors", "products"
  add_foreign_key "product_vendors", "vendors"
  add_foreign_key "products", "categories"
  add_foreign_key "products", "companies"
  add_foreign_key "products", "telecom_services"
  add_foreign_key "products", "vendors"
  add_foreign_key "project_tasks", "companies"
  add_foreign_key "project_tasks", "contributors"
  add_foreign_key "project_tasks", "contributors", column: "assignee_id"
  add_foreign_key "project_tasks", "contributors", column: "creator_id"
  add_foreign_key "project_tasks", "help_tickets"
  add_foreign_key "prompts", "prompts_templates"
  add_foreign_key "quick_view_filters", "companies"
  add_foreign_key "quick_view_filters", "company_users"
  add_foreign_key "quick_view_filters", "workspaces"
  add_foreign_key "quickbooks_configs", "companies"
  add_foreign_key "referral_payments", "user_locations", column: "sent_to_id"
  add_foreign_key "referrals", "users", column: "referred_id"
  add_foreign_key "referrals", "users", column: "referrer_id"
  add_foreign_key "reports", "companies"
  add_foreign_key "reports", "contributors", column: "creator_id"
  add_foreign_key "reports", "contributors", column: "last_edited_by_id"
  add_foreign_key "reports", "workspaces"
  add_foreign_key "risk_center_widget_options", "companies"
  add_foreign_key "sage_accounting_configs", "companies"
  add_foreign_key "sage_intacct_configs", "companies"
  add_foreign_key "salesforce_apps", "companies"
  add_foreign_key "salesforce_apps", "salesforce_configs", column: "config_id", name: "salesforce_apps_configs"
  add_foreign_key "salesforce_configs", "companies"
  add_foreign_key "salesforce_users", "companies"
  add_foreign_key "salesforce_users", "salesforce_configs", column: "config_id", name: "salesforce_users_configs"
  add_foreign_key "scheduled_automated_tasks", "automated_tasks_automated_tasks"
  add_foreign_key "scheduled_automated_tasks", "custom_form_values"
  add_foreign_key "scheduled_automated_tasks", "help_tickets"
  add_foreign_key "scheduled_reports", "analytics_report_templates"
  add_foreign_key "scheduled_reports", "reports"
  add_foreign_key "scheduled_slas", "help_tickets"
  add_foreign_key "scheduled_slas", "sla_details"
  add_foreign_key "scheduled_slas", "sla_emails"
  add_foreign_key "scheduled_task_notifications", "scheduled_tasks"
  add_foreign_key "scheduled_task_recurrences", "scheduled_tasks"
  add_foreign_key "scheduled_tasks", "companies"
  add_foreign_key "scheduled_tasks", "help_ticket_comments"
  add_foreign_key "scheduled_tasks", "workspaces"
  add_foreign_key "sla_conditions", "sla_policies"
  add_foreign_key "sla_details", "sla_policies"
  add_foreign_key "sla_emails", "sla_policies"
  add_foreign_key "sla_policies", "companies"
  add_foreign_key "sla_policies", "custom_forms"
  add_foreign_key "sla_schedules", "help_tickets"
  add_foreign_key "sla_schedules", "sla_details"
  add_foreign_key "sla_schedules", "sla_emails"
  add_foreign_key "slack_configs", "companies"
  add_foreign_key "slack_configs", "slack_configs", column: "authorized_config_id"
  add_foreign_key "slack_configs", "workspaces"
  add_foreign_key "snippets", "companies"
  add_foreign_key "snippets", "msp_templates_snippets"
  add_foreign_key "snippets", "workspaces"
  add_foreign_key "sophos_configs", "companies"
  add_foreign_key "sophos_configs", "company_users"
  add_foreign_key "sophos_tenants", "sophos_configs"
  add_foreign_key "stripe_transactions", "companies"
  add_foreign_key "stripe_transactions", "subscriptions"
  add_foreign_key "subscription_activities", "companies"
  add_foreign_key "subscription_activities", "subscriptions"
  add_foreign_key "subscriptions", "companies"
  add_foreign_key "subscriptions", "company_users", column: "subscriber_id"
  add_foreign_key "subscriptions", "subscription_plans"
  add_foreign_key "system_details", "discovered_assets"
  add_foreign_key "system_details", "managed_assets"
  add_foreign_key "task_assignees", "contributors"
  add_foreign_key "task_assignees", "project_tasks"
  add_foreign_key "task_criteria", "automated_tasks_automated_tasks", column: "automated_task_id"
  add_foreign_key "task_criteria", "companies"
  add_foreign_key "tasks", "task_checklists"
  add_foreign_key "telecom_providers", "contributors", column: "creator_id"
  add_foreign_key "telecom_service_locations", "locations"
  add_foreign_key "telecom_service_locations", "telecom_services"
  add_foreign_key "telecom_services", "companies"
  add_foreign_key "telecom_services", "contributors", column: "creator_id"
  add_foreign_key "temporary_company_subscriptions", "companies"
  add_foreign_key "ticket_emails", "custom_forms"
  add_foreign_key "ticket_emails", "workspaces"
  add_foreign_key "ticket_list_columns", "companies"
  add_foreign_key "ticket_list_columns", "users"
  add_foreign_key "ticket_list_columns", "workspaces"
  add_foreign_key "ticket_sessions", "company_users"
  add_foreign_key "ticket_sessions", "help_tickets"
  add_foreign_key "time_spents", "company_users"
  add_foreign_key "time_spents", "help_ticket_comments"
  add_foreign_key "transaction_departments", "departments"
  add_foreign_key "transaction_departments", "general_transactions"
  add_foreign_key "transaction_products_mappings", "companies"
  add_foreign_key "transaction_products_mappings", "products"
  add_foreign_key "ubiquiti_configs", "company_users"
  add_foreign_key "ubiquiti_controller_sites", "integrations_locations"
  add_foreign_key "ubiquiti_controller_sites", "ubiquiti_controllers"
  add_foreign_key "ubiquiti_controllers", "ubiquiti_configs", column: "ubiquiti_configs_id"
  add_foreign_key "user_devices", "users"
  add_foreign_key "user_pinned_companies", "companies"
  add_foreign_key "user_pinned_companies", "users"
  add_foreign_key "valid_email_extensions", "companies"
  add_foreign_key "valid_email_extensions", "workspaces"
  add_foreign_key "vendors", "company_users", column: "primary_internal_contact_id"
  add_foreign_key "vendors", "company_users", column: "secondary_internal_contact_id"
  add_foreign_key "vendors", "contributors", column: "creator_id"
  add_foreign_key "vendors", "locations"
  add_foreign_key "vendors", "products", column: "base_product_id", on_delete: :nullify
  add_foreign_key "workspaces", "companies"
  add_foreign_key "xero_configs", "companies"
  add_foreign_key "xls_import_rows", "xls_imports", column: "xls_imports_id"
  add_foreign_key "xls_import_rows", "xls_sheets"
  add_foreign_key "xls_sheets", "xls_imports", column: "xls_imports_id"
  add_foreign_key "xml_discovery_logs", "discovered_assets"
end
