ActiveAdmin.register Logs::V3IncomingRequestLog, as: 'v3_incoming_request_log' do
  menu parent: 'Logs', label: "V3 Incoming Requests", priority: 17
  breadcrumb do
    ['admin', 'logs']
  end

  filter :company
  filter :source, as: :select
  filter :is_first_request, as: :select
  filter :company_status
  filter :created_at

  actions :index, :show

  index do
    id_column
    column "Company" do |event|
      event.company&.subdomain || "Not present"
    end
    column :company_status
    column :is_first_request
    column :request_details
    column :build_url
    column :source
    column :created_at
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
