class Webhooks::Integrations::Helpdesk::MsTeams::NotificationsController < ApplicationController
  
  def index
    if params[:validationToken]
      render plain: params[:validationToken], status: :ok
    else
      process_notification(request.body.read)
    end
  end

  private

  def process_notification(payload)
    parsed_payload = JSON.parse(payload)
    event = parsed_payload.dig('value', 0)
    if is_lifecycle_event?(event)
      handle_lifecycle_event(event['subscriptionId'])
    else
      handle_change_notification(parsed_payload)
    end
  end

  def is_lifecycle_event?(event)
    event['lifecycleEvent'] == 'reauthorizationRequired'
  end

  def handle_lifecycle_event(subscription_id)
    config = Integrations::MsTeams::Config.find_by(subscription_id: subscription_id, active: true)
    ticket = HelpTicket.find_by_id(config.channel_name[/\d+$/].to_i) if config.present?
    # We will explicitly renew subscription if the ticket status gets changed to 'Open' in custom form value
    if (ticket && ticket.status != 'Closed') 
      graph_client = Integrations::MsTeams::GraphClient.new(ms_teams_config: config)
      response = graph_client.renew_subscription(config.subscription_id)
      config.subscription_expiry_date = response['expirationDateTime']
      config.save
    end
    render json: { status: 'ok' }, status: :ok
  end

  def handle_change_notification(parsed_payload)
    resource = parsed_payload.dig('value', 0, 'resource')
    team_id = resource[/teams\('([^']+)'\)/, 1]
    channel_id = resource[/channels\('([^']+)'\)/, 1]
    message_id = resource[/messages\('([^']+)'\)/, 1]
    CreateMsTeamsCommentWorker.perform_async(team_id, channel_id, message_id)
    render json: { status: 'ok' }, status: :ok
  end
end
