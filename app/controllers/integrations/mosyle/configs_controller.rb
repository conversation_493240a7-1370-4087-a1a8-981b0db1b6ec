class Integrations::Mosyle::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper

  before_action :ensure_config, only: [:destroy, :deactivate]

  def create
    token_detail = mosyle_service.token(auth_params)
    if token_detail['token'].present?
      mosyle_config = Integrations::Mosyle::Config.find_or_initialize_by(company_id: @current_company.id)

      mosyle_config.assign_attributes(
        username: auth_params['username'],
        password: auth_params['password'],
        access_token: auth_params['access_token'],
        refresh_token: token_detail['token'],
        expires_in: token_detail['expires']
      )
      mosyle_config.company_user_id = current_company_user&.id

      if mosyle_config.save
        render json: { message: 'Mosyle configuration successfully saved.' }, status: :ok
      else
        render json: { message: mosyle_config.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      render json: { message: 'Invalid credentials.' }, status: :not_found
    end
  end

  def destroy
    company_integration = @mosyle_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deleting the integration. Please try again.' }, status: :unprocessable_entity
    end
  end

  def deactivate
    if @mosyle_config.company_integration
      @mosyle_config.company_integration.update(active: false, status: false)
      render json: { message: 'Integration deactivated successfully' }, status: :ok
    else
      render json: { message: 'Sorry, there was an error deactivating the integration. Please try again.' }, status: :unprocessable_entity
    end
  end

  private

  def auth_params
    params.require(:mosyle_config).permit(:username, :password, :access_token)
  end

  def mosyle_service
    @mosyle_service ||= Integrations::Mosyle::FetchData.new(@current_company.id)
  end

  def ensure_config
    @mosyle_config ||= Integrations::Mosyle::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: 'Config was not found.' }, status: :not_found }
    end
  end
end
