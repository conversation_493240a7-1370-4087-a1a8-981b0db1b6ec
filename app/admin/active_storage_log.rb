ActiveAdmin.register ActiveStorageLog do
  menu parent: 'Logs', label: "Active Storage Logs", priority: 7
  breadcrumb do
    ['admin', 'logs']
  end

  active_storage_models = ["Logs::AssetDiscoveryLog",
                           "AttachmentUpload",
                           "AzureLicenseReport",
                           "Company",
                           "CompanyAssetType",
                           "ContractAttachment",
                           "CustomFormAttachment",
                           "CustomReport",
                           "EmailFormat",
                           "FeatureRequestAttachment",
                           "FeatureRequestImage",
                           "Invoice",
                           "LibraryDocument",
                           "Location",
                           "ManagedAsset",
                           "ManagedAssetAttachment",
                           "Msp::Templates::Document",
                           "TicketEmailAttachment",
                           "User",
                           "XlsImport"]

  filter :status, as: :select, collection: ActiveStorageLog.statuses
  filter :record_class, as: :check_boxes, collection: active_storage_models, multiple: true

  actions :index, :show

  index do
    column :id
    column :status
    column :record_class

    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
