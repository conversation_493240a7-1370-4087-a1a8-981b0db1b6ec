<template>
  <div class="clearfix row mx-1 mb-1">
    <div class="col mt-5"> 
      <div class="box-inner w-100">
        <email-format :email-format-sub-menu="true" />
      </div>
    </div>
  </div>
</template>

<script>
  import EmailFormat from "../../company/email_format.vue";

  export default {
    components: {
      EmailFormat,
    },
    data() {
      return {
        emailFormatEnabled: null,
        loading: false,
      };
    },
  };
</script>
