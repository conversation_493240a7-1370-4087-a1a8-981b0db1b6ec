ActiveAdmin.register CognitoLog do
  menu parent: 'Logs', priority: 14
  breadcrumb do
    ['admin', 'logs']
  end

  filter :api_type, as: :select
  filter :status, as: :select
  filter :user_email, as: :select

  actions :index, :show, :destroy

  index do
    column :user_email
    column :api_type
    column :status
    column :created_at
    actions
  end

  controller do
    include MultiCompany::GeneralScoping
    
    before_action :verify_super_admin_mfa
  end
end
