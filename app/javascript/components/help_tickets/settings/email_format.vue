<template>
  <div class="clearfix row">
    <sub-menu />
    <div class="col mt-5"> 
      <div class="box-inner w-100">
        <email-format :email-format-sub-menu="true" />
      </div>
    </div>
  </div>
</template>

<script>
  import SubMenu from "./sub_menu.vue";
  import EmailFormat from "../../company/email_format.vue";

  export default {
    components: {
      SubMenu,
      EmailFormat,
    },
    data() {
      return {
        emailFormatEnabled: null,
        loading: false,
      };
    },
  };
</script>
