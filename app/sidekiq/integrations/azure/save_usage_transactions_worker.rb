require 'malloc_trim'

class Integrations::Azure::SaveUsageTransactionsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureCommonMethods

  sidekiq_options queue: 'long_integrations'

  def perform(config_id, subscriptions, vendor_id, product_id, first_time_flag, next_link, current_index=0)
    @starting_time = Time.current
    set_read_replica_db do
      @azure_config = Integrations::Azure::Config.find_by(id: config_id)
      @company = @azure_config.company
      @company_integration = @azure_config.company_integration
    end
    @first_time_flag = first_time_flag

    if subscriptions.any?
      @company_integration_id = @company_integration.id
      @vendor_id = vendor_id
      @product_id = product_id

      current_subscription = subscriptions[current_index]
      refresh_access_token
      response, next_url = azure_client.get_subscription_usage(current_subscription['id'], next_link)
      pusher(@azure_config, true, 0.4)
      save_usage_transactions(response) if response.present?

      if ((next_url.nil? || next_url.include?('None')) && current_index === subscriptions.size - 1)
        finalize_sync
      elsif (next_url.present?)
        Integrations::Azure::SaveUsageTransactionsWorker.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, next_url, current_index)
      else
        Integrations::Azure::SaveUsageTransactionsWorker.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, nil, current_index + 1)
      end
      intg_duration_analysis(@company.id, @first_time_flag)
    else
      finalize_sync
      intg_duration_analysis(@company.id, @first_time_flag)
    end
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message,
                                           user_error_message: e.message)

    @company_integration.save!

    error_messages = [
      "invalid_grant",
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "The security token included in the request is invalid.",
      "Authentication failed."
    ]

    if !@first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end

    azure_client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(@azure_config, false, 0) if @first_time_flag
  ensure
    GC.start
    MallocTrim.trim
  end

  def save_usage_transactions(response)
    response.each do |usage|
      if usage.present?
        next unless cost_present_or_not_zero?(usage)

        set_read_replica_db do
          @cloud_usage_transaction = @company.cloud_usage_transactions.find_or_initialize_by(
                                                                      transaction_date: usage["properties"]["date"],
                                                                      name: usage["properties"]["consumedService"],
                                                                      company_integration_id: @company_integration_id,
                                                                      is_manual: false,
                                                                      amount: transaction_amount(usage))
        end

        @cloud_usage_transaction.assign_attributes(vendor_id: @vendor_id,
                                                   product_id: @product_id)

        @cloud_usage_transaction.save!
      end
    end
  end

  def cost_present_or_not_zero?(usage)
    (usage["properties"]["costInBillingCurrency"] && usage["properties"]["costInBillingCurrency"] != 0) || 
    (usage["properties"]["cost"] && usage["properties"]["cost"] != 0)
  end

  def transaction_amount(usage)
    usage["properties"]["costInBillingCurrency"].presence || usage["properties"]["cost"]
  end

  def azure_client
    @azure_service ||= Integrations::Azure::FetchData.new(@company.id, @first_time_flag)
  end

  def finalize_sync
    pusher(@azure_config, true, 0.8)

    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil)
    @company_integration.save!

    pusher(@azure_config, true, 1) if @first_time_flag
  end
end
