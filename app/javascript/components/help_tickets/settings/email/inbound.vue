<template>
  <section>
    <div class="box box--block box--natural-height p-4 mb-5">
      <div class="box__inner">
        <h5 class="font-weight-normal mb-1">
          Inbound Emails
        </h5>
        <p class="text-secondary mb-3 not-as-small">
          Set up and manage your Help Desk forms' inbound email addresses.
          <a 
            href="#"
            class="smallest px-1 rounded ml-3 text-muted bg-light d-inline-flex align-items-center"
            @click.stop.prevent="$refs.infoModal.open"
          >
            <i class="genuicon-info-circled text-info d-flex py-0 5 mr-1" />
            Learn More
          </a>
        </p>
        <table class="not-as-small table table--spaced mb-0">
          <thead>
            <tr>
              <th>Form Name</th>
              <th>Inbound Email Address</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="form in helpdeskForms"
              :key="`form-email-${form.id}`"
              class="box--with-hover" 
              @click="openInboundEmailModal(form)"
            >
              <td><strong>{{ form.formName }}</strong></td>
              <td class="py-0 align-middle">
                <span v-if="activeEmailAddress(form)">
                  <span v-if="activeEmailName(form)">
                    {{ activeEmailName(form) }} &lt;{{ activeEmailAddress(form) }}&gt;
                  </span>
                  <span v-else>
                    {{ activeEmailAddress(form) }}
                  </span>
                </span>
                <span v-else>
                  <em class="text-muted">No inbound email set up</em>
                </span>
                <a 
                  href="#"
                  class="btn btn-round not-full-width text-muted"
                  @click.prevent=""
                >
                  <i class="nulodgicon-edit not-as-small" />
                </a>
              </td>
              <td>
                <span 
                  v-if="form.customEmail.email && form.customEmail.verified"
                  class="text-success true-small"
                >
                  <span class="btn btn-round btn-xs btn-success no-shadow unclickable not-full-width align-baseline mr-1">
                    <i class="genuicon-checkmark align-middle" />
                  </span>
                  Verified
                </span>
                <span 
                  v-else-if="form.customEmail.email"
                  class="text-danger true-small"
                >
                  <span class="btn btn-round btn-xs btn-danger no-shadow unclickable not-full-width align-baseline mr-1">
                    <i class="genuicon-exclamation align-middle" />
                  </span>
                  Please verify your email
                  <a 
                    href="#"
                    class="ml-1 smallest btn btn-link not-full-width py-1 px-2 mt-n2 mb-n1 align-middle"
                    @click.stop.prevent="resendConfirmationEmail(form.customFormId)"
                  >
                    (Resend)
                  </a>
                </span>
                <a
                  v-else
                  href="#"
                  class="true-small"
                >
                  Set up a custom email
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <inbound-info ref="infoModal" />
    <inbound-modal 
      ref="emailModal" 
      :selected-form="selectedForm"
      @modal-close="onModalClose"
    />

  </section>
</template>

<script>
import http from "common/http";
import inboundEmailMixins from "mixins/inbound_email_settings";
import permissionsHelper from 'mixins/permissions_helper';
import InboundModal from "./inbound_modal.vue";
import InboundInfo from "./inbound_info.vue";

export default {
  components: {
    InboundInfo,
    InboundModal,
  },
  mixins: [inboundEmailMixins, permissionsHelper],
  data() {
    return {
      helpdeskForms: null,
      selectedForm: null,
    };
  },
  methods: {
    onWorkspaceChange() {
      this.getHelpdeskCustomForms();
    },
    getHelpdeskCustomForms() {
      http.get('/custom_forms/helpdesk_custom_forms.json').then(response => {
        const { data } = response;
        const forms = data.map(d => {
          const form = d;
          if (!form.customEmail) {
            form.customEmail = { name: "", email: "" };
          }
          return form;
        });
        this.helpdeskForms = forms;
      });
    },
    openInboundEmailModal(form) {
      this.selectedForm = form;
      this.$nextTick(() => {
        this.$refs.emailModal.open();
      });
    },
    onModalClose() {
      this.selectedForm = null;
      this.getHelpdeskCustomForms();
    },
  },
};
</script>
