class SubscriptionsController < ModulesController
  set_privilege_name "CompanyUser"
  skip_before_action :ensure_free_trial_or_subscribed
  before_action :include_stripe, only: [:show]
  before_action :check_blocked_company, only: [:create, :update]

  def index
    @subscriptions = scoped_company.subscriptions
    subscription_objs = []
    if @subscriptions.present?
      @subscriptions.each do |subscription|
        subscription_data = {
          subscription_plan: subscription.subscription_plan,
          subscription_expiring_days_left: subscription_days_left(subscription),
          expired: subscription.expired?,
          expiring: subscription.expiring?,
          insolvent: subscription.insolvent?
        }

        subscription_data[:renewal_date] = renewal_date(subscription) if params[:require_renewel_date] == "true"

        subscription_objs << subscription.as_json.merge(subscription_data)
      end
    end
    render json: { subscriptions: subscription_objs,
                   timezone: timezone,
                   created_at: scoped_company.created_at,
                   free_trial_days_left: scoped_company.free_trial_days_left,
                 }, status: :ok
  end

  def create
    subscription_options = params[:subscription_types]
    begin
      subscription_options.each do |option|
        status, result, incomplete_subscription = create_stripe_subscription(option)
        if status.equal?(:ok)
          subscription = Subscription.create(
            company: scoped_company,
            subscription_plan_id: subscription_plan(option).id,
            status: "active",
            stripe_subscription_id: result.id,
            start_date: Date.today,
            subscriber_id: scoped_company_user.id,
            email: customer.email,
            module_type: subscription_module_type(option)
          )
          CompleteSubscriptionSetupWorker.perform_async(scoped_company.id, scoped_company_user.id, subscription.id)
          mixpanel_service.track(current_user&.guid, 'Subscription Created', { subscription_plan_id: subscription_plan(option).id, start_date: Date.today, current_company: scoped_company.name })
        else
          mixpanel_service.track(current_user&.guid, 'Subscription Failed')
          raise "Unable to create subscription due to insufficient funds" if incomplete_subscription['status'] == 'incomplete'
          next
        end
      end
      cancel_legacy_subscription unless scoped_company.is_legacy_company # if a company has old legacy plan in active state, cancel it
      word = render_word(subscription_options.count)
      render json: { message: "#{word} created successfully." }, status: :ok
    rescue => e
      render json: { message: "An error occurred while subscribing to selected plans: #{e.message}" }, status: :unprocessable_entity
    end
  end

  def update
    begin
      if params[:refund_and_upgrade]
        handle_refund_case
      else
        insolvent_subscriptions = params[:filtered_plans]&.select { |plan| plan[:status] == 'insolvent' } || []
        insolvent_subscriptions.each do |insolvent_subscription|
          stripe_id = insolvent_subscription[:stripe_subscription_id]
          subscription = Stripe::Subscription.retrieve(stripe_id)
          if subscription.present? && subscription["ended_at"].present? && subscription["status"] != "incomplete_expired"
            params[:subscriptions_ids] |= [insolvent_subscription.id]
          end
        end
        params[:subscriptions_ids].each do |id|
          @subscription = Subscription.find(id)
          next unless @subscription

          selected_plan_name = @subscription.subscription_plan.name
          unique_emails = check_unique_emails

          if params[:upgrading]
            if is_stripe_subscription_id_nil?
              status, result = create_stripe_subscription(selected_plan_name)
              handle_upgrade(result, selected_plan_name) if status.equal?(:ok)
            else
              status, result = upgrade_stripe_subscription(selected_plan_name)
              if status.equal?(:ok) && retrieve_stripe_subscription(@subscription).pending_update.nil?
                handle_upgrade(result, selected_plan_name)
              else
                raise "Unable to upgrade subscription due to insufficient funds"
              end
            end
          elsif params[:canceling]
            status, result = cancel_stripe_subscription unless is_stripe_subscription_id_nil? 
            handle_cancel if status.equal?(:ok) || is_stripe_subscription_id_nil?
          elsif params[:secondary_emails].present? && unique_emails.present?
            handle_secondary_emails(unique_emails)
            status = :ok
          elsif params[:deleted_email].present?
            handle_deleted_email
            status = :ok
          else
            if is_stripe_subscription_id_nil?
              status, result = create_stripe_subscription(selected_plan_name)
            else
              status, result = restart_stripe_subscription(selected_plan_name)
            end
            handle_restart(result, selected_plan_name) if status.equal?(:ok)
          end

          if !status.equal?(:ok) && !is_stripe_subscription_id_nil?
            mixpanel_service.track(current_user&.guid, 'Subscription Failed')
            next
          end
        end
      end
      word = render_word(params[:subscriptions_ids].count)
      render json: { message: "#{word} updated successfully." }, status: :ok
    rescue => e
      render json: { message: "An error occurred while updating subscriptions: #{e.message}" }, status: :unprocessable_entity
    end
  end

  def show
    respond_to do |format|
      format.html {  }
      format.json { render json: { company: scoped_company }}
    end
  end

  private

  def is_stripe_subscription_id_nil?
    @subscription.stripe_subscription_id.nil?
  end

  def render_word(count)
    count > 1 ? 'Subscriptions' : 'Subscription'
  end

  def handle_upgrade(result, selected_plan_name)
    @subscription.update(
      status: "active",
      stripe_subscription_id: result.id,
      end_date: nil,
      start_date: Time.at(result["current_period_start"]).in_time_zone(scoped_company.timezone).to_date,
      subscription_plan_id: subscription_plan(selected_plan_name).id,
      module_type: subscription_module_type(selected_plan_name)
    )
    @subscription.subscription_activities.create(owner_id: scoped_company_user.id, company_id: scoped_company.id, activity_type: "subscription", activity_action: "upgraded")
  end

  def handle_cancel
    @subscription.update(status: "canceled", end_date: end_date)
    send_marketing_mailer if Rails.env.production?
    @subscription.subscription_activities.create(owner_id: scoped_company_user.id, company_id: scoped_company.id, activity_type: "subscription", activity_action: "deleted")
    mixpanel_service.track(current_user&.guid, 'Subscription Canceled', { active: @subscription.active?, end_date: end_date, current_company: scoped_company.name })
  end

  def handle_secondary_emails(unique_emails)
    if params[:secondary_emails].first['id'].nil?
      unless check_valid_email_format
        return render json: "Invalid email format", status: :bad_request
      end
    end

    if @subscription.secondary_emails.present?
      scoped_company.subscriptions.update_all(secondary_emails: @subscription.secondary_emails.concat(unique_emails).uniq)
      status = :ok
    else
      scoped_company.subscriptions.update_all(secondary_emails: params[:secondary_emails])
      status = :ok
    end
  end

  def handle_deleted_email
    remaining_secondary_emails = @subscription.secondary_emails.reject { |sec_email| sec_email['email'] == params[:deleted_email] }
    scoped_company.subscriptions.update_all(secondary_emails: remaining_secondary_emails)
  end

  def handle_restart(result, selected_plan_name)
    @subscription.end_date ||= Date.today
    subscription_start_date = @subscription.end_date <= Date.today ? Date.today : @subscription.end_date
    @subscription.update(
      status: "active",
      stripe_subscription_id: result.id,
      end_date: nil,
      start_date: subscription_start_date,
      subscription_plan_id: subscription_plan(selected_plan_name).id,
      module_type: subscription_module_type(selected_plan_name)
    )
    @subscription.subscription_activities.create(owner_id: scoped_company_user.id, company_id: scoped_company.id, activity_type: "subscription", activity_action: "created")
    transactions_duration = Date.today.to_time - scoped_company.created_at.to_time
    FetchTransactionsWorker.perform_async(scoped_company.id, transactions_duration)
    mixpanel_service.track(current_user&.guid, 'Subscription Restarted', { active: @subscription.active?, transactions_duration: transactions_duration, current_company: scoped_company.name })
  end

  def handle_create(result, selected_plan_name)
    subscription = Subscription.create(
      company: scoped_company,
      subscription_plan_id: subscription_plan(selected_plan_name).id,
      status: "active",
      stripe_subscription_id: result.id,
      start_date: Date.today,
      subscriber_id: scoped_company_user.id,
      email: customer.email,
      module_type: subscription_module_type(selected_plan_name)
    )
  end

  def handle_refund_case
    first_iteration = true
    params[:subscriptions_ids].each do |id|
      @subscription = Subscription.find_by(id: id)
      if first_iteration
        cancel_and_refund
        # create a new full platform subscription
        creation_status, creation_result = create_stripe_subscription(params[:plan_name])
        handle_create(creation_result, params[:plan_name]) if creation_status.equal?(:ok)
        first_iteration = false
      else
        cancel_and_refund
      end
    end
  end

  def cancel_and_refund
    # cancel subscription on stripe and refund the amount left on canceled plan
    status, result = immediately_cancel_stripe_subscription unless is_stripe_subscription_id_nil?
    if status.equal?(:ok)
      create_stripe_refund
      @subscription.destroy
    end
  end

  def cancel_legacy_subscription
    @subscription = scoped_company.subscriptions.joins(:subscription_plan)
                                                .where(status: "active", subscription_plan: { name: ['Basic Plan', 'Yearly Plan'] }).last
    if @subscription&.stripe_subscription_id.present?
      status, result = cancel_stripe_subscription
      handle_cancel if status.equal?(:ok)
    end
  end

  def check_unique_emails
    unique_emails = []
    return true if @subscription.secondary_emails.blank?
    params[:secondary_emails]&.each do |sec_email|
      if !(@subscription.email.include?(sec_email['email']) || @subscription.secondary_emails.include?(sec_email['email']))
        unique_emails << sec_email
      end
    end
    unique_emails
  end

  def check_valid_email_format
    return (params[:secondary_emails].first['email'] =~ (/\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\Z/i)).present?
  end

  def create_stripe_subscription(option)
    StripeSubscriptionService.new({ customer: customer, current_user: current_user, subscription_plan: subscription_plan(option) }).create_subscription
  end

  def cancel_stripe_subscription
    StripeSubscriptionService.new({ customer: customer, current_user: current_user, subscription: @subscription }).cancel_subscription
  end

  def restart_stripe_subscription(selected_plan_name)
    StripeSubscriptionService.new({ subscription: @subscription, customer: customer, current_user: current_user, subscription_plan: subscription_plan(selected_plan_name) }).restart_subscription
  end

  def upgrade_stripe_subscription(selected_plan_name)
    StripeSubscriptionService.new({ subscription: @subscription, current_user: current_user, subscription_plan: subscription_plan(selected_plan_name) }).upgrade_subscription
  end

  def retrieve_stripe_subscription(subscription)
    StripeSubscriptionService.new({ subscription: subscription }).retrieve_subscription
  end

  def create_stripe_refund
    StripeSubscriptionService.new({ subscription: @subscription }).create_refund
  end

  def immediately_cancel_stripe_subscription
    StripeSubscriptionService.new({ customer: customer, current_user: current_user, subscription: @subscription }).immediately_cancel_subscription
  end

  def renewal_date(subscription)
    if subscription.stripe_subscription_id
      stripe_subscription = retrieve_stripe_subscription(subscription)
      Time.at(stripe_subscription["current_period_end"]).to_date if stripe_subscription.present?
    elsif subscription.monthly?
      day = subscription.start_date.day
      if Date.today >= subscription.start_date
        Date.new(Date.today.year, Date.today.month, day) + 1.month
      else
        Date.new(Date.today.year, Date.today.month, day - 1)
      end
    else
      diff = Date.today.year - subscription.start_date.year
      if subscription.start_date + diff.years <= Date.today
        subscription.start_date + (diff + 1).years
      else
        subscription.start_date + diff.years
      end
    end
  end

  def end_date
    if @subscription.stripe_subscription_id
      stripe_subscription = retrieve_stripe_subscription(@subscription)
      Time.at(stripe_subscription["cancel_at"]).to_date
    elsif @subscription.monthly?
      day = @subscription.start_date.day
      if Date.today >= @subscription.start_date
        Date.new(Date.today.year, Date.today.month, day) + 1.month
      else
        Date.new(Date.today.year, Date.today.month, day - 1)
      end
    else
      diff = Date.today.year - @subscription.start_date.year
      if @subscription.start_date + diff.years <= Date.today
        @subscription.start_date + (diff + 1).years
      else
        @subscription.start_date + diff.years
      end
    end
  end

  def timezone
    current_user.timezone || scoped_company.timezone
  end

  def admins
    scoped_company.admin_company_users
  end

  def send_marketing_mailer
    SubscriptionMailer.inform_marketing_subscription_canceled(admins, scoped_company, current_user).deliver_now!
  end

  def customer
    @customer ||= Stripe::Customer.retrieve(scoped_company.stripe_id)
  end

  def subscription_plan(option)
    if params[:is_legacy_company]
      if params[:upgrading]
        SubscriptionPlan.find_by(name: "Yearly Plan")
      elsif params[:restarting]
        SubscriptionPlan.find_by(name: params[:selected_plan][0])
      else
        SubscriptionPlan.find_by(name: option)
      end
    elsif params[:restarting]
      find_subscription_plan(option)
    elsif params[:upgrading]
      words = option.split(' ')
      words.pop
      words.push('Yearly')
      plan_name = words.join(' ')
      SubscriptionPlan.find_by(name: plan_name)
    else
      words = option.split
      words[-1] = words[-1].capitalize
      plan_name = words.join(' ')
      SubscriptionPlan.find_by(name: plan_name)
    end
  end

  def subscription_module_type(option)
    if params[:is_legacy_company] || params[:refund_and_upgrade]
      "full_platform"
    else
      words = option.split(' ')
      words.pop
      module_name = words.join(' ')
      module_name.downcase.gsub(" ", "_")
    end
  end

  def subscription_days_left(subscription)
    return nil unless subscription.expiring?
    return nil unless subscription.end_date
    days_left = subscription.end_date&.mjd - Date.today.mjd
    [days_left, 0].max
  end

  def find_subscription_plan(option)
    subscription_details = params[:subscription_details].as_json
    subscription_details.each do |detail|
      if detail["id"] == @subscription.id
        return SubscriptionPlan.find_by(name: detail["scenario"])
      end
    end
  end

  def check_blocked_company
    if GlobalEmailBlocking.last&.company_ids&.include?(scoped_company.id)
      return render json: { message: "This company is blocked from buying a subscription" }, status: :unprocessable_entity
    end
  end
end
