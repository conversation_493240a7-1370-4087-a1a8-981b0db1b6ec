class CreateMsTeamsTicketWorker
  include Utilities::Domains
  include Sidekiq::Job

  sidekiq_options queue: 'critical'

  def perform(config_id, scoped_company_user_id, help_ticket_data)
    scoped_company_user = CompanyUser.find_by_cache(id: scoped_company_user_id) if scoped_company_user_id.present?
    teams_config = ::Integrations::MsTeams::Config.find_by_id(config_id)
    scoped_company = teams_config.company
    HelpDesk::SimpleEntity.new('helpdesk', scoped_company, scoped_company_user, workspace: teams_config.workspace, source: :ms_teams).create_entity(help_ticket_data)
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end
end
