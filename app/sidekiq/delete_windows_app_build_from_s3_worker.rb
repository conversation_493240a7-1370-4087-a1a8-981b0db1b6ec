class DeleteWindowsAppBuildFromS3Worker
  include Sidekiq::Worker
  sidekiq_options queue: 'high_intensity_schedule'

  def perform
    s3 = Aws::S3::Resource.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )

    bucket = s3.bucket(Rails.application.credentials.aws[:s3][:bucket])
    apps_data = bucket.objects(delimiter: '', start_after: 'apps/')
    expired_apps = []
    apps_info = [
      {
        name: "WindowsAgent.Setup.msi",
        date: WindowScript.where(name: "agent").order(:created_at).last.created_at.to_date
      },
      {
        name: "NetworkDiscovery.Setup.msi",
        date: WindowScript.where(name: "network_discovery").order(:created_at).last.created_at.to_date
      },
      {
        name: "SelfDiscovery.exe",
        date: WindowScript.where(name: "self_on_boarding").order(:created_at).last.created_at.to_date
      }
    ]

    apps_data.each do |aws_app|
      aws_app_name = aws_app.key.split("/").last
      app_info = apps_info.find { |ai| ai[:name] === aws_app_name }
      if app_info && aws_app.last_modified.to_date <= app_info[:date] - 1
        expired_apps << { key: aws_app.key }
      end
    end

    bucket.delete_objects({ delete: { objects: expired_apps } })
  end
end
