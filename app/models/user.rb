class User < ActiveRecord::Base
  BACKGROUND_COLORS = [
    '#F44336', '#FF4081', '#9C27B0', '#673AB7',
    '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4', '#009688',
    '#4CAF50', '#8BC34A', '#CDDC39', '#FFC107',
    '#FF9800', '#FF5722', '#795548', '#9E9E9E', '#607D8B'
  ]
  MONITORING_COMMON_ATTRIBUTES = ['guid', 'email', 'first_name', 'last_name', 'avatar_thumb_url', 'timezone']
  USER_ACTIVITY_ATTRIBUTES = ["first_name", "last_name", "email", "temp_email"].freeze

  include PgSearch::Model

  pg_search_scope :search_text,
                  :against => [:first_name, :last_name, :email],
                  :using => { :tsearch =>
                    {
                      :prefix => true,
                      :any_word => true
                    }
                  }

  # Dependencies
  include Validations
  include DeleteAutomatedTaskCacheKey
  include HandleCompanyCacheKeys
  extend HandleCompanyCacheKeys

  DEFAULT_PASSWORD = "newuser@123".freeze
  # Include default devise modules.
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :trackable,
         :omniauthable, :invitable, :timeoutable

  #ActiveRecord::Callbacks
  after_commit :sync_monitoring_remote_user, on: [:update], if: :valid_to_sync?
  after_update :delete_task_recipients_keys, if: -> {  previous_changes[:email] }
  after_update -> { delete_options_cache_keys('show_company_user') }, if: -> { saved_changes[:email].present? }
  after_update :delete_company_user_cache_keys
  after_update :delete_company_user_find_cache_keys, unless: -> { super_admin? }
  after_update :delete_user_cache_keys
  after_update :delete_contributor_company_user_cache_keys, if: :remove_contributor_cache_keys?
  before_save :create_guid
  before_save :set_empty_first_name
  before_save :convert_email_to_downcase
  after_destroy :destroy_user_from_cognito
  after_destroy :delete_monitoring_remote_user
  after_destroy :delete_user_cache_keys
  before_validation :strip_blanks_and_downcase
  after_commit :create_user_activity, on: [:update]
  after_commit :user_email_change_log, on: [:update], if: -> {  previous_changes[:email] }
  before_update :handle_email_change, if: :will_save_change_to_email?
  after_save :delete_user_cache_keys

  VALIDATIONS = [:validates_presence_of, :validates_uniqueness_of, :validates_format_of,
                 :validates_confirmation_of, :validates_length_of].freeze

  scope :archived, -> { where(archived: true) }
  scope :super_admins, -> { where(super_admin: true) }
  has_many :user_accesses

  has_many :user_devices, dependent: :destroy
  has_many :company_users, dependent: :destroy
  has_many :companies, through: :company_users
  has_many :user_pinned_companies, dependent: :destroy
  has_many :pinned_companies, through: :user_pinned_companies, source: :company
  has_many :user_locations
  has_many :referrals, foreign_key: :referrer_id, dependent: :nullify
  has_many :ticket_list_columns, dependent: :destroy
  has_many :super_admin_logs, dependent: :nullify, class_name: 'Logs::SuperAdminLog'
  has_many :cognito_logs, dependent: :nullify

  accepts_nested_attributes_for :company_users, allow_destroy: true

  validates_associated :company_users, on: :create, unless: Proc.new{ |user| user.skip_validations }

  validates :password, presence: true, length: {minimum: 8, maximum: 120},
                       format: { with: /[\S]+.*[\S]+/, message: "should not contain leading and trailing spaces." }, on: :create,
                       unless: Proc.new{ |user| user.skip_validations }
  validates :first_name, presence: true, on: :create, length: { maximum: 100 }
  validates :last_name, presence: true, on: :create, length: { maximum: 100 }
  validate  :full_name_validation, on: :create
  validates :password, confirmation: true, length: {minimum: 8, maximum: 120}, on: :update, unless: lambda{ |user| user.password.blank? }, allow_blank: true

  validates :guid, uniqueness: true

  after_update :filter_updation, if: -> {  previous_changes[:first_name] ||  previous_changes[:last_name]}

  validates_presence_of :email
  validates :email, uniqueness: { case_sensitive: false }
  validate :email_format_validation
  validates_with ::DomainValidator, unless: Proc.new{ |user| user.skip_validations }

  enum sso_user_source: { manual: 0, google: 1,  microsoft: 2 }

  attr_accessor :skip_validations, :domain, :full_name, :activity_owner_id, :activity_company_id, :is_uninvited, :email_domain_cache

  after_update_commit :update_subscription_secondary_email, if: -> {  previous_changes[:email] }

  def authenticatable_salt
    # On every request to the server, cookies set by devise are sent along with the request
    # Using that cookies devise decide if the user is logged in or not.

    # Now when the password changes, the value of session_token in the database
    # will change. Devise logout user and delete the cookies.
    return super unless session_token

    "#{super}#{session_token}"
  end

  def self.find_by_cache(params = nil)
    return find_by(params) unless self.is_cache_enabled?('user_find_by')

    params = params.transform_values { |param| param.is_a?(String) ? param : param&.to_s }
    cache_key = "user_find_by_#{params}".gsub(/[\\"]/, "'")
    valid_single_key = params.keys.count == 1 && [:id, :email, :guid].include?(params.keys.first)
    if valid_single_key
      Rails.cache.fetch(cache_key, expires_in: 8.hours, skip_nil: true) do
        find_by(params)
      end
    else
      find_by(params)
    end
  end

  def invalidate_all_sessions!
    update_attribute(:session_token, SecureRandom.hex)
  end

  def filter_updation
    company_user = CompanyUser.find_by_cache(user_id: self.id, company_id: activity_company_id)
    company_user.handle_name_change if company_user.present?
  end

  def as_json(options = nil)
    my_hash = super(options)
    my_hash = my_hash.merge('full_name' => self.full_name)
    my_hash
  end

  def self.find_for_authentication(warden_conditions)
    where(:email => warden_conditions[:email]).joins(:company_users).where('company_users.company_id = ?', warden_conditions[:company_id]).first
  end

  def full_name_validation
    if full_name && full_name.split(" ").length < 2
      errors.add(:full_name, "must have first and last name")
      errors.delete(:first_name)
    end
  end

  def create_user_activity
    changes = saved_changes.except("id", "created_at", "updated_at")
    changes.each do |key, value|
      if USER_ACTIVITY_ATTRIBUTES.include?(key)
        activity_params = get_activity_params(key, value, changes)

        self.company_users.each do |cu|
          if cu.company_id != self.activity_company_id
            company_admin = cu.company.admins.group_members.first
            activity_params[:owner_id] = company_admin.company_user_id
          else
            activity_params[:owner_id] = self.activity_owner_id
          end
          cu.activities.create(activity_params) if activity_params.present?
        end
      end
    end
  end

  def user_email_change_log
    company_user = CompanyUser.find_by_cache(user_id: self.id, company_id: activity_company_id)
    details = {
      name: self.full_name,
      previous_email:  previous_changes["email"][0],
      new_email:  previous_changes["email"][1],
      user_id: self.id,
      company_user_id: company_user.id,
      company: { name: company_user.company.name },
      is_uninvited_user: self.is_uninvited,
    }
    log_params = {
      api_type: "email updated - #{previous_changes['email'][1]}",
      class_name: 'User',
      company_id: activity_company_id,
      status: 1,
      response: details.to_s,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: nil,
      error_message: nil
    }
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end

  # devise_invitable accept_invitation! method overriden
  def accept_invitation!
    self.confirm
    super
  end

  def avatar_color
    BACKGROUND_COLORS[(self.id || 0) % BACKGROUND_COLORS.length]
  end

  def generate_authentication_token!
    begin
      self.auth_token = Devise.friendly_token
    end while self.class.exists?(auth_token: auth_token)
  end

  def has_active_subscription?
    is_active = false
    companies.not_sample.each do |company|
      if company.allow_access?
        is_active = true
        break
      end
    end
    is_active
  end

  def self.generate_authentication_token_global!
    token = ""
    begin
      token = Devise.friendly_token
    end while User.exists?(auth_token: token)
    token
  end

  def staff?
    true
  end

  def full_name
    if first_name.present?
      "#{first_name} #{last_name}"
    else
      last_name
    end
  end

  def full_name=(value)
    if value == nil
      self.first_name = nil
      self.last_name = nil
    else
      name = value.split(" ")
      if name.length > 1
        self.first_name = name.shift
      else
        self.first_name = ""
      end
      self.last_name = name.join(" ")
    end
  end

  def initials
    if first_name.present?
      "#{first_name.first}#{last_name.first}"
    elsif first_name.present?
      last_name.first
    else
      email.first
    end
  end

  def create_guid
    self.guid ||= SecureRandom.uuid
  end

  def set_empty_first_name
    self.first_name ||= ""
  end

  def convert_email_to_downcase
    self.email = self.email&.downcase
  end

  def admin?(company_id)
    company_users.where(company_id: company_id).first.try(:is_admin?) || super_admin?
  end

  def self.search term:, company: nil, page: 1, per_page: 25
    term_to_search = term.try(:downcase)
    if company
      users = company.try(:users)
    else
      users = User
    end
    data = users.select(:id, :first_name, :last_name).
                  order(Arel.sql("LOWER(first_name)")).
                  where('lower(first_name) like ? OR lower(last_name) like ?',
                        "%#{term_to_search}%", "%#{term_to_search}%").
                  page(page).
                  per_page(per_page)

    total_count = data.total_entries

    results = data.map do |item| {
        id: item.id,
        text: item.full_name
      }
    end
    {results: results, size: total_count}
  end

  def request_permission(requested_for, company)
    UserMailer.request_permission(self, company, requested_for).deliver_now! if !company.is_sample_company?
  end

  def referred_by
    Referral.find_by(referred_id: self.id)&.referrer&.presence
  end

  def login_code
    crypt = ActiveSupport::MessageEncryptor.new(Rails.application.secrets.secret_key_base[0..31])
    encrypted_data = crypt.encrypt_and_sign(guid)
  end

  def self.generic_email?(email)
    generic_emails = ["gmail.com", "hotmail.com", "outlook.com", "live.com", "yahoo.com", "aol.com", "inbox.com", "test.com", "sample.com", "example.com", "email.com"]
    disposable_emails = ["protonmail.com", "yopmail.com", "jourrapide.com", "dayrep.com", "teleworm.us", "sharklasers.com", "guerrillamail.info", "10mail.org", "10mail.tk", "besttempmail.com", "cmail.club", "chogmail.com", "cmail.net", "cmail.org", "getairmail.com", "getmails.eu", "getonemail.com", "giantmail.de", "mailbiz.biz", "mailblocks.com", "mailbucket.org", "mailcat.biz", "mailcatch.com", "mailde.de", "mailde.info", "maildrop.cc", "10minutemail.net", "10minemail.com", "temp-mail.org", "e4ward.com", "guerrillamail.com", "mohmal.com", "throwawaymail.com", "getnada.com", "yopmail.com", "emailondeck.com", "mail21.cc", "mail2rss.org", "mail333.com", "mailbidon.com", "mailbiz.biz", "mailblocks.com", "mailbucket.org", "mailcat.biz", "mailcatch.com", "mailde.de", "mailde.info", "maildrop.cc"]
    generic_emails.include?(email.split('@').pop()) || disposable_emails.include?(email.split('@').pop())
  end

  def companies_with_access_granted
    self.companies.includes(:subscriptions, :logo_attachment).where("company_users.granted_access_at IS NOT null")
      .select(:id, :name, :subdomain, :is_reseller_company, :is_sample_company)
      .order('is_sample_company, name')
  end

  def create_email_update_log(company, user, from)
    details = {
      name: self.full_name,
      original_email: self.email,
      temp_email: self.temp_email,
      current_user_id: self.id,
      company_user_id: CompanyUser.find_by_cache(user_id: self.id, company_id: company.id).id,
      updated_by: { name: user.full_name, email: user.email , company_user_id:  CompanyUser.find_by_cache(user_id: user.id, company_id: company.id).id, user_id: user.id},
      company: { name: company.name },
      called_from: from,
      is_uninvited_user: self.is_uninvited,
    }
    log_params = {
      api_type: "temp email updated - #{self.temp_email}",
      class_name: 'User',
      company_id: company&.id,
      status: 1,
      response: details.to_s,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: nil,
      error_message: nil
    }
    LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
  end

  protected
  def strip_blanks_and_downcase
    if self.email?
      self.email = self.email.strip.downcase
    end
  end

  def sync_monitoring_remote_user
    company_user = self.company_users.not_sample_company_user.first
    Monitoring::SyncRemoteUserWorker.perform_async(company_user.company_id, company_user.guid, self.id) if company_user.present?
  rescue => e
    Rails.logger.warn e.message
  end

  def update_subscription_secondary_email
    company_users.includes(company: :subscriptions).find_each do |company_user|
      contributor_id = company_user.contributor_id
      company_subscriptions = company_user.company.subscriptions
      next unless company_subscriptions.present?
      # using first subscription's secondary emails only as all subscriptions have same secondary emails
      secondary_emails = company_subscriptions.first.secondary_emails
      next unless secondary_emails.present?

      updated_secondary_emails = secondary_emails.map do |secondary_email|
        if secondary_email['id'] == contributor_id
          { "id": contributor_id, "email": email }
        else
          secondary_email
        end
      end
  
      company_subscriptions.update_all(secondary_emails: updated_secondary_emails)
    end
  end

  private

  def destroy_user_from_cognito
    service = CognitoService.new(self)
    if service.get_users_by_email(self.email).present?
      service.delete_cognito_users_including_external_users(self.email).nil?
    end
  rescue Aws::CognitoIdentityProvider::Errors::ResourceNotFoundException => e
    # if the resource is not found in Cognito, then just move on...
    Rails.logger.error("User #{self.email} not found in Cognito; skipping Cognito destroy...")
  end

  def valid_to_sync?
    self.saved_changes.keys.any? { |key| MONITORING_COMMON_ATTRIBUTES.include? key }
  end

  def remove_contributor_cache_keys?
    self.saved_changes.keys.any? { |key| [:first_name, :last_name, :email].include?(key) }
  end

  def delete_monitoring_remote_user
    Monitoring::DestroyRemoteUserWorker.perform_async(self.guid)
  rescue => e
    Rails.logger.warn e.message
  end

  def get_activity_params(key, value, changes)
    activity_type = key == "temp_email" ? "email_requested" : "updated"
    previous_value = key == "temp_email" ? (changes[:email]&.first || self.email) : value[0]
    current_value = value[1] || value[0]
  
    {
      activity_type: CompanyUserActivity.activity_types[activity_type],
      data: {
        activity_label: key,
        current_value: current_value,
        previous_value: previous_value
      }
    }
  end

  def handle_email_change
    old_email = User.find_by_cache(id: self.id).email
    new_email = self.email

    old_domain = extract_domain(old_email)
    new_domain = extract_domain(new_email)

    company = Company.find_by_cache(id: activity_company_id)
    handle_old_domain_removal(old_domain, company)
    handle_new_domain_addition(new_domain, company)
  end

  def extract_domain(email)
    email.split('@').last.split('.').first if email
  end

  def handle_old_domain_removal(old_domain, company)
    return unless old_domain && company

    users_with_old_domain = company.users.where("email LIKE ?", "%@#{old_domain}")

    if users_with_old_domain.count == 1 && users_with_old_domain.first.id == self.id && old_domain != company.subdomain
      company_domain = company.company_domains.find_by(domain_name: old_domain, company_id: company.id)
      company_domain&.destroy
    end
  end

  def handle_new_domain_addition(new_domain, company)
    return unless new_domain && company

    company_domain = company.company_domains.find_by(domain_name: new_domain)

    company.company_domains.create(domain_name: new_domain, is_registered: new_domain == company.subdomain) unless company_domain
  end
end
