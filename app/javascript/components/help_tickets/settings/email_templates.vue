<template>
  <div class="clearfix row mx-1">
    <div v-if="isLoading">
      <h4 class="mt-5">
        <span class="float-left mr-2">
          Loading templates
        </span>
        <span>
          <sync-loader
            :loading="true"
            class="float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h4>
    </div>
    <div 
      v-else
      class="col mt-5"
    >
      <workspace-settings-banner />
      <template-sub-menu />
      <div v-if="isEndUser || isAgent">
        <div class="box box--with-heading box--flat mt-4">
          <div class="box__heading pt-3 pb-2 rounded-top">
            <div class="d-flex align-items-center">
              <i class="nulodgicon-email mr-2" />
              <h5 class="font-weight-normal mb-0">
                New Ticket Response
              </h5>
            </div>
            <span class="not-as-small text-faded-light">
              The email sent to the ticket creator after the ticket is created.
            </span>
          </div>
          <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
            <email-template
              v-for="emailTemplate in ticketCreatedTemplateList"
              :key="emailTemplate.id"
              :email-template="emailTemplate"
              @open-template="openTemplateModal(emailTemplate, 'ticket created', true)"
              @edit-template="openTemplateModal(emailTemplate, 'ticket created', false)"
              @delete-template="openDeleteTemplateModal(emailTemplate)"
              @preview-template="openPreviewTemplate(emailTemplate)"
            />
            <div class="d-flex align-items-center justify-content-center mb-3 col-sm-12 col-md-6 col-lg-4">
              <div
                class="box box--with-hover box__inner px-4 text-center"
                @click="openTemplateModal(initEmailTemplate, 'ticket created', false)"
              >
                <div class="pt-2 pb-2 text-secondary">
                  <i class="nulodgicon-plus-round mr-1" />
                  Add New Template
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="box box--with-heading box--flat mt-5">
          <div class="box__heading pt-3 pb-2 rounded-top">
            <div class="d-flex align-items-center">
              <i class="nulodgicon-email mr-2" />
              <h5 class="font-weight-normal mb-0">
                Comment Added
              </h5>
            </div>
            <span class="not-as-small text-faded-light">
              The email sent to the ticket creator after a comment is added to their ticket.
            </span>
          </div>
          <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
            <email-template
              v-for="emailTemplate in commentAddedTemplateList"
              :key="emailTemplate.id"
              :email-template="emailTemplate"
              @open-template="openTemplateModal(emailTemplate, 'comment added', true)"
              @edit-template="openTemplateModal(emailTemplate, 'comment added', false)"
              @delete-template="openDeleteTemplateModal(emailTemplate)"
              @preview-template="openPreviewTemplate(emailTemplate)"
            />
            <div class="d-flex align-items-center justify-content-center mb-3 col-sm-12 col-md-6 col-lg-4">
              <div
                class="box box--with-hover box__inner px-4 text-center"
                @click="openTemplateModal(initEmailTemplate, 'comment added', false)"
              >
                <div class="pt-2 pb-2 text-secondary">
                  <i class="nulodgicon-plus-round mr-1" />
                  Add New Template
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="box box--with-heading box--flat mt-5">
          <div class="box__heading pt-3 pb-2 rounded-top">
            <div class="d-flex align-items-center">
              <i class="nulodgicon-email mr-2" />
              <h5 class="font-weight-normal mb-0">
                Ticket Assigned
              </h5>
            </div>
            <span class="not-as-small text-faded-light">
              The email sent to the ticket creator after an agent is assigned to the ticket.
            </span>
          </div>
          <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
            <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
              <email-template
                v-for="emailTemplate in ticketAssignedTemplateList"
                :key="emailTemplate.id"
                :email-template="emailTemplate"
                @open-template="openTemplateModal(emailTemplate, 'ticket assigned', true)"
                @edit-template="openTemplateModal(emailTemplate, 'ticket assigned', false)"
                @delete-template="openDeleteTemplateModal(emailTemplate)"
                @preview-template="openPreviewTemplate(emailTemplate)"
              />
              <div class="d-flex align-items-center justify-content-center mb-3 col-sm-12 col-md-6 col-lg-4">
                <div
                  class="box box--with-hover box__inner px-4 text-center"
                  @click="openTemplateModal(initEmailTemplate, 'ticket assigned', false)"
                >
                  <div class="pt-2 pb-2 text-secondary">
                    <i class="nulodgicon-plus-round mr-1" />
                    Add New Template
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="isSurvey">
        <div class="box box--with-heading box--flat mt-4">
          <div class="box__heading pt-3 pb-2 rounded-top">
            <div class="d-flex align-items-center">
              <i class="nulodgicon-email mr-2" />
              <h5 class="font-weight-normal mb-0">
                Ticket Closed Survey
              </h5>
            </div>
            <span class="not-as-small text-faded-light">
              The email sent to the ticket creator after ticket is closed.
            </span>
          </div>
          <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
            <div class="box__inner bg-themed-light d-flex flex-wrap mt-1">
              <email-template
                v-for="emailTemplate in ticketClosedTemplateList"
                :key="emailTemplate.id"
                :email-template="emailTemplate"
                @open-template="openTemplateModal(emailTemplate, 'closed survey', true)"
                @edit-template="openTemplateModal(emailTemplate, 'closed survey', false)"
                @delete-template="openDeleteTemplateModal(emailTemplate)"
                @preview-template="openPreviewTemplate(emailTemplate)"
              />
              <div class="d-flex align-items-center justify-content-center mb-3 col-sm-12 col-md-6 col-lg-4">
                <div
                  class="box box--with-hover box__inner px-4 text-center"
                  @click="openTemplateModal(initEmailTemplate, 'closed survey', false)"
                >
                  <div class="pt-2 pb-2 text-secondary">
                    <i class="nulodgicon-plus-round mr-1" />
                    Add New Template
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div>
      <sweet-modal
        ref="deleteTemplateModal"
        title="Before you delete this Template..."
        @close="handleClose"
      >
        <template slot="default">
          <div v-if="linkedAT.length">
            <h6>This template is linked with the following Automated Tasks:</h6>
            <p class="mb-1">
              <span
                v-for="(taskID, index) in linkedAT"
                :key="taskID"
              >
                <a
                  :href="`/help_tickets/automated_tasks/${taskID}/edit`" 
                  target="_blank"
                >
                  #{{ taskID }}
                </a>
                <span v-if="index < linkedAT.length - 1">,</span>
              </span>
            </p>
            <p class="mb-0">Please change the selected template in the automated tasks to continue.</p>
          </div>
          <div v-else>
            <h6>
              This template will be permanently deleted. Would you like to delete?
            </h6>
          </div>
        </template>
        <div class="d-flex justify-content-end mt-4">
          <submit-button
            class="mr-2"
            btn-content="Cancel"
            :conditional-class="'btn-secondary btn-sm'"
            @submit="closeDeleteTemplateModal"
          />
          <submit-button
            v-if="showDeleteButton"
            btn-content="Delete"
            :conditional-class="'btn-sm btn-danger'"
            @submit="deleteEmailTemplate()"
          />
        </div>
      </sweet-modal>
    </div>

    <new-template
      v-if="selectedEmailTemplate"
      ref="newTemplateModal"
      :selected-template="selectedEmailTemplate"
      :section="currentSection"
      :disabled="disabled"
      @input="okInput"
      @cancel="resetData"
    />
  </div>
</template>

<script>
  import http from 'common/http';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import SubmitButton from 'components/shared/submit_button.vue';
  import vClickOutside from 'v-click-outside';
  import permissionsHelper from 'mixins/permissions_helper';
  import emailTemplate from './email_template.vue';
  import templateSubMenu from './templates_sub_menu.vue';
  import newTemplate from './new_template.vue';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      templateSubMenu,
      newTemplate,
      SweetModal,
      SubmitButton,
      emailTemplate,
      SyncLoader,
      workspaceSettingsBanner,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        isLoading: true,
        selectedEmailTemplate: null,
        ticketCreatedTemplateList: null,
        commentAddedTemplateList: null,
        ticketAssignedTemplateList: null,
        ticketClosedTemplateList: null,
        disabled: false,
        showDropdown: false,
        currentSection: null,
        deleteTemplate: null,
        initEmailTemplate: {
          templateTitle: '',
          subjectTitle: '',
          emailBody: '',
        },
        linkedAT: [],
        showDeleteButton: false,
      };
    },
    computed: {
      ...mapGetters([
        'emailTemplateType',
      ]),
      isEndUser() {
        return this.emailTemplateType === 'end_users';
      },
      isAgent() {
        return this.emailTemplateType === 'agents';
      },
      isSurvey() {
        return this.emailTemplateType === 'surveys';
      },
    },
    watch: {
      'emailTemplateType': 'fetchEmailTemplates',
    },
    methods: {
      openPreviewTemplate(template) {
        localStorage.setItem('emailPreviewData', JSON.stringify({
          subjectTitle: template.subjectTitle,
          emailBody: template.emailBody,
        }));
        const baseUrl = window.location.origin;
        const fullUrl = `${baseUrl}/preview_email`;
        window.open(fullUrl, '_blank');
      },
      ...mapMutations(['setLoadingStatus']),
      onWorkspaceChange() {
        this.fetchEmailTemplates();
      },
      fetchEmailTemplates() {
        this.setLoadingStatus(true);
        const params = { template_type: this.emailTemplateType, index_page: true };
        http.get('/email_templates.json', { params })
          .then(res => {
            this.ticketCreatedTemplateList = res.data.emailTemplates.filter(x => x.templateName === 'ticket created');
            this.commentAddedTemplateList = res.data.emailTemplates.filter(x => x.templateName === 'comment added');
            this.ticketAssignedTemplateList = res.data.emailTemplates.filter(x => x.templateName === 'ticket assigned');
            this.ticketClosedTemplateList = res.data.emailTemplates.filter(x => x.templateName === 'closed survey');
            this.setLoadingStatus(false);
            this.isLoading = false;
          })
          .catch((error) => {
            this.setLoadingStatus(false);
            this.isLoading = false;
            this.emitError(`Sorry, there was an error fetching templates. ${error.response.data.message}`);
          });
      },
      openTemplateModal(template, section, disabled) {
        this.selectedEmailTemplate = template;
        this.currentSection = section;
        this.disabled = disabled;
        this.$nextTick(() => {
          if (this.$refs.newTemplateModal) {
            this.$refs.newTemplateModal.open();
          }
        });
      },
      resetData() {
        this.disabled = false;
        this.selectedEmailTemplate = null;
      },
      openDeleteTemplateModal(template) {
        this.deleteTemplate = template;
        this.checkedLinkedTask(template);
      },
      checkedLinkedTask(template) {
        http
          .get(`/check_template_linkage/${template.id}`)
          .then(response => {
            if (response.data.linked) {
              this.linkedAT = response.data.linked;
              this.showDeleteButton = false;
            } else {
              this.showDeleteButton = true;
            }
            this.$refs.deleteTemplateModal.open();
          })
          .catch(error => {
            this.emitError('Error checking template linkage:', error);
          });
      },
      closeDeleteTemplateModal() {
        this.handleClose();
        this.$refs.deleteTemplateModal.close();
      },
      deleteEmailTemplate() {
        http
          .delete(`/email_templates/${this.deleteTemplate.id}.json`)
          .then(() => {
            this.$refs.deleteTemplateModal.close();
            this.okInput();
            this.emitSuccess('Template deleted successfully!');
          })
          .catch(error => {
            this.$refs.deleteTaskModal.close();
            this.emitError(`Sorry, there was an error deleting Email Template. ${error.response.data.message}`);
          });
      },
      okInput() {
        this.fetchEmailTemplates();
        this.resetData();
      },
      handleClose() {
        this.linkedAT = [];
        this.showDeleteButton = false;
      },
    },
  };
</script>
