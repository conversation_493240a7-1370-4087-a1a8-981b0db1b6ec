ActiveAdmin.register_page "Total Counts" do
  menu parent: "Metrics", label: "Total Counts", priority: 9
  breadcrumb do
    ['admin', 'metrics']
  end
  controller do
    include ActionView::Helpers::NumberHelper
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def index
      @companies_count = Company.all.count
      @users_count = User.all.count
      @company_users_count = CompanyUser.all.count
      @managed_assets_count = ManagedAsset.all.count
      @discovered_assets_count = DiscoveredAsset.all.count
      @help_tickets_count = HelpTicket.all.count
      @probe_locations_count = ProbeLocation.all.count
      @windows_agent_count = AgentLocation.where("os ILIKE :search_query", { search_query: "%windows%" }).count
      @mac_agents_count = AgentLocation.where("os ILIKE :search_query", { search_query: "%mac%" }).count
      @contracts_count = Contract.all.count
      @vendors_count = Vendor.all.count
      @transactions_count = GeneralTransaction.active.count
      @licences_count = number_to_human(Integrations::App.licensed.sum(:total_users))
      @consumed_licenses_count = number_to_human(Integrations::App.licensed.sum(:used))
      @linked_licences_count = number_to_human(Integrations::App.licensed.linked.sum(:total_users))
      @linked_consumed_licenses_count = number_to_human(Integrations::App.licensed.linked.sum(:used))
    end
  end

  content title: "Total Counts" do
    render partial: "admin/metrics/total_counts_metric"
  end
end
