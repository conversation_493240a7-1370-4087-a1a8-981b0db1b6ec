module HelpTickets
  class TicketK<PERSON>banFieldsController < ModulesController
    def index
      ticket_kanban_field = TicketKanbanField.find_by( workspace: scoped_workspace, user: current_user) ||
                             TicketKanbanField.find_by( workspace: scoped_workspace, user: nil )

      if ticket_kanban_field
        selected_fields = ticket_kanban_field.fields.select { |item| item['active'] == true } || []
        unselected_fields = ticket_kanban_field.fields.select { |item| item['active'] == false } || []

        render json: { selected_fields: selected_fields, unselected_fields: unselected_fields }, status: :ok
      else
        render json: { message: 'Ticket kanban fields not found' }, status: :not_found
      end
    end

    def update
      user_ticket_kanban_field = TicketKanbanField.find_or_initialize_by( company_id: scoped_company.id, workspace_id: scoped_workspace.id, user_id: current_user.id )
      user_ticket_kanban_field.assign_attributes(fields: params[:fields])

      if user_ticket_kanban_field.save!
        render json: { message: "Ticket kanban fields have been saved successfully" }, status: :ok
      else
        render json: { message: user_ticket_kanban_field.errors.to_sentence }, status: :unprocessable_entity
      end
    end

    private
    def ticket_kanban_field_params
      params.permit(:workspace_id)
    end
  end
end
