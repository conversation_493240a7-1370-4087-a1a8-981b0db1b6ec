#!/usr/bin/env ruby
# Chrome Devices Processing Simulator
# Simulates the exact save_chrome_devices_worker.rb logic on NSBOROS CSV data

require 'csv'

class ChromeDevicesProcessingSimulator
  def initialize(csv_file_path)
    @csv_file_path = csv_file_path
    @processing_stats = {
      total_devices: 0,
      empty_mac_addresses: 0,
      invalid_mac_addresses: 0,
      valid_mac_addresses: 0,
      nil_mac_addresses: 0,
      all_zeros_mac: 0,
      invalid_format_mac: 0,
      successfully_processed: 0,
      would_be_skipped: 0
    }
    @sample_issues = {
      empty_mac: [],
      invalid_mac: [],
      nil_mac: []
    }
  end

  def run_simulation
    puts "🔍 CHROME DEVICES PROCESSING SIMULATION"
    puts "=" * 50
    puts "📁 Processing file: #{@csv_file_path}"
    
    process_csv_data
    generate_detailed_report
    analyze_your_selected_code
    provide_recommendations
  end

  private

  def process_csv_data
    CSV.foreach(@csv_file_path, headers: true) do |row|
      @processing_stats[:total_devices] += 1
      
      # Extract device data (matching your worker structure)
      device_data = {
        serial_number: row['serialNumber'],
        model: row['model'],
        os_version: row['osVersion'],
        mac_address: row['macAddress'],  # Column 14
        ethernet_mac: row['ethernetMacAddress'], # Column 13
        device_id: row['deviceId'],
        provision_status: row['provisionStatus']
      }
      
      # Simulate the exact worker logic
      simulate_device_processing(device_data)
    end
  end

  def simulate_device_processing(device_data)
    # Exact same logic as save_chrome_devices_worker.rb
    mac_addresses = []
    display_name = device_data[:model] && !device_data[:model].empty? ? device_data[:model] : "ChromeOS #{device_data[:os_version]}"
    serial_number = device_data[:serial_number]

    # This is the critical line from your worker
    mac_address = device_data[:mac_address] ? device_data[:mac_address].upcase : nil
    
    # Track MAC address states
    if mac_address.nil?
      @processing_stats[:nil_mac_addresses] += 1
      @sample_issues[:nil_mac] << {
        serial: serial_number,
        model: device_data[:model],
        original_mac: device_data[:mac_address]
      } if @sample_issues[:nil_mac].length < 5
    elsif mac_address.empty?
      @processing_stats[:empty_mac_addresses] += 1
      @sample_issues[:empty_mac] << {
        serial: serial_number,
        model: device_data[:model],
        original_mac: device_data[:mac_address]
      } if @sample_issues[:empty_mac].length < 5
    elsif mac_address_valid?(mac_address)
      @processing_stats[:valid_mac_addresses] += 1
      mac_addresses << mac_address
    else
      @processing_stats[:invalid_mac_addresses] += 1
      @sample_issues[:invalid_mac] << {
        serial: serial_number,
        model: device_data[:model],
        original_mac: device_data[:mac_address],
        processed_mac: mac_address
      } if @sample_issues[:invalid_mac].length < 5

      # Categorize invalid types
      if mac_address == '00:00:00:00:00:00'
        @processing_stats[:all_zeros_mac] += 1
      else
        @processing_stats[:invalid_format_mac] += 1
      end
    end
    
    # Simulate the selected code logic
    # mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
    # For simulation, assume existing asset has empty MAC addresses (worst case)
    existing_asset_mac_addresses = []
    mac_addresses_data = mac_addresses.any? ? mac_addresses : existing_asset_mac_addresses

    # Track processing results
    if mac_addresses_data.any?
      @processing_stats[:successfully_processed] += 1
    else
      @processing_stats[:would_be_skipped] += 1
    end
  end

  def mac_address_valid?(mac_address)
    return false if mac_address.nil? || mac_address.empty?

    regex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i
    mac_address.to_s.include?("00:00:00:00:00:00") ? false : !mac_address.to_s.match(regex).nil?
  end

  def generate_detailed_report
    puts "\n📊 PROCESSING RESULTS"
    puts "-" * 30
    
    total = @processing_stats[:total_devices]
    puts "Total devices in CSV: #{total}"
    puts "Expected in system: 4,957"
    puts "Missing devices: #{total - 4957}"
    
    puts "\n🔍 MAC Address Analysis:"
    puts "Valid MAC addresses: #{@processing_stats[:valid_mac_addresses]} (#{percentage(@processing_stats[:valid_mac_addresses], total)}%)"
    puts "Empty MAC addresses: #{@processing_stats[:empty_mac_addresses]} (#{percentage(@processing_stats[:empty_mac_addresses], total)}%)"
    puts "Nil MAC addresses: #{@processing_stats[:nil_mac_addresses]} (#{percentage(@processing_stats[:nil_mac_addresses], total)}%)"
    puts "Invalid MAC addresses: #{@processing_stats[:invalid_mac_addresses]} (#{percentage(@processing_stats[:invalid_mac_addresses], total)}%)"
    puts "  - All zeros (00:00:00:00:00:00): #{@processing_stats[:all_zeros_mac]}"
    puts "  - Invalid format: #{@processing_stats[:invalid_format_mac]}"
    
    puts "\n📈 Processing Outcome:"
    puts "Would be successfully processed: #{@processing_stats[:successfully_processed]}"
    puts "Would have empty MAC data: #{@processing_stats[:would_be_skipped]}"
    
    puts "\n🔍 Sample Problematic Devices:"
    
    if @sample_issues[:empty_mac].any?
      puts "\nEmpty MAC addresses:"
      @sample_issues[:empty_mac].each do |device|
        puts "  Serial: #{device[:serial]}, Model: #{device[:model]}"
      end
    end
    
    if @sample_issues[:nil_mac].any?
      puts "\nNil MAC addresses:"
      @sample_issues[:nil_mac].each do |device|
        puts "  Serial: #{device[:serial]}, Model: #{device[:model]}"
      end
    end
    
    if @sample_issues[:invalid_mac].any?
      puts "\nInvalid MAC addresses:"
      @sample_issues[:invalid_mac].each do |device|
        puts "  Serial: #{device[:serial]}, Original: '#{device[:original_mac]}', Processed: '#{device[:processed_mac]}'"
      end
    end
  end

  def analyze_your_selected_code
    puts "\n🎯 YOUR SELECTED CODE ANALYSIS"
    puts "-" * 40
    puts "Code: mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses"
    
    empty_mac_count = @processing_stats[:empty_mac_addresses] + @processing_stats[:nil_mac_addresses] + @processing_stats[:invalid_mac_addresses]
    
    puts "\nImpact Analysis:"
    puts "Devices with empty/invalid MAC: #{empty_mac_count}"
    puts "These devices would rely on existing asset MAC data"
    puts "If existing asset also has empty MAC → device gets empty MAC array"
    puts "This explains your 684 empty MAC rows in the database"
    
    puts "\nScenarios:"
    puts "1. New device, empty MAC → uses existing asset MAC (if any)"
    puts "2. New device, empty MAC + existing asset empty → stays empty"
    puts "3. New device, valid MAC → overwrites existing MAC"
    
    missing_devices = @processing_stats[:total_devices] - 4957
    puts "\nMissing Devices Breakdown:"
    puts "Total missing: #{missing_devices}"
    puts "Likely due to empty MAC: #{empty_mac_count} (#{percentage(empty_mac_count, missing_devices)}% of missing)"
  end

  def provide_recommendations
    puts "\n💡 RECOMMENDATIONS"
    puts "=" * 20
    
    puts "\n🎯 Root Cause Identified:"
    puts "- Your CSV has #{@processing_stats[:total_devices]} total devices"
    puts "- #{@processing_stats[:empty_mac_addresses] + @processing_stats[:nil_mac_addresses]} have empty/nil MAC addresses"
    puts "- #{@processing_stats[:invalid_mac_addresses]} have invalid MAC addresses"
    puts "- This accounts for most of your missing 795+ devices"
    
    puts "\n🔧 Solutions:"
    puts "1. Modify worker to process devices without MAC addresses (if they have serial numbers)"
    puts "2. Add logging to track filtered devices"
    puts "3. Consider using serial numbers for device identification"
    puts "4. Investigate why Google API returns empty MACs"
    
    puts "\n📝 Next Steps:"
    puts "1. Check if these devices should be counted in inventory"
    puts "2. Verify if they have valid serial numbers"
    puts "3. Consider alternative identification methods"
  end

  def percentage(part, total)
    return 0 if total == 0
    ((part.to_f / total) * 100).round(2)
  end
end

# Run the simulation
if __FILE__ == $0
  csv_path = "/home/<USER>/Downloads/nsboro google devices console.csv"
  simulator = ChromeDevicesProcessingSimulator.new(csv_path)
  simulator.run_simulation
end
