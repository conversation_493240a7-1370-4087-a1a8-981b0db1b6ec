ActiveAdmin.register Logs::EmailLog, as: 'email_log' do
  menu parent: "Logs", label: "Email Logs", priority: 11
  breadcrumb do
    ['admin', 'logs']
  end

  filter :receiver_email
  filter :sender_email
  filter :company, as: :select, collection: Company.order("lower(name) ASC")
  filter :status, as: :select, collection: Logs::EmailLog.statuses

  actions :index, :show, :destroy

  index do
    selectable_column
    id_column
    column :receiver_email
    column :sender_email
    column :email_type
    column ("Company") { |email_log| "#{email_log.company&.name}" }
    column :status
    column ("created_at") { |event| "#{event.created_at.to_written_time}" }
    column :error_message
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
