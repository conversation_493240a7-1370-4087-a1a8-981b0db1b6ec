<template>
  <div class="mt-5 mb-4">
    <div
      v-if="currentContract && isLoaded"
      class="position-relative"
    >
      <div class="row">
        <span class="col-4 mt-2">
          <span :style="{ color: categoryColor }">&bull;</span>
          {{ categoryName(currentContract.category) }}
        </span>
        <div class="col-8">
          <span>
            <a
              v-if="canManage && currentContract.archived"
              class="text-secondary edit-delete-btn delete ml-3"
              href="#"
              role="button"
              data-tc-delete-contract-btn
              @click.prevent="deleteContractCheck"
            >
              <i class="nulodgicon-trash-b" />
            </a>

            <a
              v-if="canManage && !currentContract.archived"
              class="text-secondary edit-delete-btn archive"
              href="#"
              role="button"
              data-tc-archive-contract-btn
              @click.stop="showArchiveModal"
            >
              <i class="nulodgicon-archive" />
            </a>

            <a
              v-if="canManage && currentContract.archived"
              class="text-secondary edit-delete-btn unarchive"
              href="#"
              role="button"
              @click.stop="showUnarchiveModal"
            >
              <i class="nulodgicon-unarchive" />
            </a>

            <a
              v-if="canManage"
              class="text-secondary mr-3 edit-delete-btn clone"
              href="#"
              role="button"
              data-tc-clone-contract-btn
              @click.prevent="cloneContract"
            >
              <i class="genuicon-duplicate-contract" />
            </a>

            <a
              v-if="canManage && !currentContract.archived"
              class="text-secondary mr-3 edit-delete-btn edit"
              href="#"
              role="button"
              data-tc-edit-contract-btn
              @click.prevent="goToEdit"
            >
              <i class="nulodgicon-edit mb-2" />
            </a>

            <router-link
              to="/all"
              class="text-secondary back-to-link mr-4"
              role="button"
              data-tc-back-btn
            >
              <i class="nulodgicon-arrow-left-c white mr-2" />
              <span>Back to <strong>all contracts</strong></span>
            </router-link>
          </span>
        </div>
      </div>
      <div class="row w-100">
        <icon-link
          v-if="currentContract.vendor"
          v-tooltip="`Vendor: ${currentContract.vendor.name}`"
          :name="currentContract.vendor.name"
          :url="currentContract.vendor.url"
          :size="80"
          class="col-auto mt-1 d-flex align-items-center justify-content-center"
        />

        <div class="col mt-3">
          <h1
            class="d-inline-block mb-0 mr-2 text-secondary"
            data-tc-contract-headline
          >
            {{ currentContract.name }}
          </h1>
          <span
            v-if="currentContract.archived"
            class="badge badge--archived position-absolute"
          >
            <small class="text-uppercase font-weight-semi-bold">Archived</small>
          </span>
          <span
            v-if="isExpired"
            class="badge badge--expired"
          >
            <small class="text-uppercase font-weight-semi-bold">Expired</small>
          </span>
          <tag
            v-for="tag in currentContract.tags"
            :key="tag.id"
            class="mb-1 mr-1"
          >
            {{ tag.tag }}
            <i
              aria-hidden="true"
              tabindex="1"
              class="multiselect__tag-icon"
              :class="{disabled: isLoading}"
              @click="removeTag(tag)"
            />
          </tag>
          <a
            href="#"
            class="text-secondary small mb-1 d-inline-block align-bottom mx-1"
            @click.stop.prevent="openTagsModal"
          >+ Add tag</a>
        </div>
      </div>
      <div class="mt-3">
        <div
          v-if="!currentContract.vendor"
          class="d-flex align-items-end"
        >
          <span class="d-inline-block text-muted mb-0">
            No vendor present
          </span>
        </div>
        <div
          v-else
          class="d-flex align-items-center"
        >
          <span
            v-tooltip="`Vendor: ${currentContract.vendor.name}`"
            class="d-inline-flex align-items-center"
          >
            <icon-link
              :name="currentContract.vendor.name"
              :url="currentContract.vendor.url"
              :size="24"
            />
            <span class="d-inline-block mb-0 ml-0.5">
              <a
                :href="`/vendors/${currentContract.vendor.id}`"
                target="_blank"
              >{{ currentContract.vendor.name }}</a>
            </span>
          </span>
          <span
            v-if="currentContract.product"
            class="d-flex align-items-center"
          >
            <span class="mx-2">/</span>
            <span
              v-tooltip="`Linked Product: ${currentContract.product.name}`"
              class="d-flex"
            >
              <product-image
                class="mr-1 d-flex align-items-center"
                :product="currentContract.product"
                :width="20"
              />
              <span class="align-middle">{{ currentContract.product.name }}</span>
            </span>
            <span
              v-if="currentContract.trackSpend"
              v-tooltip="'Monthly transactions for this contract are tracked in the Vendors module through this product.'"
              class="d-flex align-items-center"
            >
              <i class="genuicon-module-vendors d-flex text-vendor ml-2"/>
              <i class="nulodgicon-information-circled text-info ml-1"/>
            </span>
          </span>
        </div>

      </div>
      <hr class="mb-5">
      <div class="row">
        <contract-sub-menu :contract="currentContract" />

        <div class="col-10 mr-n5">
          <router-view />
        </div>
      </div>
    </div>

    <div
      v-else
      class="row"
    >
      <h5 class="ml-4 text-muted">
        Loading contract
      </h5>
      <pulse-loader
        :loading="true"
        class="float-left ml-3"
        color="#0d6efd"
        size="0.5rem"
      />
    </div>

    <sweet-modal
      ref="deleteContractModal"
      v-sweet-esc
      title="Before you delete this contract..."
    >
      <template slot="default">
        <div>
          <warning-message
            v-if="currentContract"
            ref="warningMessage"
            entity-type="Contract"
            :entity="currentContract"
          />
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-danger"
        data-tc-delete-confirmation-btn
        @click.stop="trashContract"
      >
        Delete
      </button>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        data-tc-cancel-btn
        @click.stop="cancelContractRemove"
      >
        Cancel
      </button>
    </sweet-modal>

    <sweet-modal
      ref="archiveModal"
      v-sweet-esc
      title="Before you archive this contract..."
    >
      <template slot="default">
        <div class="text-left">
          <p>
            Are you sure you want to archive this contract? You can unarchive it any time later.
          </p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        data-tc-cancels-btn
        @click.stop="$refs.archiveModal.close"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-link text-danger"
        data-tc-archive-confirmation-btn
        @click.stop="archiveContract"
      >
        Archive
      </button>
    </sweet-modal>

    <sweet-modal
      ref="unArchiveModal"
      v-sweet-esc
      title="Before you unarchive this contract..."
    >
      <template slot="default">
        <div class="text-center">
          <p>
            This contract will be unarchived, you can archive it any time later.
            Would you like to continue?
          </p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="$refs.unArchiveModal.close"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-link text-success"
        @click.stop="unArchiveContract"
      >
        Unarchive
      </button>
    </sweet-modal>

    <sweet-modal
      ref="tagsModal"
      v-sweet-esc
      title="Add a new tag"
      class="tags-modal"
    >
      <template>
        <form
          accept-charset="UTF-8"
          enctype="multipart/form-data"
        >
          <div class="form-group">
            <multi-select
              v-if="currentContract"
              id="contract_tags"
              class="w-100 mb-1"
              placeholder="Type in a new tag"
              tag-placeholder="Add a tag"
              :allow-empty="true"
              :multiple="true"
              :options="contractTags"
              :taggable="true"
              :value="tagsArray"
              @remove="removeTagSelection"
              @select="selectTag"
              @tag="addNewTag"
            />
          </div>
        </form>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        type="button"
        @click="closeTagsModal"
      >
        Close
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import contractSpecific from 'mixins/contract_specific';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import { CATEGORY_COLORS } from 'common/chart_colors';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import IconLink from 'components/shared/icon_link';
  import ProductImage from 'components/shared/product_image';
  import dates from 'mixins/dates';
  import _ from 'lodash';
  import MultiSelect from 'vue-multiselect';
  import permissionsHelper from 'mixins/permissions_helper';
  import contractClone from 'mixins/contract_clone';
  import WarningMessage from '../shared/warning.vue';
  import Tag from "../shared/tag.vue";
  import ContractSubMenu from './contract_sub_menu.vue';

  export default {
    components: {
      SweetModal,
      PulseLoader,
      IconLink,
      ProductImage,
      ContractSubMenu,
      Tag,
      MultiSelect,
      WarningMessage,
    },
    mixins: [
      contractSpecific, 
      dates, 
      permissionsHelper, 
      contractClone,
    ],
    data() {
      return {
        file: '',
        isLoading: false,
      };
    },
    computed: {
      ...mapGetters([
        'totalMonthlyValue',
        'currentContract',
        'loading',
        "tags",
      ]),
      contractTags() {
        if (!this.tags || this.tags.length === 0) {
          return [];
        }
        const myTags = [];
        for (let idx = 0; idx < this.tags.length; idx += 1) {
          myTags.push(this.tags[idx].tag);
        }
        return myTags;
      },
      isExpired() {
        const today = moment();
        const endDate = moment(this.currentContract.endDate);
        return endDate && endDate.isBefore(today, 'day');
      },
      categoryColor() {
        if (this.currentContract && this.currentContract.category) {
          const contractCategoryName = this.categoryName(this.currentContract.category);
          if (contractCategoryName) {
            return CATEGORY_COLORS[contractCategoryName.toLowerCase().replace(/\s|-/gi, "_")] || CATEGORY_COLORS.uncategorized;
          }
        }

        return CATEGORY_COLORS.uncategorized;
      },
      showAttachments() {
        return this.currentContract.contractAttachments && this.currentContract.contractAttachments.length > 0;
      },
      tagsArray() {
        return this.currentContract ? this.currentContract.tagsArray : [];
      },
      canManage() {
        return this.isWrite || (this.isScoped && this.currentContract.creatorId === this.$currentContributorId);
      },
    },
    methods: {
      ...mapMutations([
        'setCurrentContract', 
        'setIndexPage',
        'setLoading',
        'setCompanyChannelKey',
      ]),
      ...mapActions('contracts', [
        'fetchTags',
      ]),

      onWorkspaceChange() {
        this.setCurrentContract(null);
        const {id} = this.$route.params;
        this.fetchContract(id);
        this.fetchTags();
      },

      deleteContractCheck() {
        this.$refs.warningMessage.open();
        this.$refs.deleteContractModal.open();
      },
      cancelContractRemove() {
        this.$refs.deleteContractModal.close();
      },
      trashContract() {
        http
          .delete(`/contracts/${this.currentContract.id}.json`)
          .then(() => {
            this.setIndexPage(0);
            this.emitSuccess("Contract was successfully removed.");
            this.$router.push({ path: "/all"});
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error removing this contract (${error.response.data.message}).`);
          });
          this.$refs.deleteContractModal.close();
      },
      archiveContract() {
        http
          .post(`/contracts/${this.currentContract.id}/archive.json`)
          .then(() => {
            this.setIndexPage(0);
            this.emitSuccess("Contract was successfully archived.");
            this.$router.push({ path: '/all' });
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error archiving this contract (${error.response.data.message}).`);
          });

        this.$refs.archiveModal.close();
      },
      unArchiveContract() {
        http
          .post(`/contracts/${this.currentContract.id}/unarchive.json`)
          .then(() => {
            this.emitSuccess("Contract was successfully unarchived.");
            this.$router.push({ path: '/all' });
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error unarchiving this contract (${error.response.data.message}).`);
          });

        this.$refs.unArchiveModal.close();
      },
      showArchiveModal() {
        this.$refs.archiveModal.open();
      },
      showUnarchiveModal() {
        this.$refs.unArchiveModal.open();
      },
      goToEdit() {
        this.$router.push({ path: `/${this.currentContract.id}/edit` });
      },
      openTagsModal() {
        if (!this.isWriteAny) {
          this.emitError(`Sorry, you do not have permissions to add new tags.`);
          return;
        }
        this.$refs.tagsModal.open();
      },
      closeTagsModal() {
        this.$refs.tagsModal.close();
      },
      selectTag(tag) {
        this.addNewTag(tag);
      },
      addNewTag(newTag) {
        http
          .post(`/contract_tags.json`, { contract_id: this.currentContract.id, tag: newTag })
          .then(() => {
            this.$store.dispatch("fetchTags");
            this.fetchContract(this.$route.params.id);
            this.emitSuccess(`${newTag} was successfully added.`);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error adding this tag (${error.response.data.message}).`);
          });
      },
      removeTagSelection(tag) {
        const removedTag = this.currentContract.tags.find(t => t.tag === tag);
        this.removeTag(removedTag);
      },
      removeTag(tag) {
        this.isLoading = true;
        if (!this.isWrite) {
          this.emitError(`Sorry, you do not have permissions to remove tags.`);
          return;
        }
        const index = _.findIndex(this.currentContract.tags, (t) => t === tag);
        this.currentContract.tags.splice(index, 1);
        http
          .delete(`/contract_tags/${tag.id}.json`)
          .then(() => {
            this.$store.dispatch('fetchAuditHistory');
            this.emitSuccess(`${tag.tag} was successfully removed.`);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error deleting this tag (${error.response.data.message}).`);
          })
          .finally(() => {
            this.fetchContract(this.currentContract.id);
            this.isLoading = false;
          });
      },
      isLoaded(){
        return !!this.currentContract;
      },
    },
  };
</script>

<style scoped lang="scss">
.badge--expired {
  background-color: $color-accent;
  color: $white;
  bottom: 7px;
}

.alert-modal {
  :deep(.sweet-modal) {
    overflow: unset;
  }
}

.badge--archived {
  background-color: $color-accent;
  color: $white;
  border-radius: 5px;
}

.disabled {
  pointer-events: none;
  cursor: default;
}

.edit-delete-btn.clone:before { 
  content: "Clone"; 
}
</style>
