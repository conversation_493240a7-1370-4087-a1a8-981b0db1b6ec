class Integrations::Ubiquiti::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs
  include IntegrationAlertsHelper
  include IntegrationHelper

  sidekiq_options queue: 'integrations'

  def perform(credential_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    @ubiquiti = set_read_replica_db do 
      Integrations::Ubiquiti::Config.find_by(id: credential_id)
    end
    return if @ubiquiti.nil?

    @import_target = @ubiquiti.import_type
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0

    set_read_replica_db do
      @ubiquiti_controllers = @ubiquiti.ubiquiti_controllers
      @current_company = @ubiquiti.company
      @company_integration = @ubiquiti.company_integration
      @intg_id = Integration.find_by_name("ubiquiti").id
    end

    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    pusher(@ubiquiti, true, 0.2)
    @ubiquiti_controllers.each do |controller|
      create_failed_auth_log(controller.to_json) && next unless controller.authenticate?

      pusher(@ubiquiti, true, 0.6)
      @ubiquiti_config = controller
      save_assets
    end
    pusher(@ubiquiti, true, 0.8)
    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil, 
                                           failure_count: 0,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag,
                                           alert_info: IntegrationAlertsHelper::SUCCESS_STATE)
    @company_integration.save!
    pusher(@ubiquiti, true, 1, {
      new_ubiquiti_discovered_assets: @new_discovered_assets_count,
      new_ubiquiti_managed_assets: @new_managed_assets_count
    })
    create_logs
    intg_duration_analysis(@current_company.id, first_time_flag)
  rescue Exception => e
    create_logs
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           error_message: e.message, 
                                           failure_count: @company_integration.failure_count + 1,
                                           active: true,
                                           company_user_id: @company_user_id,
                                           first_time_flag: @first_time_flag,
                                           alert_info: IntegrationAlertsHelper::ERROR_STATE)

    @company_integration.save

    error_messages = ["api.err.LoginRequired", "Failed to open TCP connection", "getaddrinfo: Name or service not known"]

    if !@first_time_flag && error_messages.find {|em| e.message.downcase.include?(em.downcase)}
      send_notification(@company_integration)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find {|em| e.message.downcase.include?(em.downcase)}
    end
    pusher(@ubiquiti ,false, 0)
  end

  def save_assets
    ubiquiti_service&.each do |service|
      save_devices service
      save_clients service
    end
  end

  def create_failed_auth_log detail
    error = Exception.new 'Failed to authenticate controller'
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(error, detail, "authenticate_controller").to_json)
  end

  def save_clients service
    service[:data]&.list_clients&.each do |client_info|
      retries = 0
      begin
        discovered_asset = nil
        managed_asset = nil
        display_name = client_info["hostname"] || client_info["mac"].try(:upcase)
        ip_address = client_info["ip"]
        manufacturer = client_info["oui"]
        mac_addresses = []
        optional_data = {}
        mac_address = (client_info["mac"].try(:upcase) || client_info['wifiMac'].try(:upcase))
        mac_addresses << mac_address if mac_address_valid? mac_address
        @asset_device_type = :client

        #params company, is_probe, serial_number, mac_addresses, display_name, ip_address, manufacturer, is_mac_duplicate
        discovered_asset = find_discovered_asset(@current_company, false, nil, mac_addresses, display_name, ip_address, manufacturer, false)

        if discovered_asset.blank? && ip_address.present? && mac_addresses.blank? && manufacturer.blank?
          company_discovered_asset_by_ip = set_read_replica_db do
            @current_company.discovered_assets.find_by(ip_address: ip_address)
          end
          next if company_discovered_asset_by_ip
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.new(company_id: @current_company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        manufacturer_info = manufacturer.present? ? manufacturer : ""
        
        is_lower_precedence = is_higher_precedence?(**precedence_data(discovered_asset))
        discovered_asset.assign_attributes(
          display_name: display_name_data,
          mac_addresses: mac_addresses_data,
          ip_address: ip_address_data,
          manufacturer: manufacturer_info,
          source: "ubiquiti",
          discovered_asset_type: @asset_device_type,
          optional_details: client_description(client_info),
          lower_precedence: is_lower_precedence
        )

        unless discovered_asset.integration_location&.source == 'probe'
          discovered_asset.integrations_locations_id = set_read_replica_db do
            @current_company.integrations_locations.find_by(address: service[:site])&.id
          end
        end

        if discovered_asset.display_name.present? && discovered_asset.manufacturer.present? &&
        discovered_asset.mac_addresses.present? && discovered_asset.machine_serial_no.present?
          discovered_asset.status = :ready_for_import
        end

        if mac_addresses.present?
          # params company, serial_number, mac_addresses, display_name, manufacturer
          managed_asset = find_managed_asset(@current_company, nil, mac_addresses_data, display_name_data, manufacturer_info)

          if managed_asset.present?
            discovered_asset.status = :imported
            discovered_asset.managed_asset_id = managed_asset.id
          end
        else
          discovered_asset.status = :unrecognized
        end

        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new
        @new_discovered_assets_count += 1 if is_new

        asset_source_data = {
          display_name: display_name,
          mac_addresses: mac_addresses,
          ip_address: client_info["ip"],
          manufacturer: client_info["oui"],
          source: "ubiquiti",
          discovered_asset_type: @asset_device_type,
        }
        add_source(discovered_asset, asset_source_data)
        discovered_asset.reload

        if @import_target == "managed_asset" && manufacturer_info&.downcase.include?('ubiquiti') && discovered_asset.managed_asset_id.blank?
          BulkDiscoveredAssetsUpdate.new([], discovered_asset.company, nil).move_to_managed_asset(discovered_asset)
          @new_managed_assets_count += 1
        end
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, discovered_asset, "save_clients")
          break
        end
      rescue => e
        log_exception(e, discovered_asset, "save_clients")
      end
    end
  end

  def save_devices service
    service[:data]&.list_devices&.each do |device_info|
      retries = 0
      begin
        discovered_asset = nil
        managed_asset = nil
        display_name = device_info["name"] || device_info["mac"].try(:upcase)
        serial_number = device_info["serial"]
        ip_address = device_info["ip"]
        mac_addresses = []
        optional_data = {}
        mac_address = (device_info["mac"].try(:upcase) || device_info['wifiMac'].try(:upcase))
        mac_addresses << mac_address if mac_address_valid? mac_address
        @asset_device_type = :device
        last_check_in_time = device_info["last_seen"]&.then { |t| Time.at(t).utc }

        if [display_name, mac_addresses, serial_number, ip_address].all?(&:blank?)
          next       # Skip processing devices having missing fields
        end
        #params company, is_probe, serial_number, mac_addresses, display_name, ip_address, manufacturer, is_mac_duplicate
        discovered_asset = find_discovered_asset(@current_company, false, serial_number, mac_addresses, display_name, ip_address, 'Ubiquiti', false)

        if discovered_asset.blank? && ip_address.present? && serial_number.blank? && mac_addresses.blank?
          company_discovered_asset_by_ip = set_read_replica_db do
            @current_company.discovered_assets.find_by(ip_address: ip_address)
          end
          next if company_discovered_asset_by_ip
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.ready_for_import.new(company_id: @current_company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        ip_address_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        serial_data = serial_number.present? ? serial_number : discovered_asset.machine_serial_no

        is_lower_precedence = is_higher_precedence?(**precedence_data(discovered_asset))
        company_integration_locations = set_read_replica_db do
          @current_company.integrations_locations.find_by(address: service[:site])&.id
        end
        discovered_asset.assign_attributes(
          display_name: display_name_data,
          mac_addresses: mac_addresses_data,
          ip_address: ip_address_data,
          manufacturer: "Ubiquiti",
          asset_type: device_asset_type(device_info["type"]),
          machine_serial_no: serial_data,
          integrations_locations_id: company_integration_locations,
          source: "ubiquiti",
          model: device_info['model'],
          discovered_asset_type: @asset_device_type,
          optional_details: device_description(device_info),
          lower_precedence: is_lower_precedence,
          last_check_in_time: last_check_in_time
        )

        # params company, serial_number, mac_addresses, display_name, manufacturer
        managed_asset = find_managed_asset(@current_company, serial_data, mac_addresses_data, display_name_data, "Ubiquiti")

        if managed_asset.present?
          discovered_asset.status = :imported
          discovered_asset.managed_asset_id = managed_asset.id
        end
        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new
        @new_discovered_assets_count += 1 if is_new
        
        asset_source_data = {
          display_name: display_name,
          mac_addresses: mac_addresses,
          ip_address: device_info["ip"],
          manufacturer: "Ubiquiti",
          asset_type: device_asset_type(device_info["type"]),
          machine_serial_no: serial_number,
          source: "ubiquiti",
          discovered_asset_type: @asset_device_type,
        }
        add_source(discovered_asset, asset_source_data)
        discovered_asset.reload

        if @import_target == "managed_asset" && discovered_asset.managed_asset_id.blank?
          BulkDiscoveredAssetsUpdate.new([], discovered_asset.company, nil).move_to_managed_asset(discovered_asset)
          @new_managed_assets_count += 1
        end
      rescue PG::ConnectionBad => e
        retries += 1
        if retries <= 3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, discovered_asset, "save_devices")
          break
        end
      rescue => e
        log_exception(e, discovered_asset, "save_devices")
      end
    end
  end

  def client_description client_info
    if client_info['tx_rate'] && client_info['rx_rate']
      internet_usage = "Sent = #{(client_info['tx_rate'] / 1000).round}mbs, Receive = #{(client_info['rx_rate'] / 1000).round}mbs"&.gsub("\;", "")
    end

    {
      "Internet Usage": internet_usage,
      "Hostname": client_info['hostname']&.gsub("\;", ""),
      "IP Address": client_info['ip']&.gsub("\;", ""),
      "SSID": client_info['essid']&.gsub("\;", ""),
      "Vlan": client_info['vlan'],
      "UpTime": client_info['uptime'],
      "SW-Port": client_info['sw_port'],
      "SW-Depth": client_info['sw_depth'],
      "Is Wired": client_info['is_wired'],
      "Is Guest": client_info['is_guest'],
      "First Seen": Time.at(client_info['first_seen'].to_i),
      "Last Seen": Time.at(client_info['last_seen'].to_i),
      "Network": client_info['network']&.gsub("\;", ""),
      "AP MAC": client_info['ap_mac']&.gsub("\;", ""),
      "SW MAC": client_info['sw_mac']&.gsub("\;", ""),
      "Password Enabled": client_info['powersave_enabled'],
      "Channel": client_info['channel'],
      "Radio": client_info['radio']&.gsub("\;", ""),
      "Radio Name": client_info['radio_name']&.gsub("\;", ""),
      "ESSID": client_info['essid']&.gsub("\;", ""),
      "BSSID": client_info['bssid']&.gsub("\;", ""),
      "Power Save Enabled": client_info['powersave_enabled'],
      "Noise": client_info['noise'],
      "Signal": client_info['signal'],
      "Satisfaction": client_info['satisfaction'],
      "Anomalies": client_info['anomalies'],
      "Radio Proto": client_info['radio_proto']&.gsub("\;", ""),
      "Authorized": client_info['authorized']
    }
  end

  def device_description device_info
    if device_info['tx_rate'] && device_info['rx_rate']
      internet_usage = "Sent = #{(device_info['tx_rate'] / 1000).round}mbs, Receive = #{(device_info['rx_rate'] / 1000).round}mbs"&.gsub("\;", "")
    end

    {
      "Internet Usage": internet_usage,
      "LAN IP": device_info['ip']&.gsub("\;", ""),
      "Model": device_info['model']&.gsub("\;", ""),
      "Version": device_info['version']&.gsub("\;", ""),
      "Board Revision": device_info['board_rev'],
      "UpTime": device_info['uptime'],
      "Adopt Ip": device_info['adopt_ip']&.gsub("\;", ""),
      "Adopt Url": device_info['adopt_url']&.gsub("\;", ""),
      "Device Id": device_info['device_id']&.gsub("\;", ""),
      "Inform Url": device_info['inform_url']&.gsub("\;", ""),
      "Inform IP": device_info['inform_ip']&.gsub("\;", ""),
      "Temperature": device_info['general_temperature'],
      "Device Type": device_info["type"]&.gsub("\;", "")
    }
  end

  def device_name_from_device_mac mac_address
    display_name = ''
    company_discovered_assets = set_read_replica_db do
      @current_company.discovered_assets.ready_for_import
    end
    company_discovered_assets.each do |asset|
      if asset.mac_addresses&.include?(mac_address.try(:upcase))
        display_name = asset&.display_name
        break
      end
    end
    display_name
  end

  def device_asset_type type
    case type.downcase
    when "ugw"
      get_asset_type("Gateway")
    when "usw"
      get_asset_type("Switch")
    when "uap"
      get_asset_type("Access Point")
    end
  end

  def get_asset_type(type_name)
    asset_type = CompanyAssetType.find_or_create_by(company: @current_company, name: type_name)&.name
    asset_type
  end

  def ubiquiti_service
    @ubiquiti_config[:sites].map do |site|
      config = JSON.parse(@ubiquiti_config.to_json).symbolize_keys
      current_site = set_read_replica_db do
        @current_company.integrations_locations.find_by(address: site)
      end
      active_site_name = current_site&.address
      if active_site_name.present?
        config[:site] = active_site_name.split("-")[0]
        Integrations::Ubiquiti::UbiquitiControllerSite.create!(ubiquiti_controller_id: config[:id], integrations_location_id: current_site.id )
        { site: active_site_name, data: Integrations::Ubiquiti::FetchData.new(config, @first_time_flag, @current_company) }
      else
        {}
      end
    end
  end

  def add_source(discovered_asset, asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: discovered_asset.id, source: :ubiquiti)
    end
    das.asset_data = asset_source_data
    das.managed_asset_id = discovered_asset.managed_asset_id
    das.company_integration_id = @company_integration.id  
    das.updated_at = DateTime.now
    das.save!
  end

  private

  def event_params error, detail, api_type
    {
      company_id: @current_company.id,
      status: :error,
      error_detail: error.backtrace&.join("\n"),
      class_name: self.class.name,
      integration_id: @intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def mac_address_valid? mac_address
    regex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i
    mac_address.to_s.include?("00:00:00:00:00:00") ? false : mac_address.to_s.match(regex).present?
  end

  def precedence_data(discovered_asset)
    discovered_managed_asset_sources = set_read_replica_db do
      discovered_asset.managed_asset&.sources
    end
    {
      asset_sources: discovered_managed_asset_sources,
      current_source: "ubiquiti",
      discovered_asset: discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.ubiquiti.new(
        discovered_asset_type: @asset_device_type
      )
    }
  end
  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Ubiquiti', @company_user_id, @current_company.id)
    end
  end
end
