<template>
  <div class="clearfix row mx-1">
    <div class="blocked-table column table-width">
      <div class="mt-5 mb-3 clearfix">
        <workspace-settings-banner />
        <h5 class="font-weight-normal float-left mb-0">
          Blocked Emails &amp; Domains
        </h5>
      </div>
      <table class="blocked-table">
        <thead>
          <tr>
            <th class="mb-2">
              Blocked Emails
            </th>
            <th />
          </tr>
        </thead>
        <tbody v-if="!blockedEmails">
          <tr class="text-muted">
            <td colspan="3">
              Loading...
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="blockedEmails.length == 0">
          <tr class="text-muted">
            <td colspan="3">
              No emails are being blocked.
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr
            v-for="item in blockedEmails"
            :key="item.id"
            class="py-1"
          >
            <td>
              {{ item.entity }}
            </td>
            <td>
              <span @click="openUnblock(item)">
                <i class="remove-link nulodgicon-trash-b" />
              </span>
            </td>
          </tr>
        </tbody>

        <div class="mt-5">
          &nbsp;
        </div>

        <thead>
          <tr>
            <th class="mb-2">
              Blocked Domains
            </th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody v-if="!blockedDomains">
          <tr class="text-muted">
            <td colspan="3">
              Loading...
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="blockedDomains.length == 0">
          <tr>
            <td
              colspan="3"
              class="text-muted"
            >
              No domains are being blocked.
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr
            v-for="item in blockedDomains"
            :key="item.id"
            class="py-1"
          >
            <td>
              *{{ item.entity }}
            </td>
            <td>
              <span @click="openUnblock(item)">
                <i class="remove-link nulodgicon-trash-b" />
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <sweet-modal
      ref="unblockConfirmModal"
      v-sweet-esc
      title="Are you sure you want to unblock these items?"
    >
      <template slot="default">
        <div class="text-center">
          <p>Are you sure you want to unblock this?</p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click="closeUnblockConfirmModal"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click="submitUnblockItems"
      >
        Yes, I'm sure
      </button>
    </sweet-modal>

    <sweet-modal
      ref="blockModal"
      v-sweet-esc
      title="Block Domain/Email"
    >
      <template slot="default">
        <div class="h6 mb-3">
          Please fill in the information below to block domain/email
        </div>

        <form>
          <div class="form-group">
            <label
              key="0"
              class="w-100 p-2 mb-0"
            >
              <input
                v-model="entityType"
                value="domain"
                type="radio"
                class="mr-2"
              >
              Domain
            </label>

            <label
              key="1"
              class="w-100 p-2 mb-0"
            >
              <input
                v-model="entityType"
                value="email"
                type="radio"
                class="mr-2"
              >
              Email
            </label>

            <label class="mt-3">
              Enter value:
            </label>
            <div
              v-if="entityType == 'domain'"
              class="input-group mb-3"
            >
              <div class="input-group-prepend">
                <span
                  id="basic-addon1"
                  class="input-group-text"
                >@</span>
              </div>
              <input
                v-model="entity"
                :disabled="!entityType"
                class="form-control"
                :placeholder="placeholderText"
                aria-label="Value"
                aria-describedby="basic-addon1"
              >
            </div>
            <input
              v-else
              v-model="entity"
              :disabled="!entityType"
              class="form-control"
              :placeholder="placeholderText"
            >
          </div>
        </form>
      </template>

      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click="closeBlockModal"
      >
        Cancel
      </button>

      <button
        slot="button"
        :disabled="checkBtnSubmission"
        class="btn btn-primary ml-2"
        @click="submitBlockEntity"
      >
        Block
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import { mapMutations } from 'vuex';
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import workspaceSettingsBanner from './workspace_settings_banner.vue';


  export default {
    components: {
      SweetModal,
      workspaceSettingsBanner,
    },
    mixins: [ permissionsHelper ],
    data() {
      return {
        blockedEmails: null,
        blockedDomains: null,
        selectedCompany: null,
        selectedBlockedEntity: null,
        entityType: null,
        entity: null,
      };
    },
    computed: {
      checkBtnSubmission() {
        return !this.entityType || !this.entity;
      },
      placeholderText() {
        if (this.entityType === "domain") {
          return "eg. genuity.com";
        } 
          return "eg. <EMAIL>";
        
      },
      emailIsChecked() {
        if (this.selectedEmails.length === this.blockedEmails.length) {
          return "checked";
        } 
          return "";
        
      },
      domainIsChecked() {
        if (this.selectedDomains.length === this.blockedDomains.length) {
          return "checked";
        } 
          return "";
        
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),

      onWorkspaceChange() {
        this.fetchBlockedEntities();
      },

      // It might be better you think to make this openBlockedModal, but the button method is generic
      // to be more useful from the parent component.
      openModal() {
        this.selectedCompany = null;
        this.$refs.blockModal.open();
      },
      submitBlockEntity() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          this.emitError("Must have a workspace selected.");
          return;
        }

        http
          .post(`/blocked_entities`, { entities: [this.entity], entity_type: this.entityType })
          .then(() => {
            this.closeBlockModal();
            this.emitSuccess('Successfully added blocked domain/email');
            this.fetchBlockedEntities();
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error adding this value: ${error.response.data.message}`);
          });
      },
      closeBlockModal() {
        this.$refs.blockModal.close();
        this.entity = null;
        this.entityType = null;
      },
      openUnblock(entity) {
        this.$refs.unblockConfirmModal.open();
        this.selectedEntity = entity;
      },
      submitUnblockItems() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          this.emitError("Must have a workspace selected.");
          return;
        }
        http
          .delete(`/blocked_entities/${this.selectedEntity.id}.json`)
          .then(() => {
            this.emitSuccess('Successfully unblocked item');
            this.closeUnblockConfirmModal();
            this.fetchBlockedEntities();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error creating new users`);
          });
      },
      closeUnblockConfirmModal() {
        this.$refs.unblockConfirmModal.close();
      },

      fetchBlockedEntities() {
        const workspace = getWorkspaceFromStorage();
        if (!workspace || !workspace.id) {
          return;
        }

        this.setLoadingStatus(true);

        http
          .get('/blocked_entities.json')
          .then((res) => {
            if (res.data.emails) {
              this.blockedEmails = res.data.emails;
            } else {
              this.blockedEmails = [];
            }
            if (res.data.domains) {
              this.blockedDomains = res.data.domains;
            } else {
              this.blockedDomains = [];
            }
            this.setLoadingStatus(false);
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error loading blocked emails and domains. ${error}`);
            this.setLoadingStatus(false);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>

  label {
    border-bottom: 1px solid $themed-very-fair;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;

    &:last-of-type {
      border-bottom: 0;
    }
  }
  .heading-row {
    border-bottom: 1px solid $themed-very-fair;
  }

  .request-row,
  .email-row {
    &:hover {
      background-color: $themed-light;
    }
  }

  [type="checkbox"] {
    display: none;
  }

  .nulodgicon-checkmark {
    background-color: white;
    border-radius: 50%;
    border: 1px solid $themed-fair;
    color: white;
    display: block;
    font-size: 0.875rem;
    height: 1.5rem;
    line-height: 1.5rem;
    position: absolute;
    right: 1rem;
    text-align: center;
    top: 50%;
    transition: all 0.2s ease-in-out;
    transform: translateY(-50%);
    width: 1.5rem;
  }

  :checked ~ .nulodgicon-checkmark {
    background-color: $teal;
    border-color: $teal;
  }

  .table-width {
    max-width: 100%;
  }
  .nulodgicon-ios-email-outline {
    font-size: 2rem;
    vertical-align: middle;
  }
</style>
