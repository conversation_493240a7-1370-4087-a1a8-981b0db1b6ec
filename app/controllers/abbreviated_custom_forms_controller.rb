class AbbreviatedCustomFormsController < AuthenticatedController
  include MultiCompany::CompanyScoping

  def index
    render json: custom_forms
  end

  private

  def custom_forms
    forms = CustomForm.order(:order)

    if company_module == 'helpdesk'
      forms = forms.where(workspace_id: scoped_workspace.id)
    elsif company_id
      forms = forms.where(company_id: company_id)
    else
      forms = forms.where(company_id: scoped_company.id)
    end
    forms = forms.where(company_module: company_module) if company_module
    forms = forms.where.not(id: excludes) if excludes.present?
    forms = forms.where(is_active: true, is_draft: false) if status == 'active' || portal
    forms = forms.where(is_active: false, is_draft: false) if status == 'archived'
    forms = forms.where(is_draft: true) if status == 'draft'
    forms = forms.where(show_in_open_portal: true) if portal
    forms = forms.left_joins(:custom_form_values).select("custom_forms.*, COUNT(custom_form_values.id) AS custom_form_values_count").group("custom_forms.id")

    forms.map do |f|
      {
        id: f.id,
        name: f.form_name,
        is_active: f.is_active,
        is_draft: f.is_draft,
        default: f.default,
        company: {
          id: f.company.id,
          name: f.company.name
        },
        workspace:  {
          id: f.workspace_id,
        },
        module: f.company_module,
        in_use: f.company_module == 'company_user' ?  f.company_users.count > 0 : f.custom_form_values_count > 0
      }.merge(fetch_fields ? { form_fields: f.custom_form_fields } : {})
    end
  end

  def workspace_id
    params[:workspace_id]
  end

  def company_id
    params[:company_id]
  end

  def status
    params[:status]
  end

  def active
    params[:active]
  end

  def excludes
    params[:excludes]
  end

  def company_module
    params[:company_module]
  end

  def portal
    params[:portal]
  end

  def is_draft
    params[:is_draft]
  end

  def fetch_fields
    params[:fetch_fields]
  end
end
