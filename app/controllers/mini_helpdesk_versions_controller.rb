class MiniHelpdeskVersionsController < ApplicationController
  def download_mini_helpdesk
    mac_version = mini_hd_mac_version
    windows_version = mini_hd_windows_version

    if mac_version.present? || windows_version.present?
      render json: {
        status: true,
        mac_url: mac_version&.installer_link&.url,
        windows_url: windows_version&.installer_link&.url,
        mac_app_version: mac_version&.version,
        windows_app_version: windows_version&.version
      }
    else
      render json: { status: false }
    end
  end

  private

  def mini_hd_mac_version
    MacMiniHelpdeskVersion.last
  end

  def mini_hd_windows_version
    WindowsMiniHelpdeskVersion.last
  end
end
