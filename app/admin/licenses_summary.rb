ActiveAdmin.register Company, as: "Licenses Summary" do
  menu parent: 'Metrics', label: "Licenses Summary", priority: 4
  breadcrumb do
    ['admin', 'metrics']
  end
  
  actions :index

  filter :name, as: :select, collection: Company.order("name ASC").map { |comp| [comp.name] }
  config.sort_order = 'total_licenses_desc'

  index do
    column ("Company") do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end
    column :subdomain

    column ("Admin Email") { |company| "#{company.admin_company_users.first.try(:user).try(:email)}" }
    
    column :total_licenses do |company|
      company.apps.licensed.sum(:total_users)
    end
    
    column ("Consumed Licenses") do |company|
      company.apps.licensed.sum(:used)
    end

    column ("Linked Total Licenses") do |company|
      company.apps.licensed.linked.sum(:total_users)
    end
    
    column ("Linked Consumed Licenses") do |company|
      company.apps.licensed.linked.sum(:used)
    end
    
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def find_collection(options = {})
      super.includes(:integrations_apps).select("companies.*, COALESCE(SUM(integrations_apps.total_users), 0) as total_licenses")
        .joins("left join integrations_apps on integrations_apps.company_id = companies.id AND integrations_apps.app_type = #{licensed_app_type}")
        .group("companies.id")
        .order("total_licenses DESC")
    end

    def licensed_app_type
      Integrations::App.app_types["licensed"]
    end
  end
end
