ActiveAdmin.register ServiceOption, as: 'ManageImports' do
  menu parent: 'Manage Services', label: 'Manage Imports', priority: 5
  
  breadcrumb do
    ['admin', 'manage services']
  end 
  
  actions :all, except: [:new, :destroy]
  config.batch_actions = false

  filter :service_name

  controller do
    def scoped_collection
      super.where(service_type: ServiceOption.service_types[:imports])
    end
  end
  
  index do
    column :service_name do |option|
      link_to "#{option.service_name.humanize} module", admin_manage_import_path(option)
    end
    column :created_at
    column :updated_at

    column "Disabled For" do |option|
      "#{option.company_ids.size} companies"
    end
  end
  
  show do
    div class: 'filters' do
      form method: :get, action: admin_manage_import_path(resource) do
        div do
          label 'Company Name:'
          select name: 'company_name' do
            option 'All', value: ''
            Company.order(:name).pluck(:name).each do |company_name|
              option company_name, value: company_name, selected: params[:company_name] == company_name
            end
          end
        end
    
        div do
          label 'Status:'
          select name: 'status' do
            option 'All', value: ''
            option 'Enabled', value: 'Enabled', selected: params[:status] == 'Enabled'
            option 'Disabled', value: 'Disabled', selected: params[:status] == 'Disabled'
          end
        end
    
        div do
          input type: 'submit', value: 'Filter', class: 'button'
        end
      end
    end
    panel "Import Settings for #{resource.service_name}", style: "margin-top: 2rem;" do
      companies = Company.all.paginate(page: params[:page], per_page: 25)
      
      companies = companies.where("name ILIKE ?", "%#{params[:company_name]}%") if params[:company_name].present?

      if params[:status].present?
        case params[:status]
        when 'Enabled'
          companies = companies.where.not(id: resource.company_ids)
        when 'Disabled'
          companies = companies.where(id: resource.company_ids)
        end
      end
      
      table_for companies do
        column :id
        column :name
        column "Import Status" do |company|
          status = resource.company_ids.include?(company.id) ? 'Disabled' : 'Enabled'

          status_class = status == 'Enabled' ? 'status_tag_green' : 'status_tag_red'
          content_tag :span, status, class: status_class
        end
        column "Actions" do |company|
          link_to(
            resource.company_ids.include?(company.id) ? 'Enable' : 'Disable',
            toggle_import_admin_manage_import_path(resource, company_id: company.id),
            method: :post,
            class: 'button',
            data: { confirm: "Are you sure you want to #{resource.company_ids.include?(company.id) ? 'enable' : 'disable'} this import for #{company.name}?" }
          )
        end
      end
    end
  end

  member_action :toggle_import, method: :post do
    begin
      service_option = ServiceOption.find_by(id: params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:alert] = "Service option not found."
      redirect_to admin_manage_imports_path and return
    end
    company = Company.find_by_cache(id: params[:company_id])

    company_ids = service_option.company_ids || []
    
    if company_ids.include?(company.id)
      company_ids.delete(company.id)
      flash[:notice] = "Import enabled for #{company.name}"
    else
      company_ids << company.id
      flash[:notice] = "Import disabled for #{company.name}"
    end
    
    service_option.update(company_ids: company_ids)
    
    redirect_to admin_manage_import_path(service_option)
  end

  action_item :back_to_list, only: :show do
    link_to 'Back to List', admin_manage_imports_path, class: 'button'
  end
end
