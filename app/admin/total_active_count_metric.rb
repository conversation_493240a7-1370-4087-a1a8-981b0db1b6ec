ActiveAdmin.register_page "Total Active Counts" do
  menu parent: "Metrics", label: "Total Active Counts", priority: 1
  breadcrumb do
    ['admin', 'metrics']
  end

  controller do
    include ActionView::Helpers::NumberHelper
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]

    def index
      active_companies = Company.active_companies

      @users_count = active_users_count active_companies
      @companies_count = active_companies_count active_companies
      active_companies_agents = AgentLocation.left_outer_joins(company: :subscriptions)
                                  .where(active_company_where_condition)
                                  .distinct

      @mac_agents_count = active_mac_agents_count active_companies_agents
      @windows_agent_count = active_windows_agents_count active_companies_agents

      active_companies_licenses = Integrations::App.licensed.left_outer_joins(company: :subscriptions)
                                    .where(active_company_where_condition)

      @licences_count = active_licences_count active_companies_licenses
      @consumed_licenses_count = active_consumed_licenses_count active_companies_licenses

      active_companies_linked_licenses = Integrations::App.licensed.linked.left_outer_joins(company: :subscriptions)
                                          .where(active_company_where_condition)

      @linked_licences_count = active_linked_licences_count active_companies_linked_licenses
      @linked_consumed_licenses_count = active_linked_consumed_licenses_count active_companies_linked_licenses

      @vendors_count = active_companies "Vendor"
      @contracts_count = active_companies "Contract"
      @help_tickets_count = active_companies "HelpTicket"
      @mobile_devices_count = active_mobile_devices_count
      @company_users_count = active_companies "CompanyMember"
      @managed_assets_count = active_companies "ManagedAsset"
      @probe_locations_count = active_companies "ProbeLocation"
      @transactions_count = active_companies "GeneralTransaction"
      @discovered_assets_count = active_companies "DiscoveredAsset"
    end

    def active_companies_count active_companies
      active_companies.count
    end

    def active_mobile_devices_count
      UserDevice.all.count
    end

    def active_users_count active_companies
      active_companies.joins(:users).distinct.count("users.id")
    end

    def active_windows_agents_count active_companies_agents
      active_companies_agents.where("os ILIKE :search_query", { search_query: "%windows%" }).count
    end

    def active_mac_agents_count active_companies_agents
      active_companies_agents.where("os ILIKE :search_query", { search_query: "%mac%" }).count
    end

    def active_licences_count active_companies_licenses
      number_to_human(active_companies_licenses.sum(:total_users))
    end

    def active_consumed_licenses_count active_companies_licenses
      number_to_human(active_companies_licenses.sum(:used))
    end

    def active_linked_licences_count active_companies_linked_licenses
      number_to_human(active_companies_linked_licenses.sum(:total_users))
    end

    def active_linked_consumed_licenses_count active_companies_linked_licenses
      number_to_human(active_companies_linked_licenses.sum(:used))
    end

    def active_companies class_name
      class_name.constantize.left_outer_joins(company: :subscriptions).where(active_company_where_condition).count
    end

    def active_company_where_condition
      "subscriptions.company_id = companies.id
      AND subscriptions.end_date IS NULL
      OR (CAST (TO_CHAR(companies.created_at, 'J') AS INTEGER) + free_trial_days) - CAST(TO_CHAR(now(), 'J') AS INTEGER) > 0
      AND (subscriptions.module_type ILIKE 'full_platform' OR subscriptions.module_type ILIKE 'asset_management')
      AND (subscriptions.status = 0 OR (subscriptions.status = 1 AND subscriptions.end_date >= CURRENT_DATE))"
    end
  end

  content title: "Total Active Counts" do
    render partial: "admin/metrics/total_active_counts_metric"
  end
end
