class Integrations::Meraki::SaveClientsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include MerakiSyncData
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'long_integrations'

  def perform(clients_info, credential_id, first_time_flag, is_resyncing = false, log_id = nil, company_user_id = nil)
    @asset_count = 0
    @log_id = log_id
    @company_user_id = company_user_id
    @clients_info = clients_info
    @starting_time = Time.current

    set_read_replica_db do
      @meraki_config = Integrations::Meraki::Config.find(credential_id)
      @current_company = @meraki_config.company
      @company_integration = @meraki_config.company_integration
      @intg_id = Integration.find_by_name("meraki").id
    end

    @import_target = @meraki_config.import_type
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    save_clients
    intg_duration_analysis(@current_company.id, first_time_flag)
    create_or_update_asset_history_log('Meraki')
  end

  def save_clients
    @clients_info.each do |client_info|
      retries = 0
      begin
        discovered_asset = nil
        managed_asset = nil
        display_name = client_info["description"] || client_info["mac"].try(:upcase) || client_info["ip"]
        ip_address = client_info["ip"]
        os = client_info["os"]
        manufacturer = client_info["manufacturer"]
        mac_addresses = []
        device_type = nil
        mac_addresses << client_info["mac"].try(:upcase) if mac_address_valid? client_info["mac"].try(:upcase)
        @asset_device_type = :client
        last_check_in_time = client_info['lastSeen']

        discovered_asset = find_discovered_asset(@current_company, false, nil, mac_addresses, display_name, ip_address, manufacturer, false)

        if discovered_asset.blank? && ip_address.present? && mac_addresses.blank? && manufacturer.blank?
          set_read_replica_db do
            next if @current_company.discovered_assets.find_by(ip_address: ip_address)
          end
        end

        if discovered_asset.blank?
          discovered_asset = DiscoveredAsset.new(company_id: @current_company.id)
        end

        display_name_data = display_name.present? ? display_name : discovered_asset.display_name
        mac_addresses_data = mac_addresses.present? ? mac_addresses : discovered_asset.mac_addresses
        ip_data = ip_address.present? ? ip_address : discovered_asset.ip_address
        os_data = os.present? ? os : discovered_asset.os
        manufacturer_data = manufacturer.present? ? manufacturer : discovered_asset.manufacturer
        is_lower_precedence = !is_higher_precedence?(**precedence_data(discovered_asset))
        discovered_asset.assign_attributes(
          display_name: display_name_data,
          mac_addresses: mac_addresses_data,
          ip_address: ip_data,
          asset_type: asset_type(client_info),
          os: os_data,
          manufacturer: manufacturer_data != "Intel" ? manufacturer_data : " ",
          source: "meraki",
          integrations_locations_id: client_info["integrations_locations_id"],
          meraki_network_id: client_info["meraki_network_id"],
          discovered_asset_type: @asset_device_type,
          optional_details: client_description(client_info),
          lower_precedence: is_lower_precedence,
          last_check_in_time: last_check_in_time
        )

        if discovered_asset.display_name.present? && discovered_asset.manufacturer.present? &&
        discovered_asset.mac_addresses.present? && discovered_asset.machine_serial_no.present?
          discovered_asset.status = :ready_for_import
        end

        managed_asset = discovered_asset.managed_asset
        discovered_asset.status = :imported if managed_asset.present?

        if managed_asset.blank?
          if mac_addresses_data.present?
            manufc_data  = manufacturer_data != "Intel" ? manufacturer_data : " "
            managed_asset = find_managed_asset(@current_company, nil, mac_addresses_data, display_name_data, manufc_data)

            if managed_asset.present?
              discovered_asset.status = :imported
              discovered_asset.managed_asset_id = managed_asset.id
            end
          else
            discovered_asset.status = :unrecognized
          end
        end
        is_new = discovered_asset.new_record?
        discovered_asset.save!
        @asset_count += 1 if is_new

        asset_source_data = {
          os: client_info["os"],
          display_name: display_name,
          asset_type: device_asset_type(client_info),
          mac_addresses: mac_addresses,
          ip_address: client_info["ip"] ,
          manufacturer: client_info["manufacturer"] != "Intel" ? client_info["manufacturer"] : " ",
          source: "meraki",
          meraki_network_id: client_info["meraki_network_id"],
          discovered_asset_type: @asset_device_type,
        }
        add_source(discovered_asset, asset_source_data)
        discovered_asset.reload

        if @import_target == "managed_asset" && manufacturer_data&.downcase&.include?('meraki') && discovered_asset.managed_asset_id.blank?
          BulkDiscoveredAssetsUpdate.new([], discovered_asset.company, nil).move_to_managed_asset(discovered_asset)
        end
      rescue PG::ConnectionBad => e
        create_or_update_asset_history_log('Meraki')
        retries += 1
        if retries <=3
          ActiveRecord::Base.flush_idle_connections!
          retry
        else
          log_exception(e, discovered_asset)
          break
        end
      rescue Exception => e
        create_or_update_asset_history_log('Meraki')
        log_exception(e, discovered_asset)
        break
      end
    end
  end

  def log_exception(exception, discovered_asset)
    Rails.logger.error(exception)
    Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !exception.message.include?("Managed asset has already been taken")
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, 'save_clients').to_json)
  end
end
