<template>
  <div
    class="clearfix"
    :class="{ 'row': isHelpdeskModule }"
  >
    <sub-menu v-if="isHelpdeskModule" />
    <div
      v-if="loadingStatus"
      class="col mt-5"
    >
      <div class="d-inline-block">
        <h5 class="text-dark d-inline-block mb-0">
          Loading categories...
        </h5>
        <span class="d-inline-block align-top ml-3">
          <sync-loader
            color="#0d6efd"
            size="0.5rem"
            :loading="true"
          />
        </span>
      </div>
    </div>
    <div
      v-else
      class="mt-5"
      :class="{ 'row': !isHelpdeskModule, 'col': isHelpdeskModule }"
    >
      <workspace-settings-banner v-if="isHelpdeskModule" />
      <div class="col-12">
        <div class="row">
          <div
            v-if="isWrite"
            class="col-5"
          >
            <div class="row">
              <h5 class="font-weight-normal text-secondary mb-3">
                Add {{ moduleTitle }} Categories
              </h5>
              <div class="col-12 box box--with-title bg-very-light">
                <div class="box__inner">
                  <div class="input-group mb-3">
                    <input
                      v-model="newCategory"
                      v-validate="'max:30'"
                      type="text"
                      class="form-control"
                      placeholder="New category name (maximum 30 characters)."
                      maxlength="30"
                      data-vv-as="category"
                      data-tc-new-category-name
                      name="newCategory"
                      style="border-right:0;"
                      :class="{ 'is-invalid': errors.has('newCategory') }"
                    >
                    <div class="input-group-append">
                      <button
                        class="btn btn-primary"
                        type="button"
                        :disabled="!newCategory"
                        style="box-shadow:none;"
                        data-tc-btn-add-category
                        @click="addNewCategory"
                      >
                        + Add
                      </button>
                    </div>
                  </div>
                  <div
                    v-show="errors.has('newCategory')"
                    class="form-text text-danger small mb-3"
                  >
                    {{ errors.first('newCategory') }}
                  </div>
                  <div v-if="filteredDefaultCategories.length == 0">
                    <p class="text-muted not-as-small">
                      Genuity default categories will appear here if you remove them from your list
                    </p>
                  </div>
                  <draggable
                    v-model="filteredDefaultCategories"
                    class="w-100"
                    group="columns"
                    v-bind="dragOptions"
                  >
                    <div
                      v-for="category in filteredDefaultCategories"
                      :key="category.id"
                      class="mb-3"
                    >
                      <div
                        class="box py-2 px-3 text-secondary not-as-small active-items"
                        style="box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);"
                      >
                        <div
                          class="d-flex justify-content-between box__inner"
                          :data-tc-categories-name="category.name"
                        >
                          <div>
                            <span class="genuicon-draggable"/>
                            <span>
                              {{ category.name }}
                            </span>
                          </div>
                          <sync-loader
                            v-if="selectedCategories.includes(category.name)"
                            color="#0d6efd"
                            size="0.3rem"
                            :loading="true"
                          />
                          <i
                            v-else
                            v-tooltip="`Put back ${category.name} category`"
                            class="nulodgicon-android-add-circle float-right align-middle clickable h5 mb-0 text-blue-dark"
                            :data-tc-add-category-icon="category.name"
                            @click.stop.prevent="addCategory(category.name)"
                          />
                        </div>
                      </div>
                    </div>
                  </draggable>
                </div>
              </div>
            </div>
          </div>
          <div class="box--with-title col-5 ml-5">
            <h5
              class="font-weight-normal text-secondary mb-3"
              data-tc-current-categories-header
            >
              Current {{ moduleTitle }} Categories
            </h5>
            <div class="col-12 box box--with-title bg-very-light">
              <div class="box__inner">
                <draggable
                  v-model="categories"
                  class="w-100"
                  group="columns"
                  v-bind="dragOptions"
                  @end="handleDragEnd"
                >
                  <div
                    v-for="(category, index) in categories"
                    :key="`current-category-${category.id}-${index}`"
                    class="mb-3 w-100 active-items"
                  >
                    <div
                      class="box py-2 px-3 text-secondary not-as-small"
                      style="box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);"
                      data-tc-category-name
                      :data-tc-view-name="category.name"
                    >
                      <div class="box__inner">
                        <span class="genuicon-draggable"/>
                        <span>
                          {{ category.name }}
                        </span>
                        <i
                          v-if="isWrite && !isUncategorized(category)"
                          v-tooltip="`Remove ${category.name} category`"
                          class="genuicon-minus-circle float-right align-middle clickable h5 mb-0 text-red-dark"
                          :data-tc-remove-category-icon="category.name"
                          @click.stop.prevent="removeCategory(category)"
                        />
                        <i
                          v-else-if="isWrite && isUncategorized(category)"
                          v-tooltip="`Can't remove Uncategorized category`"
                          class="genuicon-ban float-right align-middle h4 mb-0 text-light"
                        />
                      </div>
                    </div>
                  </div>
                </draggable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sweet-modal
      ref="removeAssociationsModal"
      v-sweet-esc
      title="Modify categories"
      :close="clearAssociations"
    >
      <template slot="default">
        <div class="h6 mb-3">
          The following records are connected to this category. Please select a new category for the items below before deleting.
        </div>
        <div v-if="associations.length > 0">
          <div
            v-for="(association, index) in associations"
            :key="`association-${index}`"
          >
            <h6 class="text-secondary mt-5">
              {{ pluralize(association.class, 2) }}
            </h6>
            <hr>
            <div
              v-for="(item, itemIndex) in association.items"
              :key="`item-${itemIndex}`"
              class="mb-2"
            >
              <edit-category
                :categories="categories"
                :item="item"
                :klass="pluralize(association.class, 2)"
              />
            </div>
          </div>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-primary"
        @click.prevent="$refs.removeAssociationsModal.close"
      >
        Done
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import _sortBy from 'lodash/sortBy';
  import pluralize from 'pluralize/pluralize';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapActions, mapGetters, mapMutations } from 'vuex';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import EditCategory from '../company/edit_category.vue';
  import SubMenu from '../help_tickets/settings/sub_menu.vue';
  import workspaceSettingsBanner from '../help_tickets/settings/workspace_settings_banner.vue';

  export default {
    $_veeValidate: {
      validator: "new",
    },
    components: {
      EditCategory,
      SweetModal,
      SyncLoader,
      SubMenu,
      workspaceSettingsBanner,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        categories: [],
        newCategory: null,
        associations: [],
        selectedCategories: [],
        dragOptions: {
          animation: 200,
          group: 'columns',
        },
      };
    },
    computed: {
      ...mapGetters(['loadingStatus']),
      ...mapGetters('GlobalStore', ['defaultCategories']),
      filteredDefaultCategories: {
        get() {
          return this.defaultCategories.filter(category => !this.categories.map(c => c.name).includes(category.name));
        },
        set(value) {
          if (value.length > this.filteredDefaultCategories.length) {
            const newObject = value.find(
              newItem => !this.filteredDefaultCategories.some(
                existingItem => existingItem.name === newItem.name
              )
            );
            if (newObject) {
              this.removeCategory(newObject);
            }
          }
        },
      },
      addedCategories: {
        get() {
          return this.categories;
        },
        set(value) {
         if (value.length > this.categories.length) {
            const newObject = value.find(
              newItem => !this.categories.some(
                existingItem => existingItem.name === newItem.name
              )
            );
            if (newObject) {
              this.addCategory(newObject.name);
            }
          }
        },
      },
      isHelpdeskModule() {
        return this.moduleName === 'HelpTicket';
      },
      currentModuleName() {
        return this.isHelpdeskModule ? 'helpdesk_categories' : 'categories';
      },
      moduleTitle() {
        return this.isHelpdeskModule ? 'Help Desk' : 'Company';
      },
    },
    methods: {
      ...mapMutations(['setLoadingStatus']),
      ...mapActions('GlobalStore', ['fetchDefaultCategories']),

      onWorkspaceChange() {
        this.fetchDefaultCategories();
        this.fetchCategories();
      },
      handleDragEnd() {
        const updatedPositions = this.categories.map((category, index) => ({
          id: category.id,
          position: index + 1,
        }));

        http
          .post('/categories/update_categories_position', { categories: updatedPositions })
          .then((response) => {
            this.categories = this.categories.map((category, index) => ({
              ...category,
              position: index + 1,
            }));
            this.emitSuccess(response.data.message);
          })
          .catch(() => {
            this.emitError("Sorry, there was an error while updating the position of categories");
          });
      },
      pluralize(str, count) {
        return pluralize(str, count);
      },
      clearAssociations() {
        this.associations = [];
      },
      isUncategorized(category) {
        return category.name === "Uncategorized";
      },
      addNewCategory() {
        this.$validator.validateAll().then(result => {
          if (result) {
            this.addCategory(this.newCategory);
            return;
          }
          this.emitError('Please correct the highlighted errors before adding category.');
        });
      },
      addCategory(categoryName) {
        this.selectedCategories.push(categoryName);
        http
          .post(`/${this.currentModuleName}.json`, { name: categoryName })
          .then(res => {
            this.selectedCategories = [];
            this.emitSuccess(`Successfully added ${categoryName} to your categories`);
            this.categories = res.data.categories;
            this.newCategory = null;
          })
          .catch(error => {
            this.selectedCategories = [];
            this.emitError(`Sorry, there was an error submitting your new category. ${error.response.data.message}`);
          });
      },
      fetchCategories() {
        http
          .get(`/${this.currentModuleName}.json`)
          .then(res => {
            this.categories = _sortBy(res.data, 'position');
            this.setLoadingStatus(false);
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error fetching company categories. Please refresh the page and try again.`);
          });
      },
      removeCategory(category) {
        if (category.name === "Uncategorized") {
          this.emitError('Sorry, this category cannot be removed');
          return;
        }
        http
          .delete(`/${this.currentModuleName}/${category.id}.json`)
          .then(res => {
            if (res.data.associations) {
              this.associations = res.data.associations;
              this.$refs.removeAssociationsModal.open();
            } else {
              this.emitSuccess(`Successfully removed ${category.name} from your categories`);
              this.categories = res.data.categories;
            }
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error removing this category. Please refresh the page and try again. ${error.response.data.message}`);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .active-items {
    cursor: move;
    .box {
      padding-left: 1.5rem;
    }
  }
</style>
