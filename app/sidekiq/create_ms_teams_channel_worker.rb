require 'httparty'

class CreateMsTeamsChannelWorker
  include Sidekiq::Job
  include MsTeamsOptions
  include Utilities::Domains
  include CustomFieldActivityHelper
  include MsTeamsHelper

  sidekiq_options queue: 'critical'

  attr_accessor :ticket, :company, :guest_or_company_user, :help_ticket_data, :config, :team_id, :teams_user_id

  def perform(config_id, guest_or_company_user_id, help_ticket_data, team_id, teams_user_id)
    @config = Integrations::MsTeams::Config.find_by_id(config_id)
    return if config.nil?
    
    @team_id = team_id
    @teams_user_id = teams_user_id
    @help_ticket_data = help_ticket_data    
    @guest_or_company_user = CompanyUser.find_by_id(guest_or_company_user_id) || Guest.find_by_id(guest_or_company_user_id)
    @company = guest_or_company_user.company
    @ticket = company.help_tickets.find(help_ticket_data['help_ticket_id'])
    
    ticket_contributors = Contributor.where(id: [ticket.creator_contributors_ids, ticket.followers, ticket.assigned_users].flatten)
    sanitized_names = ticket_contributors.map { |contributor| sanitize_teams_channel_name(contributor.name) }
    # This won't create channel immediatly we have to ping the resource with location url of that resource
    response = graph_client.create_channel(new_channel_name(sanitized_names), format_member(teams_user_id, 'owner'))

    location_url = response.headers['location']
    new_channel_id = check_channel_status(location_url)
    ticket_related_teams_members(ticket_contributors).each do |member_id|
      graph_client.add_member_to_channel(member_id, new_channel_id)
    end
    
    channel_info = graph_client.fetch_channel_info(new_channel_id)
    subscription_info =  graph_client.create_channel_subscription(channel_info['id'])
    new_channel_config = create_new_channel_config(channel_info, subscription_info)

    # Removing the ticket so that user won't get messages if that ticket is being moved to new channel
    bot_config = company.ms_teams_configs.where('bot_ticket_ids @> ARRAY[?]::integer[]', ticket.id).first
    if bot_config
      bot_config.bot_ticket_ids.delete(ticket.id)
      bot_config.save!
    end
    # For Admin Channel
    message = "Channel has been created successfuly you can access it here [here](#{new_channel_config.generated_channel_link})"
    ms_teams_client(config).call('channel_created', { savedConversationId: help_ticket_data['saved_conversation_id'], message: message })
    
    # For Bot's DM
    message = "A Genuity agent is inviting you to discuss your query '#{ticket.ticket_number}-#{ticket.subject}' click here [here](#{new_channel_config.generated_channel_link}) to join the channel"
    con = company.ms_teams_configs.find_by(channel_id: help_ticket_data['saved_conversation_id'].split(";")[0])
    ms_teams_client(con).call('channel_created', { message: message })
  rescue => e
    error = JSON.parse(e.message) rescue {}
    if error['error_description']&.include?("missing service principal in the tenant")
      send_access_require_message
      return
    end
    Logs::ApiEvent.create(error_event_params(e, config.to_json, "ms_teams_integration"))
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production? 
  end

  def send_access_require_message
    ms_teams_client(config).call('access_require', { savedConversationId: help_ticket_data['saved_conversation_id'] })
  end

  def graph_client
    @client ||= Integrations::MsTeams::GraphClient.new(ms_teams_config: config, team_id: team_id)
  end

  def ms_teams_client(config)
    Integrations::MsTeams::Client.new(config)
  end

  def create_new_channel_config(channel_info, subscription_info)
    new_channel_config = config.dup
    new_channel_config.channel_name = channel_info['displayName']
    new_channel_config.channel_id = channel_info['id']
    new_channel_config.generated_channel_link = channel_info['webUrl']
    new_channel_config.subscription_id = subscription_info['id']
    new_channel_config.subscription_expiry_date = subscription_info['expirationDateTime']
    new_channel_config.authorized_config_id = config.id
    new_channel_config.skip_automated_tasks_creation = true
    new_channel_config.save
    new_channel_config
  end

  def sanitize_teams_channel_name(name)
    name = name.downcase  
    name = name.gsub(/[^a-z0-9\-_]/, '-')
    name = name.gsub(/-{2,}/, '-')
    name.gsub(/^-+/, '').gsub(/-+$/, '')
  end

  def new_channel_name(names)
    channel_name = "#{ticket.ticket_number}-#{names.join('_')}-#{ticket.id}"
    return channel_name if channel_name.length < 45

    sanitized_subject = sanitize_teams_channel_name(ticket.subject)[0, 20]
    
    "#{ticket.ticket_number}-#{sanitized_subject}-#{ticket.id}"
  end

  def ticket_related_teams_members(ticket_contributors)
    contributor_emails = ticket_contributors.map(&:email)
    contributor_emails.map do |email|
      response = graph_client.get_user_by_email(email)
      user_id = response.parsed_response['value'].first['id']
      user_id if user_id && user_id != teams_user_id
    end.compact
  end
  
  def check_channel_status(location_url)
    max_retries = 5
    sleep_duration = 2
    
    max_retries.times do
      status_response = graph_client.fetch_request_status(location_url)
      case status_response['status']
      when 'succeeded'
        # this returns new channel id
        return JSON.parse(status_response['Value'])['ThreadId']
      when 'inProgress' || 'notStarted'
        sleep(sleep_duration)
      else
        raise StandardError, status_response
      end
    end
  
    raise StandardError, "Channel creation timed out after #{max_retries} attempts"
  end

  def format_member(member_id, role)
    [
      {
        "@odata.type": "#microsoft.graph.aadUserConversationMember",
        "roles": [role],
        "<EMAIL>": "https://graph.microsoft.com/v1.0/users/#{member_id}"
      }
    ]
  end
end
