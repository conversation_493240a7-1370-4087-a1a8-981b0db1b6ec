class CreateMsTeamsCommentWorker
  include Utilities::Domains
  include Sidekiq::Job

  sidekiq_options queue: 'critical'

  def perform(team_id, channel_id, message_id)
    config = ::Integrations::MsTeams::Config.find_by(channel_id: channel_id, active: true)
    return if config.nil?

    graph_client = ::Integrations::MsTeams::GraphClient.new(ms_teams_config: config, team_id: team_id)
    response = graph_client.get_chat_message(channel_id, message_id)
    message = response['body']['content']
    ticket = HelpTicket.find_by_id(config.channel_name[/\d+$/].to_i)
    response = graph_client.fetch_user_email(response['from']['user']['id'])
    user_email = response['email'] || response['userPrincipalName']
    user = User.where("lower(email) = lower(?)", user_email).first
    company_user = CompanyUser.find_by(user_id: user) || Guest.find_by_id(user_id: user)
    # Although the outsiders are not allowed to create tickes but this is just on safe side as we had email
    creator = company_user&.contributor&.id || user_email 
    HelpDesk::CommentCreate.new(ticket, comment_params(message, creator)).call
  rescue => e
    Logs::ApiEvent.create(error_event_params(e, config.to_json, "ms_teams_integration"))
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end
end

def comment_params(message, contributor_id)
  {
    source: :ms_teams,
    comment_body: "<div><!--block-->#{message}</div>",
    contributor_id: contributor_id,
    private_flag: false,
    private_contributor_ids: [],
  }
end
