ActiveAdmin.register Company, as: "Company Integrations" do
  menu parent: 'Metrics', label: "Company Integrations", priority: 7
  breadcrumb do
    ['admin', 'metrics']
  end
  filter :name_or_subdomain_contains, :as => :string
  filter :integrations_name, :as => :select, :collection => lambda { Integration.all.map { |integ| [integ.name.titleize, integ.name]} }

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
    around_action :set_read_replica_db, only: [:index, :show]
  end

  index do
    selectable_column
    id_column

    column ("Name"), sortable: :name do |company| 
      "#{company.name}"
    end

    column "Plaid" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "PLaidAccount").present? ? "Yes" : "No"
    end

    column "Quickbooks" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Quickbooks::Config").present? ? "Yes" : "No"
    end

    column "Xero" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Xero::Config").present? ? "Yes" : "No"
    end

    column "Google Assets" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::GoogleAssets::Config").present? ? "Yes" : "No"
    end

    column "Gsuite" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Gsuite::Config").present? ? "Yes" : "No"
    end

    column "GsuiteAd" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::GsuiteAd::Config").present? ? "Yes" : "No"
    end

    column "Jamf Pro" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::JamfPro::Config").present? ? "Yes" : "No"
    end

    column "Kaseya" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Kaseya::Config").present? ? "Yes" : "No"
    end

    column "Meraki" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Meraki::Config").present? ? "Yes" : "No"
    end

    column "Microsoft" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Microsoft::Config").present? ? "Yes" : "No"
    end

    column "Ms Intune Assets" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::MsIntuneAssets::Config").present? ? "Yes" : "No"
    end

    column "Salesforce" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Salesforce::Config").present? ? "Yes" : "No"
    end

    column "Aws" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Aws::Config").present? ? "Yes" : "No"
    end

    column "Azure" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Azure::Config").present? ? "Yes" : "No"
    end

    column "Azure Assets" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::AzureAssets::Config").present? ? "Yes" : "No"
    end

    column "Azure AD" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::AzureAd::Config").present? ? "Yes" : "No"
    end

    column "Azure AD Assets" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::AzureAdAssets::Config").present? ? "Yes" : "No"
    end

    column "Bill" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Bill::Config").present? ? "Yes" : "No"
    end

    column "Okta" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Okta::Config").present? ? "Yes" : "No"
    end

    column "OneLogin" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::OneLogin::Config").present? ? "Yes" : "No"
    end

    column "Netsuite" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Netsuite::Config").present? ? "Yes" : "No"
    end

    column "Sage Intacct" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::SageIntacct::Config").present? ? "Yes" : "No"
    end

    column "Sage Accounting" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::SageAccounting::Config").present? ? "Yes" : "No"
    end

    column "Ubiquity" do |company|
      CompanyIntegration.where(company_id: company.id, integrable_type: "Integrations::Ubiquity::Config").present? ? "Yes" : "No"
    end
  end
end
