class CreateMsTeamsTicketWorker
  include Utilities::Domains
  include Sidekiq::Job
  include MsTeamsHelper

  sidekiq_options queue: 'critical'
  attr_accessor :company, :config

  def perform(config_id, scoped_company_user_id, help_ticket_data, workspace_id = nil, is_dm_ticket = nil)
    scoped_company_user = CompanyUser.find_by_cache(id: scoped_company_user_id) if scoped_company_user_id.present?
    @config = ::Integrations::MsTeams::Config.find_by_id(config_id)
    @company = config.company
    workspace = company.workspaces.find_by_id(workspace_id) || config.workspace
    ticket = HelpDesk::SimpleEntity.new('helpdesk', company, scoped_company_user, workspace: workspace, source: :ms_teams).create_entity(help_ticket_data)
    if is_dm_ticket
      config.bot_ticket_ids << ticket.id
      config.save!
    end
  rescue => e
    Logs::ApiEvent.create(error_event_params(e, config.to_json, "ms_teams_integration"))
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end
end
