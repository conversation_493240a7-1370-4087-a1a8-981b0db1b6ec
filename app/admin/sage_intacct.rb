ActiveAdmin.register Webhooks::Integrations::SageIntacct do
  menu parent: 'Logs', label: "Sage Intacct", priority: 5
  breadcrumb do
    ['admin', 'logs']
  end
  config.filters = false

  actions :all, except: [:new, :create, :edit]

  index do
    selectable_column
    id_column
    column :response
    column ("created_at") { |company| "#{company.created_at.to_written_time}" }
    column ("updated_at") { |company| "#{company.updated_at.to_written_time}" }
    column :tenant
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
