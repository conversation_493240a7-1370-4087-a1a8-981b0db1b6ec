<template>
  <Teleport to="body">
    <div class="custom-sweet-modal" />
    <sweet-modal
      ref="modal"
      v-sweet-esc
      title="Important Note"
      width="50%"
    >
      <template slot="default">
        <div class="mb-4 alert p-3 clearfix d-inline-block position-relative alert-basic-danger w-100 text-left">
          <div class="readable-length d-inline-block align-middle">
            <h4 class="mb-0">
              <span class="not-as-big mb-1 d-inline-block align-bottom">
                Note:
              </span>
            </h4>
            <p class="mb-0 not-as-small">
              You are removing options that are linked to tickets or automated tasks. Please select a new option from the dropdown below before you proceed.
            </p>
          </div>
        </div>
        <multi-select
          v-model="value"
          label="name"
          placeholder="Select an option"
          :allow-empty="false"
          :multiple="false"
          :options="normalizedOptions"
        />
      </template>
      <template slot="button">
        <button
          class="btn btn-link text-secondary"
          @click.stop="cancelUpdate"
        >
          Cancel
        </button>
        <button
          class="btn btn-link text-danger"
          @click.stop="updateStatus"
        >
          Confirm
        </button>
      </template>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import MultiSelect from "vue-multiselect";

  export default {
    components: {
        SweetModal,
        MultiSelect,
      },
      props: {
        message: {
          type: Boolean,
          default: false,
        },
        updatedOptions: {
          type: Array,
          default: () => [],
        },
      },
      data() {
        return {
          value: {},
        };
      },
      computed: {
        normalizedOptions() {
          return this.updatedOptions.map(
            opt => typeof opt === 'string' ? { name: opt } : opt
          );
        },
      },
    methods: {
      open() {
        this.$refs.modal?.open();
      },
      cancelUpdate() {
        this.$refs.modal?.close();
      },
      updateStatus() {
        const valueSelected = Object.keys(this.value).length;
        if (!valueSelected) {
          this.emitError("Please select an option");
          return;
        }
        this.$emit('update', { value: this.value });
        this.$refs.modal?.close();
      },
    },
  };
</script>
