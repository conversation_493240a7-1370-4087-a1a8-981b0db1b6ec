<template>
  <div class="form-group mx-auto mt-5">
    <div class="row">
      <div class="col-8">
        <h5 class="font-weight-normal mb-1 h5--responsive">
          Kanban Fields
        </h5>
        <p class="not-as-small text-secondary p--responsive">
          Customize the <strong>Kanban ticket fields</strong> displayed on your tickets.
        </p>
      </div>
      <div class="col-4 text-right pt-1">
        <a
          class="text-secondary"
          href="#"
          role="button"
          @click.prevent="backBtn"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to <strong> Help Tickets</strong></span>
        </a>
      </div>
    </div>
    <custom-kanban-list
      :selected-fields="selectedFields"
      :unselected-fields="unselectedFields"
      kanban-view
    />
  </div>
</template>

<script>
  import permissionsHelper from "mixins/permissions_helper";
  import strings from 'mixins/string';
  import http from 'common/http';
  import CustomKanbanList from 'components/shared/custom_kanban_list.vue';

  export default {
    components: {
      CustomKanbanList,
    },
    mixins: [permissionsHelper, strings],
    props: {
      showPreview: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        drag: false,
        dragOptions: {
          animation: 200,
          ghostClass: "ghost",
        },
        numUnselectedSkeletons: 12,
        numSelectedSkeletons: 5,
        startDragging: false,
        selectedFields: [],
        unselectedFields: [],
      };
    },
    computed: {
      allColumnsTitles() {
        return this.selectedFields.map(col => col.description).concat(this.unselectedFields.map(col => col.description));
      },
    },
    methods: {
      onWorkspaceChange() {
        this.fetchKanbanFields();
      },
      async fetchKanbanFields() {
        http.get('/ticket_kanban_fields')
          .then((response) => {
            this.selectedFields = response.data.selectedFields;
            this.unselectedFields = response.data.unselectedFields;
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error loading kanban fields. ${error}`);
          });
      },
      backBtn() {
        this.$router.push({ path: `/` });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .active-items--movable {
    cursor: move;

    .box {
      padding-left: 1.5rem;
    }
  }

  .ghost {
    opacity: 0.5;
    background-color: #E3F2FD;
  }

  .column--disabled {
    cursor: default;

    .box {
      background-color: $themed-light;
      border-color: $themed-very-fair;
      padding-left: 1rem;
    }

    .genuicon-add-circle,
    .genuicon-minus-circle {
      visibility: hidden;
    }
  }

  .pointer-events--disabled {
    pointer-events: none;
  }

  .genuicon-add-circle,
  .genuicon-minus-circle {
    &:before {
      line-height: 1.5rem;
    }
  }

  .handle {
    cursor: move;
    font-size: 1.375rem;
    left: .375rem;
    letter-spacing: -.25rem;
    position: absolute;
    top: 12%;
  }
</style>
