ActiveAdmin.register Logs::StripeWebhookLog, as: 'stripe_webhook_log' do
  menu parent: 'Logs', label: "Stripe Webhook Logs", priority: 9
  breadcrumb do
    ['admin', 'logs']
  end

  filter :company, as: :select, collection: Company.order("name ASC").map { |comp| [comp.name, comp.id] }
  filter :charge_id, :as => :string
  filter :stripe_id, :as => :string

  actions :index, :show

  scope :all do |events|
    events.all
  end

  scope :errors do |events|
    events.where.not(error_message: nil)
  end

  index do
    id_column
    column ("Event Type") { |event| "#{event.log_event}" }
    column ("Stripe ID") { |event| "#{event.stripe_id}" }
    column ("Charge ID") { |event| "#{event.charge_id}" }
    column ("Company") { |event| "#{event.company&.name}" }
    column ("Created At") { |event| "#{event.created_at.strftime("%b %d, %Y")}" }
  end

  show do
    attributes_table do
      row ("ID") { |event| "#{event.id}" }
      row ("Company") { |event| "#{event.company&.name}" }
      row ("event_type") { |event| "#{event.log_event}" }
      row ("stripe_id") { |event| "#{event.stripe_id}" }
      row ("charge_id") { |event| "#{event.charge_id}" }
      row ("created_at") { |event| "#{event.created_at.strftime("%b %d, %Y")}" }
      row ("updated_at") { |event| "#{event.updated_at.strftime("%b %d, %Y")}" }
      row ("error_message") { |event| "#{event.error_message}" }
      row ("response") { |event| "#{event.response}" }
    end
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
