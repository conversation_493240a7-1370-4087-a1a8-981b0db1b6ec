ActiveAdmin.register CompanyAssociationEventLog do
  menu parent: 'Logs', priority: 11
  breadcrumb do
    ['admin', 'logs']
  end

  filter :parent_company
  filter :child_company
  filter :event_type, as: :select
  filter :status, as: :select
  filter :performed_by, as: :select
  filter :performer_user_email, label: "Performer", as: :select

  actions :all, except: [:new, :edit]

  index do
    selectable_column
    id_column
    column ("Parent Company") { |event| "#{event.parent_company&.name}" }
    column ("Child Company") { |event| "#{event.child_company&.name}" }
    column :event_type
    column ("Status") { |event| "#{event.status}" }
    column :performed_by
    column ("Performer") { |event| "#{event.performer_user_email}" }
    column :created_at
  end

  controller do
    include MultiCompany::GeneralScoping
    
    before_action :verify_super_admin_mfa
  end
end
