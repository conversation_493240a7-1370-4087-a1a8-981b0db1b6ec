class Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketsController < Webhooks::Integrations::Helpdesk::BaseController
  include MsTeamsOptions

  before_action :check_data, only: [:update]
  before_action :ticket, only: [:update]
  before_action :help_ticket, only: [:update]
  before_action :set_privilege_name, only: [:update]
  before_action :authorize_action, only: [:update], if: :is_authorization_required?
  rescue_from Exceptions::AccessDenied, with: :no_access

  def create
    initiate_ms_teams_ticket_creation

    res = { message: 'Ticket creation has been initiated.' }
    log_event(res, 'create', :success)
    render json: res, status: :ok
  end

  def update
    initiate_ms_teams_ticket_updation
    response_message = is_any_comment_present? ? 'Comment is being added...' : 'Ticket is being updated...'
    ticket_details = ticket_json
    ticket_details[:card_description] = help_ticket_data['card_description']

    res = { message: response_message, help_ticket: ticket_details }
    log_event(res, 'create', :success)
    render json: res, status: :ok
  end

  private

  def check_data
    unless is_data_present?
      render json: { message: 'Please enter any text to add a comment.' }, status: :ok
    end
  end

  def ticket
   @help_ticket = HelpTicket.find(params[:help_ticket][:help_ticket_id])
  rescue ActiveRecord::RecordNotFound => e
    res = { message: 'Sorry, no help ticket found to perform this action.' }
    log_event(res, 'ticket', :error, {}, e)
    render json: res, status: :ok
  end

  def set_privilege_name
    params[:privilege_name] = 'HelpTicket'
  end

  def is_any_comment_present?
    params[:help_ticket][:comment].present? || params[:help_ticket][:private_comment].present?
  end

  def update_value(field, value)
    @previous_value = nil
    @new_value = nil
    value_update = CustomForms::ValueUpdate.new(help_ticket, scoped_company_user)
    value_params = { name: field.name, value: value }
    if !value_update.already_exists?(value_params) && value_update.call(value_params)
      @previous_value = value_update.p_value.presence
      form_value = value_update.form_value
      @new_value = form_value
      create_activity

      AutomatedTasks::EventRouting.perform_async(@new_value.id,
                                                 @new_value.class.name,
                                                 @new_value.attributes.as_json)

      "#{field.label} to #{field_value(field.name)}"
    elsif value_update.form_value
      "#{field.label} to #{field_value(field.name)}"
    end
  end

  def is_data_present?
    params[:help_ticket].keys.any? { |key| ['comment', 'private_comment', 'assigned_to', 'status', 'priority'].include?(key) }
  end

  def find_help_ticket
    scoped_company.help_tickets.find(params[:id])
  end

  def initiate_ms_teams_ticket_creation
    CreateMsTeamsTicketWorker.perform_async(@config.id, scoped_company_user&.id, new_help_ticket_data.as_json)
  end

  def initiate_ms_teams_ticket_updation
    UpdateMsTeamsTicketWorker.perform_async(@config.id, guest_or_company_user_id, help_ticket_data.as_json);
  end

  def authorize_action
    return no_access if current_user.blank?
    authorize_write(ticket)
  end

  def no_access
    res = { message: 'Sorry, you are not authorize to perform this action.' }
    log_event(res, 'no_access', :success)
    render json: res, status: :ok
  end

  def new_help_ticket_data
    data = help_ticket_data
    creator = scoped_company_user.present? ? scoped_company_user.contributor_id : current_user_email
    data['created_by'] = creator
    data
  end

  def help_ticket_data
    data = params[:help_ticket].as_json
    data['assigned_to'] = data['assigned_to'].split(',').map(&:to_i) if data['assigned_to']
    data
  end

  def custom_form
    help_ticket.custom_form
  end

  def integration
    'MsTeams'
  end

  def guest_or_company_user_id
    scoped_company_user&.id || guest.id
  end

  def is_authorization_required?
    return true unless is_any_comment_present? && scoped_company_user.blank? && guest.present?
    !linked_contributor_ids(ticket).include?(guest.contributor_id)
  end
end
