<template>
  <div class="settings-layout">
    <div class="settings-sidebar">
      <sub-menu />
    </div>
    <div class="settings-content">
      <router-view />
    </div>
  </div>
</template>

<script>
import SubMenu from './sub_menu.vue';

export default {
  components: { SubMenu },
};
</script>

<style lang="scss" scoped>
.settings-layout {
  display: flex;
  height: 80vh;
}

.settings-sidebar {
  overflow-y: auto;
  scroll-behavior: smooth;
}

.settings-content {
  flex: 1 1 0%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.settings-sidebar, .settings-content {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
} 
</style>
