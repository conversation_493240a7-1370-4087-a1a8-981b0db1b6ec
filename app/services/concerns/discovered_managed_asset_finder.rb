module DiscoveredManagedAssetFinder
  extend ActiveSupport::Concern
  include ReadReplicaDb

  # included doDiscovery Logic:

  # Serial Number Priority: If serial exists, find by serial first
  # MAC Address Matching: Exact MAC array match, then partial matches
  # Cross-validation: If found by MAC but has different serial, reject
  # Fallback to Name: If no serial/MAC match, try display name
    def find_discovered_asset(company, is_probe, serial_number, mac_addresses, display_name, ip_address, manufacturer, is_mac_duplicate)
      set_read_replica_db do
        @company = company
        @mac_addresses = mac_addresses
        @ip_address = ip_address
        @display_name = display_name
        @manufacturer = manufacturer
        discovered_asset = nil
        recog_dis_assets = @company.discovered_assets.where.not(status: :unrecognized)

        # If serial number is present then try to find D.Asset with it, if not found then try with mac addresses
        # And if serial number is not present than try to find with mac addresses
        # And if neither serial number nor mac addresses is present than try to find with name 
        if serial_number.present?
          discovered_asset = @company.discovered_assets.find_by('lower(machine_serial_no) = ?', serial_number.downcase)
          # if not find with serial number than try to find with additional mac address checks
          discovered_asset ||= find_with_exact_mac_checks @company.discovered_assets, @mac_addresses
          discovered_asset ||= find_with_more_mac_checks @company.discovered_assets, @mac_addresses, @manufacturer
          discovered_asset = nil if discovered_asset&.machine_serial_no.present? && discovered_asset.machine_serial_no.downcase != serial_number.downcase
        elsif @mac_addresses.present?
          # if data come from probe than it contain additional flag is_mac_duplicate,
          # And it is true when two or more assets have atleast one same mac address
          if is_probe && is_mac_duplicate
            discovered_asset = already_un_recog
          else
            discovered_asset = find_with_exact_mac_checks recog_dis_assets, @mac_addresses
            if discovered_asset.blank?
              discovered_asset = @company.discovered_assets.find_by("machine_serial_no = ? AND status != ? AND mac_addresses::text[] && ARRAY[?]::text[]","", DiscoveredAsset.statuses[:unrecognized], @mac_addresses)
            end
            discovered_asset ||= find_with_more_mac_checks recog_dis_assets, @mac_addresses, @manufacturer
            if discovered_asset.blank?
              @company.discovered_assets.where('lower(display_name) = ?', @display_name.to_s.downcase).where.not(status: :unrecognized).find_each do |asset|
                @mac_addresses.each do |mac_address|
                  if asset.mac_addresses&.include?(mac_address)
                    discovered_asset = asset
                    break
                  end
                end
                break if discovered_asset.present?
              end
            end
          end
          discovered_asset ||= find_discovered_asset_with_name @display_name
        else
          discovered_asset = find_discovered_asset_with_name @display_name
        end
        discovered_asset
      end
    end

    def find_discovered_asset_for_cloud(company, display_name, source, worker_start_time)
      set_read_replica_db do
        discovered_assets = company.discovered_assets.joins(:asset_sources).where(display_name: display_name, :asset_sources => {source: source})
        discovered_assets.where('discovered_assets.updated_at < ?', worker_start_time)&.first
      end
    end

    # If serial number is present then try to find M.Asset with it
    # And if serial number is not present than try to find with mac addresses
    # And if neither serial number nor mac addresses is present than find with name 
    def find_managed_asset(company, serial_number, mac_addresses, display_name, manufacturer)
      set_read_replica_db do
        @company = company
        managed_asset = nil
        if serial_number.present?
          managed_asset = filtered_assets.find_by('lower(machine_serial_number) =?', serial_number.downcase)
          # if not find with serial number than try to find with additional mac address checks
          managed_asset ||= find_with_exact_mac_checks filtered_assets, mac_addresses
          managed_asset ||= find_with_more_mac_checks filtered_assets, mac_addresses, manufacturer
          managed_asset = nil if managed_asset&.machine_serial_number.present? && managed_asset.machine_serial_number.downcase != serial_number.downcase
        elsif mac_addresses.present?
          managed_asset = find_with_exact_mac_checks filtered_assets, mac_addresses
          if managed_asset.blank?
            filtered_assets.where(machine_serial_number: [nil, '']).find_each do |asset|
              mac_addresses.each do |mac_address|
                if asset.mac_addresses&.include?(mac_address)
                  managed_asset = asset
                  break
                end
              end
              break if managed_asset.present?
            end
          end
          managed_asset ||= find_with_more_mac_checks filtered_assets, mac_addresses, manufacturer
          if managed_asset.blank?
            filtered_assets.where('lower(name) = ?', display_name.to_s.downcase).find_each do |asset|
              mac_addresses.each do |mac_address|
                if asset.mac_addresses&.include?(mac_address)
                  managed_asset = asset
                  break
                end
              end
              break if managed_asset.present?
            end
          end
          managed_asset ||= find_managed_asset_with_name company, display_name
        else
          managed_asset = find_managed_asset_with_name company, display_name
        end
        managed_asset
      end
    end

    def find_agent_probe_location(locations, app_id, serial_number, mac_addresses, computer_name, manufacturer)
      set_read_replica_db do
        location = nil
        location = locations.find_by(system_uuid: app_id) if app_id.present?
        if location.blank? && serial_number.present?
          location = locations.find_by('lower(machine_serial_number) =?', serial_number.downcase)
          # if not find with serial number then try to find with additional mac address checks
          location ||= find_with_exact_mac_checks(locations, mac_addresses)
          location ||= find_with_more_mac_checks(locations, mac_addresses, manufacturer)
          location = nil if location&.machine_serial_number.present? && location.machine_serial_number.downcase != serial_number.downcase
        elsif location.blank? && mac_addresses.present?
          location = find_with_exact_mac_checks(locations, mac_addresses)
          if location.blank?
            locations.where(machine_serial_number: [nil, '']).find_each do |loc|
              mac_addresses.each do |mac_address|
                if loc.mac_addresses&.include?(mac_address)
                  location = loc
                  break
                end
              end
              break if location.present?
            end
          end
          location ||= find_with_more_mac_checks(locations, mac_addresses, manufacturer)
          if location.blank?
            locations.where('lower(computer_name) = ?', computer_name.to_s.downcase).find_each do |loc|
              mac_addresses.each do |mac_address|
                if loc.mac_addresses&.include?(mac_address)
                  location = loc
                  break
                end
              end
              break if location.present?
            end
          end
          location ||= find_location_with_name(locations, computer_name)
        elsif location.blank? && computer_name.present?
          location = find_location_with_name(locations, computer_name)
        end
        location
      end
    end
  end

  # If D.Asset has duplicate mac address(same as other asset) we update his status as unrecognised.
  # We find the asset from unrecognised if asset is_mac_duplicate flag is true 
  def already_un_recog
    discovered_asset = nil
    @company.discovered_assets.unrecognized.where('lower(display_name) = ?', @display_name.to_s.downcase).each do |asset|
      @mac_addresses.each do |mac_address|
        if asset.mac_addresses&.include?(mac_address) && asset.ip_address == @ip_address
          discovered_asset = asset
          break
        end
      end
      break if discovered_asset.present?
    end
    discovered_asset ||= find_with_exact_mac_checks @company.discovered_assets.unrecognized, @mac_addresses
    discovered_asset ||= find_with_more_mac_checks @company.discovered_assets.unrecognized, @mac_addresses, @manufacturer 
    discovered_asset
  end

  def find_with_exact_mac_checks entities, mac_addresses
    entities.find_by(mac_addresses: mac_addresses) if mac_addresses.present?
  end

  # Entities can be DiscovereAsset, ManagedAsset, AgentLocation or ProbeLocation
  # If entity not found with basic serial number check and basic mac addresss check,
  # Then trying to find the entity with additional mac addresses check in which first of all trying to find with
  # exact match of mac adresses array if entity is not find than trying to find with following:
  # If incoming entity have more than one mac addresses then trying to find the D.entity that have atleast two same mac addresses
  # At last trying to find with name if still not found.
  # The major purpose of this method is, if entity is present with much details and next time come with less detials we find easily.
  def find_with_more_mac_checks(entities, mac_addresses, manufacturer)
    entity_find = nil
    if mac_addresses.present? && mac_addresses.count > 1
      entities.where(manufacturer: manufacturer).where("array_length(mac_addresses, 1) > 1").find_each do |entity|
        match_count = 0
        mac_addresses.each do |mac_address|
          if entity.mac_addresses.include?(mac_address)
            match_count += 1
            if match_count > 1
              entity_find = entity
              break
            end
          end
        end
        break if entity_find.present?
      end
    end
    entity_find
  end

  def find_discovered_asset_with_name display_name
    return display_name.present? ? @company.discovered_assets.where(mac_addresses: [], machine_serial_no: "").where("lower(display_name) = ?", display_name.to_s.downcase).first : nil
  end

  def find_managed_asset_with_name company, display_name
    return display_name.present? ? filtered_assets.where('lower(name) = ?', display_name.to_s.downcase).where(mac_addresses: [], machine_serial_number: [nil, ""]).first : nil
  end

  def find_location_with_name(locations, computer_name)
    if computer_name.present?
      locations.where('lower(computer_name) = ?', computer_name.to_s.downcase).where(mac_addresses: [], machine_serial_number: [nil, ""]).first
    end
  end

  def filtered_assets
    # return assets that are not scoped by 'default_scope' and do not include combined assets.
    ManagedAsset.unscoped.where(company_id: @company.id).where.not(is_combined_asset: true)
  end

  # TODO: Needs to remove this check once we add this check to windows side.
  def get_valid_serial(serial_no)
    invalid_serial = ['null', 'to be', 'system serial', 'default', 'not ap'].any? { |str| serial_no&.downcase&.gsub(".", "")&.include? str }
    invalid_serial ? "" : serial_no
  end
end
