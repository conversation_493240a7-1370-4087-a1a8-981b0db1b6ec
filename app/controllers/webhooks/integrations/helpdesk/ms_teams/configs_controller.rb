class Webhooks::Integrations::Helpdesk::MsTeams::ConfigsController < Webhooks::Integrations::Helpdesk::BaseController
  include MsTeamsHelper
  before_action :if_already_linked?, only: [:create]
  before_action :authenticate_user, only: [:create]
  skip_before_action :authenticate_user, only: [:update, :consume]
  skip_before_action :set_resources, only: [:update, :consume]
  skip_before_action :set_current_user, only: [:update, :consume]

  def create
    # TODO: MS teams: remove env check 
    workspace_id = ms_teams_feature_access_pending?(scoped_company.id) ? scoped_company.default_workspace.id : params[:workspace_id]
    ms_teams_config = scoped_company.ms_teams_configs.find_or_initialize_by(team_id: team_id, channel_id: channel_id, workspace_id: workspace_id)
    ms_teams_config.assign_attributes(team_name: team_name, channel_name: channel_name, active: true, tenant_id: tenant_id )

    if ms_teams_config.save
      config_workspace = Workspace.find_by(id: params[:workspace_id])
      res = { subdomain: company_subdomain, workspace: config_workspace&.name }
      log_event(res, 'create', :success, { company_id: scoped_company.id })
      render json: res, status: :ok
    else
      res = { message: 'An error occured while linking your channel.' }
      log_event(res, 'create', :error, { company_id: scoped_company.id })
      render json: res, status: :bad_request
    end
  rescue => e
    res = { message: 'An error occured while linking your channel.' }
    log_event(res, 'create', :error, { company_id: scoped_company&.id }, e)
    render json: res, status: :unprocessable_entity
  end

  def show
    res = { subdomain: scoped_company.subdomain }
    log_event(res, 'show', :success)
    render json: res, status: :ok
  end

  def destroy
    ms_teams_config = ::Integrations::MsTeams::Config.find_by(channel_id: channel_id, team_name: team_name)

    if ms_teams_config&.destroy
      res = { message: 'This channel is successfully unlinked.' }
      log_event(res, 'destroy', :success)
      render json: res, status: :ok
    else
      res = { message: 'An error occured while unlinking your channel.' }
      log_event(res, 'destroy', :error)
      render json: res, status: :unprocessable_entity
    end
  end

  def update
    config = ::Integrations::MsTeams::Config.find_by_id(params['id'].to_i)
    if params['is_bot_installed']
      data = if config.active && config.update(active: false)
              unlink_child_configs(config)
              { is_app_deacitvated: true }
             elsif !config.active && config.update(active: true)
              link_child_configs(config)
              { is_app_activated: true }
             else
               { message: 'Sorry, something went wrong. Please try again.' }
             end
      Pusher.trigger("ms_teams=#{config.company.guid}", 'ms_teams_configs', data)
    else
      Pusher.trigger("ms_teams=#{config.company.guid}", 'ms_teams_configs', { message: "It seems like Genuity app is not installed in your Microsoft Teams or connected channel doesn't exist anymore. You can delete this connector instead." })
    end
  end

  def consume
    if params['admin_consent'] == 'True'
      config = Integrations::MsTeams::Config.find_by(tenant_id: params['tenant'], channel_id: params['state'])
      message = "Genuity now has the required permissions to work across your organization. All features are now fully enabled."
      ms_teams_client(config).call('channel_created', { message: message })
    end
    redirect_to 'https://docs.gogenuity.com/docs/ms-teams'
  end

  private

  def ms_teams_client(config)
    Integrations::MsTeams::Client.new(config)
  end

  def unlink_child_configs(config)
    return if ms_teams_feature_access_pending?(config.company.id)

    other_parent_configs = config.company.ms_teams_configs
                                 .where.not(id: config.generated_configs + [config.id])
                                 .where(authorized_config_id: nil)
    new_parent = nil
    new_parent = other_parent_configs.first if other_parent_configs.exists?
    config.generated_configs.each do |generated_config|
      # If the workspace id is nil it means its BOT DM
      if new_parent.present? && generated_config.workspace_id.nil?
        # Rebind the generated_config to the first available active config in the company so 
        # Handle case where use has multiple workspaces so DMs keep working when enabled again
        generated_config.update(authorized_config_id: new_parent.id)
        next
      end
      generated_config.update(active: false)
    end              
  end

  def link_child_configs(config)
    return if ms_teams_feature_access_pending?(config.company.id)
    config.generated_configs.each do |generated_config|
      generated_config.update(active: true)
    end
  end

  def if_already_linked?
    # MS teams TOOD: check if this won't cause any issue with nil
    config_company = @config&.company
    is_channel_already_linked = @config.present? && config_company.subdomain.eql?(company_subdomain)
    is_workspace_already_linked = ::Integrations::MsTeams::Config.find_by(workspace_id: params[:workspace_id]).present? if !ms_teams_feature_access_pending?(config_company&.id)
    if is_channel_already_linked || is_workspace_already_linked
      res = { message:  is_channel_already_linked ? 'Sorry, this channel is already linked with another company.' : 'Sorry, this workspace is already linked' }
      log_event(res, 'if_already_linked?', :success)
      render json: res, status: :bad_request
    end
  end

  def integration
    'MsTeams'
  end
end
