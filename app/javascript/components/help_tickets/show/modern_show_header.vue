<template>
  <div
    v-if="currentHelpTicket"
    class="mt-2 bg-themed-box-bg table-header"
    :class="{ 
      'w-100': !isQuickView,
      'dismissible-container__sticky-header preview-header quick-view': isQuickView,
    }"
  >
    <div
      v-if="!hideHeader"
      class="d-flex align-items-center font-size-small"
    >
      <span
        class="p-2 rounded-pill bg-white-bold max-height"
      >
        <span>{{ dateTimeFormatting('createdAt')[1] }} • </span>
      </span>
      <div
        v-for="(formField, idx) in headerFields"
        :key="`field-${idx}`"
      >
        <div v-if="formField.label === 'Assigned To' && !isBasicAccess">
          <inline-assigned-dropdown
            :object="assignedToFields(formField)"
            :field-name="'assigned_to'"
            :is-help-ticket-show="true"
            @value-updated="fetchCurrentTicket"
          />
        </div>
        <div v-else-if="formField.label !== 'Assigned To'">
          <field-renderer
            class="rounded ml-1"
            :class="formField.label === 'Status' ? 'bg-green-subtle' : 'pills-bg'"
            :form-field="formField"
            :object="currentHelpTicket"
            :active-dropdown="activeDropdown"
            :is-click-allowed="true"
            :show-label="false"
            :object-class="''"
            :is-help-ticket-show="true"
            @update-field="updateFormField"
            @dropdown-toggled="setActiveDropdown"
          />
        </div>
      </div>
    </div>
    <div
      class="row justify-content-end m-0"
      :class="{ 'align-items-start mb-3 mt-0': isQuickView }"
    >
      <avatar
        class="d-inline-block align-middle mr-3 logo-outline"
        :size="40"
        :src="currentHelpTicket.avatar.avatarThumbUrl"
        :username="currentHelpTicket.avatar.name"
      />
      <div 
        class="col p-0"
        :class="{ 'pt-1' : !isQuickView }"
      >
        <div class="d-inline font-weight-bold">
          <div :class="{ 'mr-2': isQuickView }">
            <field-renderer
              v-if="subjectField"
              :form-field="subjectField"
              :value="subjectValues"
              :object="ticket"
              :show-label="false"
              :is-quick-view="isQuickView"
              :is-help-ticket-show="true"
              class="ml-1"
              object-class="ml-1"
              :class="{ 'pr-4': !isQuickView }"
              @edit="toggleSubjectEdit"
              @refresh="refreshTicketAndActivities"
            />
          </div>
          <div
            v-if="!hideHeader"
            :class="{ 'mr-2': isQuickView }"
          >
            <field-renderer
              v-if="descriptionField"
              :form-field="descriptionField"
              :value="descriptionValues"
              :object="ticket"
              :show-label="false"
              :is-quick-view="isQuickView"
              :is-help-ticket-show="true"
              class="ml-1"
              object-class="ml-1"
              @refresh="refreshTicketAndActivities"
            />
          </div>
        </div>
      </div>

      <div
        v-if="!isQuickView"
        class="col-auto text-muted p-0"
      >
        <div class="d-flex align-items-center">
          <div
            v-if="!isSplitPaneView"
            class="d-inline-block position-relative align-top"
          >
            <a
              v-if="!navigatedFromParent"
              class="text-secondary mr-4"
              @click="redirectBack"
            >
              <i class="nulodgicon-arrow-left-c white mr-2" />
              <span class="p--responsive">Back to <strong>all tickets</strong></span>
            </a>
            <div
              v-else
              v-click-outside="closeDropdown"
              class="dropdown"
            >
              <a
                id="dropdownMenuButton"
                class="text-secondary mr-4"
                role="button"
                @click="toggleDropdown"
              >
                <i class="nulodgicon-arrow-left-c white mr-2" />
                <span class="p--responsive">Back to</span>
              </a>
              <div
                v-if="showDropdown"
                class="dropdown-menu show"
                aria-labelledby="dropdownMenuButton"
              >
                <a
                  class="dropdown-item"
                  @click="redirectBack"
                >
                  All tickets
                </a>
                <a
                  class="dropdown-item"
                  @click="redirectToParent"
                >
                  Parent company tickets
                </a>
              </div>
            </div>
          </div>
          <div
            v-if="currentHelpTicket.archived"
            class="d-inline-block"
          >
            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Delete', classes: 'custom-tooltip' }"
              aria-label="Delete"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              @click.stop.prevent="openDeleteConfirmationPopup"
            >
              <i class="nulodgicon-trash-b" />
            </a>

            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Unarchive', classes: 'custom-tooltip' }"
              aria-label="Unarchive"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              @click.stop.prevent="$refs.unArchiveModal.open"
            >
              <i class="nulodgicon-unarchive" />
            </a>
          </div>
          <div
            v-else
            class="d-inline-block"
          >
            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Archive', classes: 'custom-tooltip' }"
              aria-label="Archive"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              @click.stop.prevent="$refs.archiveModal.open"
            >
              <i class="nulodgicon-archive" />
            </a>
            <a
              v-if="isWrite"
              v-tooltip="{ content: currentHelpTicket.muteNotification ? 'Unmute' : 'Mute', classes: 'custom-tooltip' }"
              :aria-label="currentHelpTicket.muteNotification ? 'Unmute' : 'Mute'"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView, [notificationStatus]: true }"
              role="button"
              @click.stop.prevent="$refs.notificationsModal.open"
            >
              <i :class="notificationIcon" />
            </a>
            <a
              v-if="isWrite || isScoped"
              v-tooltip="{ content: 'Print', classes: 'custom-tooltip' }"
              aria-label="Print"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              @click.stop.prevent="printCurrentTicket"
            >
              <i class="nulodgicon-file-o" />
            </a>
            <a
              v-tooltip="{ content: 'More Actions', classes: 'custom-tooltip' }"
              v-click-outside="closeQuickViewActions"
              aria-label="More actions"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              @click.stop.prevent="toggleQuickViewActions"
            >
              <i class="genuicon-ellipsis-v text-secondary d-inline-block mt-0.5" />
            </a>
          </div>
        </div>

        <div
          class="dropdown-menu split-view-action-dropdown not-as-small"
          :class="{'show mr-n3': showQuickViewActions && isSplitPaneView}"
        >
          <div v-if="currentHelpTicket.archived && isQuickView">
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.unArchiveModal.open"
            >
              <i class="nulodgicon-unarchive mr-2 small" />Unarchive ticket
            </div>
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="openDeleteConfirmationPopup"
            >
              <i class="nulodgicon-trash-b mr-2 small" />Delete ticket
            </div>
          </div>
          <div v-else-if="isQuickView">
            <div
              v-if="isWrite || isScoped"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="printCurrentTicket"
            >
              <i class="nulodgicon-file-o mr-2 small" />Print ticket
            </div>
            <div
              v-if="isWrite"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.notificationsModal.open"
            >
              <i class="nulodgicon-bell-o mr-2 small" />{{ currentHelpTicket.muteNotification ? 'Unmute ticket' : 'Mute ticket' }}
            </div>
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.archiveModal.open"
            >
              <i class="nulodgicon-archive mr-2 small" />Archive
            </div>
          </div>
          <div v-if="shouldShowSecondaryTicketActions">
            <div
              v-for="(action, index) in secondaryActionOptions"
              :key="`secondary-action-${index}`"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="action.onClick"
            >
              <i
                class="mr-2 small"
                :class="action.icon"
              />
              {{action.label}}
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="isQuickView"
        class="mr-n2"
      >
        <span
          v-tooltip="'Help Center Ticket'"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm quick-view-edit-btn text-secondary ml-2"
          @click="redirectToTicketShow"
        >
          <i class="mt-1 nulodgicon-external-link small text-muted ml-1 clickable external-link-size" />
        </span>
        <span
          v-tooltip="'More Actions'"
          v-click-outside="closeQuickViewActions"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm text-secondary ml-2 d-inline-flex justify-content-center align-items-center "
          @click.stop.prevent="toggleQuickViewActions"
        >
          <i class="genuicon-ellipsis-v text-secondary pt-1" />
        </span>
        <span
          v-tooltip="'Close Preview'"
          v-click-outside="closeQuickViewActions"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm text-secondary ml-2 d-inline-flex justify-content-center align-items-center "
          @click="$emit('close-quick-view')"
        >
          <i class="nulodgicon-android-close big" />
        </span>
      </div>

      <div
        id="modern-ticket-show-page-dropdown"
        class="dropdown-menu quick-view-action-dropdown not-as-small"
        :class="{
          'show': showQuickViewActions && !isSplitPaneView,
          'mt-4 mr-n3': !isQuickView,
          'dropdown-menu-top': !hideHeader,
          'mt-6':isBasicAccess
        }"
      >
        <span v-if="currentHelpTicket.archived && isQuickView">
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.unArchiveModal.open"
          >
            <i class="nulodgicon-unarchive mr-2 small" />Unarchive ticket
          </div>
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="openDeleteConfirmationPopup"
          >
            <i class="nulodgicon-trash-b mr-2 small" />Delete ticket
          </div>
        </span>
        <span v-else-if="isQuickView">
          <div
            v-if="isWrite || isScoped"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="printCurrentTicket"
          >
            <i class="nulodgicon-file-o mr-2 small" />Print ticket
          </div>
          <div
            v-if="isWrite"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.notificationsModal.open"
          >
            <i class="nulodgicon-bell-o mr-2 small" />{{ currentHelpTicket.muteNotification ? 'Unmute ticket' : 'Mute ticket' }}
          </div>
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.archiveModal.open"
          >
            <i class="nulodgicon-archive mr-2 small" />Archive
          </div>
        </span>
        <div v-if="shouldShowSecondaryTicketActions">
          <div
            v-for="(action, index) in secondaryActionOptions"
            :key="`secondary-action-${index}`"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="action.onClick"
          >
            <i
              class="mr-2 small"
              :class="action.icon"
            />{{action.label}}
          </div>
          <hr
            v-if="!isQuickView"
            class="divider"
          >
        </div>
        <modern-view-toggle v-if="!isQuickView" />
      </div>
    </div>
    <div
      v-if="showAppSessions"
      class="row justify-content-between align-items-center mx-0"
      :class="{'mb-1': !isQuickView, 'mr-n5 pr-0 mb-0 mt-n1': isQuickView}"
    >
      <div
        class="d-flex"
        :class="{'mr-3': isQuickView}"
      >
        <div
          v-if="tasksTotalCount > 0"
          v-tooltip="{ content: isQuickView ? `Tasks: ${tasksCompletedCount}/${tasksTotalCount } completed`: '' }"
          :class="{
            'btn-link smallest text-muted px-2 border-radius-4 mr-2 clickable d-flex align-items-center': !isQuickView, 
            'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
          }"
          @click="openTasksTab"
        >
          <i
            class="genuicon-clipboard-with-check align-middle"
            :class="{ 'mr-1': !isQuickView }"
          />
          <span v-if="!isQuickView">
            <span>Tasks:</span>
            <span class="font-weight-semi-bold">
              <span>{{ tasksCompletedCount }}</span>/<span>{{ tasksTotalCount }} completed</span>
            </span>
          </span>
        </div>
      </div>
    </div>
    <div class="header-toggle-container">
      <hr class="my-3">
      <div 
        v-tooltip="{ content: 'Expand/Collapse header' }"
        class="cursor-pointer align-items-center header-toggle-button"
        @click.stop="toggleHeader"
      >
        <i
          class="header-toggle-button-icon"
          :class="[
            'align-middle',
            hideHeader ? 'genuicon-up-down-arrow' : 'genuicon-up-down-arrow'
          ]"
        />
      </div>
    </div>
    <Teleport to="body">
      <sweet-modal
        ref="unArchiveModal"
        v-sweet-esc
        title="Before you unarchive this ticket..."
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-center">
            <h6 class="mb-3">
              Are you sure you want to unarchive this help ticket?
            </h6>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          @click.stop="$refs.unArchiveModal.close"
        >
          No, keep it.
        </button>
        <button
          slot="button"
          class="btn btn-primary"
          @click.stop="okUnarchive"
        >
          Yes, unarchive it.
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="notificationsModal"
        v-sweet-esc
        :title="`Before you ${notificationStatus} notifications`"
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-left">
            <h6 class="mb-3">
              Are you sure you want to {{ notificationStatus }} all notifications for this help ticket?
            </h6>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          @click.stop="$refs.notificationsModal.close"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-primary text-capitalize"
          @click.stop="toggleMute"
        >
          {{ notificationStatus }}
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="archiveModal"
        v-sweet-esc
        title="Before you archive this ticket..."
        :blocking="disabled"
        :hide-close-button="disabled"
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-left">
            <p class="mb-3">
              Are you sure you want to archive this help ticket? You can unarchive it any time later.
            </p>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary"
          :disabled="disabled"
          @click.stop="$refs.archiveModal.close"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-link text-danger"
          :disabled="disabled"
          @click.stop="okArchive"
        >
          Archive
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="deleteModal"
        v-sweet-esc
        title="Before you delete this ticket..."
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div>
            <warning-message
              v-if="currentHelpTicket"
              ref="warningMessage"
              :entity="currentHelpTicket"
              entity-type="HelpTicket"
            />
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          @click.stop="$refs.deleteModal.close"
        >
          No, keep it.
        </button>
        <button
          slot="button"
          class="btn btn-primary"
          @click.stop="okDelete"
        >
          Yes, delete it.
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <merge-ticket-modal
        ref="mergeTicketModal"
        class="ticket-modal"
      />
    </Teleport>

    <Teleport to="body">
      <move-ticket-modal
        ref="moveTicketModal"
        class="ticket-modal"
        :workspaces="workspaceOptions"
        :forms="forms"
      />
    </Teleport>
    
    <ticket-print
      v-if="printingHelpTicket && currentHelpTicket"
      ref="ticketPrint"
      :is-bulk-printing="false"
    />

    <Teleport to="body">
      <clone-ticket-modal
        ref="cloneTicketModal"
        class="ticket-modal"
        :is-quick-view="isQuickView"
        @load-ticket="refreshTicket"
      />
    </Teleport>
  </div>
</template>

<script>
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import _get from 'lodash/get';
  import http from 'common/http';
  import VueMoment from 'vue-moment';
  import moment from 'moment-timezone';
  import { Avatar } from 'vue-avatar';
  import dates from 'mixins/dates';
  import customForms from 'mixins/custom_forms';
  import multiCompany from 'mixins/multi_company';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import MomentTimezone from 'mixins/moment-timezone';
  import permissions from 'mixins/custom_forms/permissions';
  import helpTickets from 'mixins/help_ticket';
  import pushers from 'mixins/pushers';
  import vClickOutside from 'v-click-outside';
  import WarningMessage from '../../shared/warning.vue';
  import InlineAssignedDropdown from '../../shared/inline_assigned_dropdown.vue';
  import FieldRenderer from '../../shared/custom_forms/renderer.vue';
  import MoveTicketModal from './move_ticket_modal.vue';
  import MergeTicketModal from './merge_ticket_modal.vue';
  import CloneTicketModal from './clone_ticket_modal.vue';
  import TicketPrint from './ticket_print.vue';
  import ModernViewToggle from './modern_view_toggle.vue';

  Vue.use(VueMoment, {
    moment,
  });

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      SweetModal,
      FieldRenderer,
      WarningMessage,
      InlineAssignedDropdown,
      Avatar,
      MoveTicketModal,
      TicketPrint,
      MergeTicketModal,
      CloneTicketModal,
      ModernViewToggle,
    },
    mixins: [
      dates,
      customForms,
      strings,
      permissionsHelper,
      MomentTimezone,
      permissions,
      multiCompany,
      helpTickets,
      pushers,
    ],
    props: {
      isQuickView: {
        type: Boolean,
        default: false,
      },
      appSessions: {
        type: Array,
        default: () => [],
      },
      avatarSession: {
        type: Object,
        default: () => {},
      },
      isSplitPaneView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        secondaryActionOptions: [
          { label: 'Merge ticket', icon: 'genuicon-merge-icon', onClick: () => {this.openMergeTicketModal();}},
          { label: 'Move ticket', icon: 'genuicon-nav-transactions', onClick: () => {this.openMoveTicketModal();}},
          { label: 'Clone ticket', icon: 'genuicon-edit-copy', onClick: () => {this.openCloneTicketModal();}},
        ],
        editingSubject: false,
        disabled: false,
        showAppSessionsSection: false,
        isOpen: true,
        isSamePage: false,
        activeDropdown: '',
        changeFormHovering: false,
        printingHelpTicket: false,
        showQuickViewActions: false,
        navigatedFromParent: false,
        showDropdown: false,
        hideHeader: true,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['workspaceOptions']),
      ...mapGetters([
        'currentHelpTicket',
        'tasksTotalCount',
        'tasksCompletedCount',
        'currentCompany',
      ]),
      ...mapGetters('customForms', ['customForms']),
      ticket() {
        return this.currentHelpTicket;
      },
      isScopedWriteForTicket() {
        return ((this.isScopedAny || this.isBasicAccess) && this.isWritableFromCustomFormObject(this.ticket));
      },
      createdAt() {
        if (this.ticket && this.ticket.createdAt) {
          return this.timezoneDatetime(this.ticket.createdAt, Vue.prototype.$timezone);
        }
        return null;
      },
      subjectField() {
        if (this.ticket) {
          return this.getField(this.ticket, 'subject');
        }
        return null;
      },
      descriptionField() {
        if (this.ticket) {
          return this.getField(this.ticket, 'description');
        }
        return null;
      },
      subjectValues() {
        if (this.ticket) {
          return this.getValuesForName(this.ticket, 'subject');
        }
        return [];
      },
      descriptionValues() {
        if (this.ticket) {
          return this.getValuesForName(this.ticket, 'description');
        }
        return [];
      },
      subjectValue() {
        if (this.ticket) {
          return this.getValuesForName(this.ticket, 'subject')[0].valueStr;
        }
        return null;
      },
      priority() {
        if (this.ticket) {
          const values = this.getValuesForName(this.ticket, 'priority');
          return _get(values, "[0].valueStr", '');
        }
        return null;
      },
      priorityFlagColor() {
        if (this.ticket) {
          const priorityField = this.ticket.customForm.formFields.find(field => field.fieldAttributeType === "priority");
          if (priorityField) {
            const option = priorityField.options.find(obj => obj.name.toLowerCase() === this.priority);
            if (option) {
              return option.color;
            }
          }
        }
        return null;
      },
      priorityTooltip() {
        return `${this.titleize(this.priority)} Priority`;
      },
      notificationStatus() {
        return this.currentHelpTicket.muteNotification ? "unmute" : "mute";
      },
      notificationIcon() {
        return this.currentHelpTicket.muteNotification ? "genuicon-bell-slash-o" : "nulodgicon-bell-o";
      },
      canEditTicketForm() {
        return false;
      },
      canEditTicket() {
        return this.isWrite || this.isScopedWriteForTicket;
      },
      forms() {
        return this.customForms;
      },
      shouldShowSecondaryTicketActions() {
        return this.isWrite && (this.workspaceOptions && this.workspaceOptions.length > 0 || this.forms && this.forms.length > 1);
      },
      headerFields() {
        if (this.currentHelpTicket) {
          const includedFields = ['Status', 'Priority', 'Assigned To'];
          return this.currentHelpTicket.customForm.formFields
            .filter((field) => includedFields.includes(field.label))
            .sort(
              (a, b) =>
                includedFields.indexOf(a.label) - includedFields.indexOf(b.label)
            );
        }
        return null;
      },
    },
    watch: {
      isOpen(val) {
        this.$nextTick(() => {
          if (val && this.$refs.contributorSelect) this.$refs.contributorSelect.$el.querySelector('input').focus();
        });
      },
    },
    methods: {
      ...mapMutations([
        'setTickets',
        'setCurrentHelpTicket',
        'setHideHeader',
      ]),
      ...mapActions(['fetchTicket']),
      redirectBack() {
        this.setTickets([]);
        this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
      },
      emitAllSessions(sessions) {
        this.$emit("all-sessions", sessions);
      },
      emitSession(session) {
        this.$emit("session", session);
      },
      redirectToParent() {
        const url = `/user_accesses.json?company_id=${this.$defaultCompanyId}&redirect_route=/help_tickets?workspace_id=${this.ticket.workspaceId}`;
          http.get(url)
            .then((res) => {
              window.open(res.data.url, '_blank');
            });
      },
      onWorkspaceChange() {
        this.navigatedFromParent = window?.location?.search?.includes('parent');
        this.showFirstTimeHelpdesk('show');
        this.setupWorkspacePusherListeners();
        if (!this.workspaceOptions?.length) {
          this.$store.dispatch('GlobalStore/fetchWorkspaces');
        }
        this.toggleHeader();
      },
      redirectToTicketShow() {
        if (this.isQuickView && !this.currentCompany) {
          window.open(`/help_tickets/${this.currentHelpTicket.id}`, '_blank');
        } else {
          const url = `/user_accesses.json?company_id=${this.currentHelpTicket.company.id}&redirect_route=/help_tickets/${this.currentHelpTicket.id}?workspace_id=${this.currentHelpTicket.workspaceId}`;
          http.get(url)
            .then((res) => {
              window.open(res.data.url, '_blank');
            });
        }
      },
      toggleQuickViewActions() {
        this.showQuickViewActions = !this.showQuickViewActions;
      },
      closeQuickView() {
        this.$emit('close-quick-view');
      },
      closeQuickViewActions(event) {
        if (!document.getElementById('modern-ticket-show-page-dropdown')?.contains(event.target)) {
          this.showQuickViewActions = false;
        }
      },
      dateTimeFormatting(value) {
        if (this.currentHelpTicket && this.currentHelpTicket[value]) {
          return [moment(this.currentHelpTicket[value]).fromNow(), this.timezoneDatetimeMoment(this.currentHelpTicket[value], Vue.prototype.$timezone)];
        }
        return ['No record'];
      },
      setActiveDropdown(field) {
        this.activeDropdown = field;
      },
      updateFormField(params) {
        this.updateField(params)
          .then((res) => {
            this.setFieldInTicket(res.data);
          });
      },
      onBlur() {
        if (!document.activeElement?.classList.contains("multiselect__input")) {
          this.isOpen = false;
        }
      },
      assignedToFields(field) {
        if (this.ticket) {
          return {
            ...this.ticket,
            customFormId: this.ticket.customForm?.id || null,
            canWrite: true,
            fields: {
              assignedTo: field.customFormValue.map((cfv) => ({ id: cfv.valueInt })) || [],
            },
          };
        }
        return {};
      },
      fetchCurrentTicket() {
        this.$store.dispatch("fetchTicket", this.currentHelpTicket.id);
      },
      refreshTicketAndActivities() {
        this.refreshTicket();
        this.refreshActivities();
      },
      refreshTickets() {
        this.$store.dispatch("fetchTickets");
      },
      refreshTicket() {
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      refreshActivities() {
        this.$store.dispatch("fetchTicketActivities", this.ticketId);
      },
      toggleSubjectEdit(value) {
        this.editingSubject = value;
      },
      okDelete() {
        this.$refs.deleteModal.close();
        http
          .delete(`/tickets/${this.ticket.id}.json`)
          .then(() => {
            this.setCurrentHelpTicket(null);
            this.refreshTickets();
            this.emitSuccess("Help ticket successfully deleted");
            if (!this.isQuickView) {
              this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error deleting the help ticket (${error.response.data.message}).`);
          });
      },
      okArchive() {
        this.disabled = true;
        const params = {
          company_id: this.ticket.company.id,
        };
        http
          .post(`/tickets/${this.ticket.id}/archive.json`, params)
          .then(() => {
            this.$store.commit('setPage', 0);
            this.emitSuccess("Help ticket successfully archived");
            this.$refs.archiveModal.close();
            this.refreshTickets();
            this.disabled = true;
            if (!this.isQuickView) {
              this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            let errorMessage = error.message;

            if (error.response) {
              errorMessage = error.response.data.message;
            }
            this.emitError(`Sorry, there was an error archiving the help ticket (${errorMessage}).`);
            this.$refs.archiveModal.close();
            this.disabled = true;
          });
      },
      okUnarchive() {
        http
          .post(`/tickets/${this.ticket.id}/unarchive.json`)
          .then(() => {
            this.emitSuccess("Help ticket successfully unarchived");
            this.$refs.unArchiveModal.close();
            this.refreshTickets();
            if (!this.isQuickView) {
              this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            this.$refs.unArchiveModal.close();
            this.emitError(`Sorry, there was an error unarchiving the help ticket (${error.response.data.message}).`);
          });
      },
      showAppSessions(sessionsCount) {
        this.showAppSessionsSection = sessionsCount > 0;
      },
      openDeleteConfirmationPopup() {
        this.$refs.warningMessage.open();
        this.$refs.deleteModal.open();
      },
      openTasksTab() {
        this.$emit('set-active-component', 'Tasks');
      },
      toggleMute() {
        http
          .put(`/tickets/${this.currentHelpTicket.id}/update_notification_status.json`, { muteNotification: !this.currentHelpTicket.muteNotification, company_id: this.currentHelpTicket.company.id })
          .then(() => {
            this.emitSuccess('Successfully updated mute notification status');
            this.$refs.notificationsModal.close();
            this.refreshTicket();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error updating mute status`);
          });
      },
      openMoveTicketModal() {
        this.$refs.moveTicketModal.open();
      },
      openMergeTicketModal() {
        this.$refs.mergeTicketModal.open();
      },
      openCloneTicketModal() {
        this.$refs.cloneTicketModal.open();
      },
      printCurrentTicket() {
        this.printingHelpTicket = true;
        requestAnimationFrame(() => {
          this.$refs.ticketPrint.printHelpTicket();
          this.printingHelpTicket = false;
        });
      },
      toggleDropdown() {
        this.showDropdown = !this.showDropdown;
      },
      closeDropdown() {
        this.showDropdown = false;
      },
      toggleHeader() {
        this.hideHeader = !this.hideHeader;
        this.setHideHeader(this.hideHeader);
      },
    },
  };
</script>

<style lang="scss">

  .custom-tooltip {
    z-index: 51;
  }
  .header-toggle-button-icon {
    cursor: pointer !important;
    font-size: x-large;
  }

  body .sweet-modal-overlay .sweet-modal.theme-light {
    overflow: hidden !important;
  }
</style>

<style scoped lang="scss">

  .icon {
    color: $pastel-blue-100;
  }

  .multiselect {
    :deep {
      width: 10rem;

      .multiselect__tags {
        padding-top: 0.4rem;
        padding-left: 0.75rem;
        min-height: 0.75rem;
        height: 1.875rem;
        border-radius: 0.2rem;
        margin-right: 0.5rem !important;
      }

      .multiselect__select {
        height: 1.875rem;
      }

      .multiselect__content-wrapper {
        width: 9.6rem;
      }
    }
  }

  .priority-flag {
    margin-left: -1.25rem;
  }

  .priority-flag-position {
    position: relative;
    top: -1.25rem;
  }

  .border-radius-4 {
    border-radius: 4px;
  }

  .ticket-modal {
    :deep(.sweet-modal) {
      overflow: unset;
    }
  }

  .close-cross:before {
    color: $themed-muted !important;
    font-size: 1.25rem;
  }

  .quick-view-action-dropdown {
    left: unset;
    right: 3.5rem;
    top: 2.8125rem;
  }

  .split-view-action-dropdown {
    left: unset;
    right: 1rem;
    top: 2.4rem;
  }

  .external-link-size {
    font-size: 0.938rem;
  }

  .genuicon-android-time:before {
    position: relative;
    top: 1px;
  }

  .preview-header {
    top: -1.5rem !important;
    position: sticky !important;
  }

  .nulodgicon-android-close:before {
    color: $themed-secondary;
  }

  .font-size-small {
    font-size: smaller;
    font-weight: 100;
  }

  .table-header {
    display: table;
    position: static;
    top: 5rem;
    z-index: 99;
  }

  .quick-view {
    width: calc(100% + 3rem);
    position: static;
    top: auto;
    z-index: auto;
  }
  .max-height {
    max-height: 2rem;
  }

  .header-toggle-container {
    position: relative;
    text-align: center;
  }

  .header-toggle-button {
    position: absolute;
    top: -50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4rem;
  }

  .pills-bg {
    background-color: $themed-lighter;
  }

  .dropdown-menu-top {
    top: 4.8125rem !important;
  }

  .divider {
    border: none;
    border-top: 1px solid #c1c1c1;
    margin: 0.5rem 0;
  }
</style>
