class MiniHelpdeskVersionsController < ApplicationController
  def download_mini_helpdesk
    mac_version = mini_hd_mac_version
    windows_version = mini_hd_windows_version

    if mac_version.present? || windows_version.present?
      render json: {
        status: true,
        mac_url: mac_version&.installer_link&.url,
        windows_url: windows_version&.installer_link&.url,
        mac_app_version: mac_version&.version,
        windows_app_version: windows_version&.version
      }
    else
      render json: { status: false }
    end
  end

  def mini_hd_windows_url
    build_url = mini_hd_windows_version.installer_link.url
    file_name = File.basename(URI.parse(build_url).path)

    scripts = {
      powershell_script: "cd $env:temp; Invoke-RestMethod -Uri \"#{build_url}\" -OutFile \"#{file_name}\"; Start-Process \"./#{file_name}\" -Wait",
      powershell_silent_script: "cd $env:temp; Invoke-RestMethod -Uri \"#{build_url}\" -OutFile \"#{file_name}\"; Start-Process \"./#{file_name}\" -ArgumentList \"/quiet\" -Wait"
    }

    render json: { scripts: scripts }, status: :ok
  end

  private

  def mini_hd_mac_version
    MacMiniHelpdeskVersion.last
  end

  def mini_hd_windows_version
    WindowsMiniHelpdeskVersion.last
  end
end
