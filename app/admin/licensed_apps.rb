ActiveAdmin.register Company, as: "Licensed Apps" do
  menu parent: 'Metrics', label: "Licensed Apps", priority: 5
  breadcrumb do
    ['admin', 'metrics']
  end
  
  actions :index

  filter :name, as: :select, collection: Company.order("name ASC").map { |comp| [comp.name] }
  config.sort_order = 'name_asc'

  controller do
    around_action :set_read_replica_db, only: [:index, :show]
  end

  index do
    column ("Company"), sortable: :name do |company|
      if company.system_users.present?
        link_to company.try(:name), new_user_access_path(company_id: company.id)
      else
        company.try(:name)
      end
    end

    column :subdomain

    column ("Admin Email") { |company| "#{ company.admin_company_users.first.try(:user).try(:email)}" }
    
    column ("Licensed Apps") do |company|
      licenses_info = []
      company.apps.licensed.find_each do |app|
        licenses_info << "#{app.name}: (Total: #{app.total_users}, Consumed: #{app.used})"
      end
      licenses_info.length > 0 ? licenses_info : "No Licensed Apps"
    end
  end

  controller do
    include MultiCompany::GeneralScoping
  
    before_action :verify_super_admin_mfa
  end
end
