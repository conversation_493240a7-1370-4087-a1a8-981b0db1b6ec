<template>
  <div>
    <h5 class="mb-3">Choose how to import your devices:</h5>
    <div class="radio-group f-flex-col">
      <label
        key="1"
        class="w-100 p-2 mb-0"
      >
        <input
          v-model="importType"
          value="managed_asset"
          type="radio"
        >
        <i class="nulodgicon-checkmark" />
        <div class="d-block align-middle ml-5 pl-2 pt-1">
          <p class="mb-0 font-weight-bold text-secondary option-header">
            {{ moveAllDevices ? 'Managed Assets' : integrationName + ' devices to Managed Assets and others to Discovered Assets' }}
          </p>
        </div>
        <i class="nulodgicon-checkmark" />
      </label>
      <label
        key="2"
        class="w-100 p-2 mb-0"
      >
        <input
          v-model="importType"
          value="discovered_asset"
          type="radio"
        >
        <i class="nulodgicon-checkmark" />
        <div class="d-block align-middle ml-5 pl-2 pt-1">
          <p class="mb-0 font-weight-bold text-secondary option-header">
            {{ moveAllDevices ? 'Discovered Assets' : 'All devices to Discovered Assets' }}
          </p>
        </div>
        <i class="nulodgicon-checkmark" />
      </label>
      <div 
        v-if="moveAllDevices"
        class="mt-3"
      >
        <p>
          Note: If you want to review your devices before moving to managed assets select "Import to Discovered Assets". If your data
          integrity is high select "Import to Managed Assets".
        </p>
      </div>
      <div class="row align-items-center mt-3 ml-0">
        <div class="col-auto">
          <material-toggle
            :init-active="assignUsers"
            @toggle-sample="assignUsers = $event"
          />
        </div>
        <label class="col font-weight-bold mb-0 pl-0">
          Import Users assigned to devices
        </label>
      </div>
      <p
        class="text-muted mt-1"
        style="font-size: 0.875rem;"
      >
        Enabling this option will auto-assign users to managed assets on Genuity. If matching users do not exist, they will be created as uninvited staff member. 
      </p>
      <div 
        v-if="!autoSettingImportType"
        class="form-group col-12 mb-n3 mt-4 text-right"
      >
        <button
          class="btn btn-sm btn-link text-secondary mr-2"
          @click.stop="backToAccDetail"
        >
          <span>Back</span>
        </button>
        <button
          :disabled="noTarget"
          class="btn btn-sm btn-primary px-3"
          @click.stop="submitForm"
        >
          Continue
        </button>
      </div>
    </div>
  </div>
</template>

<script>
  import MaterialToggle from '../material_toggle.vue';

  export default {
    components: {
      MaterialToggle,
    },
    props: {
      integrationName: {
        type: String,
        default: null,
      },
    },
    data() {
      return {
        importType: 'managed_asset',
        assignUsers: false,
      };
    },
    computed: {
      noTarget() {
        return this.importType === '';
      },
      moveAllDevices() {
        const connector = [
          'Kaseya', 'Sophos', 'Azure AD', 'Kandji', 'Google Workspace', 'Microsoft Intune', 'Jamf Pro', 'Azure', 'AWS', 'GCP', 'Mosyle',
        ];
        return connector.includes(this.integrationName);
      },
      autoSettingImportType() {
        const connector = ['Azure AD', 'Google Workspace', 'Microsoft Intune', 'Azure'];
        return connector.includes(this.integrationName);
      },
    },
    watch: {
      importType(newVal) {
        if (this.autoSettingImportType) {
          this.$emit('submit', newVal);
        }
      },
      assignUsers(newVal) {
        this.$emit('assign-users', newVal);
      },
    },
    methods: {
      submitForm() {
        this.$emit('submit', this.importType);
      },
      backToAccDetail() {
        this.$emit('back-to-acc-details');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .radio-group {
    margin-left: 0.625rem;
    gap: 0.6875rem;
  }
  .nulodgicon-checkmark {
    background-color: white;
    border-radius: 50%;
    border: 1px solid $themed-fair;
    color: white;
    display: block;
    font-size: 0.875rem;
    height: 1.5rem;
    line-height: 1.5rem;
    position: absolute;
    left: 1rem;
    text-align: center;
    top: 50%;
    transition: $transition-base;
    transform: translateY(-50%);
    width: 1.5rem;
  }
  :checked ~ .nulodgicon-checkmark {
    background-color: $blue;
    border-color: $blue;
  }
  label {
    cursor: pointer;
    transition: $transition-base;
    position: relative;
    &:hover {
      background-color: $themed-light;
    }
  }
  [type='radio'] {
    display: none;
  }
</style>
