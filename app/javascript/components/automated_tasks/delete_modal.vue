<template>
  <sweet-modal
    v-if="value"
    ref="modal"
    v-sweet-esc
    title="Before you delete this Automated Task"
  >
    <template>
      <div>
        Are you absolutely sure you want to delete this automated task?
      </div>
    </template>
    <button
      slot="button"
      class="btn btn-link text-secondary mr-2 btn-sm"
      @click.prevent.stop="close"
    >
      No, keep it
    </button>
    <button
      slot="button"
      class="btn btn-link text-danger btn-sm"
      data-tc-i-am-sure
      @click.prevent.stop="okDelete"
    >
      Yes, I'm sure
    </button>
  </sweet-modal>
</template>

<script>
import http from 'common/http';
import { mapMutations, mapActions } from 'vuex';
import { SweetModal } from 'sweet-modal-vue';
import common from '../../mixins/automated_tasks/common';

export default {
  components: {
    SweetModal,
  },
  mixins: [ common ],
  props: ['value'],
  computed: {
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  methods: {
    ...mapMutations(['setAutomatedTasks']),
    ...mapActions(['fetchAutomatedTasks']),

    close() {
      this.$refs.modal.close();
    },
    open() {
      this.$refs.modal.open();
    },
    okDelete() {
      const url = this.isAssetsModule ? `/asset_automation_tasks/${this.value.id}.json` : `/automated_tasks/${this.value.id}.json`;
      http
        .delete(url)
        .then(() => {
          this.close();
          this.emitSuccess(`Task successfully deleted`);
          this.$emit('fetch-grouped-tasks');
          this.loadTasks();
        })
        .catch(() => {
          this.emitError(`Sorry, there was an issue deleting this automated task. Please refresh the page and try again.`);
          this.close();
        });
    },
    loadTasks() {
      this.fetchAutomatedTasks()
        .then(res => {
          this.setAutomatedTasks(res.data);
        })
        .catch(() => {
          this.emitError("Sorry, but there was an error fetching the automated tasks.");
        });
    },
  },
};
</script>
