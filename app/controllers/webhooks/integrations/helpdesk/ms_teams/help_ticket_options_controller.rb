class Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketOptionsController < Webhooks::Integrations::Helpdesk::BaseController
  include MsTeamsOptions

  def index
    options = custom_fields_options
    log_event(options, 'index', :success)
    render json: options, status: :ok
  end

  private

  def integration
    'MsTeams'
  end

  def custom_form
    @custom_form ||= scoped_company.default_workspace.custom_forms.active.find_by(company_module: 'helpdesk', default: true) ||
			               scoped_company.default_workspace.custom_forms.active.where(company_module: 'helpdesk').order(:created_at).first
  end
end
