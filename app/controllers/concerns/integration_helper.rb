module IntegrationHelper
  extend ActiveSupport::Concern

  def resync
    config_class = "Integrations::#{params[:integration_name].camelize}::Config".constantize
    worker_class = "Integrations::#{params[:integration_name].camelize}::SyncDataWorker".constantize
    config = config_class.find_by_id(params[:id])
    if config.present?
      company_integration = config.company_integration
      company_integration.assign_attributes(sync_status: :pending,
                                            status: true,
                                            error_message: nil,
                                            notified: nil,
                                            company_user_id: current_company_user&.id)
      company_integration.save!
      worker_class.perform_async(config.id, false, params[:is_resyncing], current_company_user&.id)
      render json: {}, status: 200
    else
      render json: {}, status: 404
    end
  end
  
  def log_exception(exception, discovered_asset, api_type = nil)
    Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, api_type).to_json)
  end
end
