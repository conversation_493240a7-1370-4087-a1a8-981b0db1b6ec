<template>
  <div>
    <form>
      <div class="row">
        <div class="col offset-0">
          <div class="form-group mb-3">
            <label>Name / Description</label>
            <input
              v-model="metric.label"
              v-validate="'required|max:25'"
              name="label"
              type="text"
              class="form-control bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-lg px-0 template-name--input big"
              :class="{'is-invalid': errors.has('label') }"
              placeholder="Metric name"
              required
              @input="handleNameInput"
            >
            <span 
              v-if="errors.has('label')"
              class="help text-danger small"
            >
              {{ errors.first('label') }}
            </span>
          </div>
          <div class="form-group mb-3">
            <input
              v-model="metric.description"
              v-validate="'max:50'"
              name="description"
              type="text"
              :readonly="!isWrite"
              class="form-control bg-transparent border-right-0 border-left-0 border-top-0 rounded-0 shadow-none form-control-lg px-0 template-name--input not-as-small"
              :class="{'is-invalid': errors.has('description') }"
              placeholder="A basic description of your metric"
              @input="handleDescriptionInput"
            >
            <span 
              v-if="errors.has('description')"
              class="help text-danger small"
            >
              {{ errors.first('description') }}
            </span>
          </div>
        </div>
        <div class="form-group col-2 offset-1">
          <div>
            <label class="d-block mb-2 ml-n2">Icon</label>
            <style-select-dropdown
              class="ml-n2"
              :object="iconOptions"
              :value="{class:`${metric.icon || 'genuicon-software'}`}"
              :color-class="`${'secondary'}`"
              @input="changeMetricIcon"
            />
          </div>
        </div>
      </div>
    </form>
    
    <div v-if="isFirewallOrEncryptionMetric">
      <h6>
        No configuration is required for the {{ metric.label }} metric. Save the metric to view {{ metric.label }} statistics.
      </h6>
    </div>
    <div 
      v-else
      class="box box--with-heading box--flat mt-4 mb-5"
    >
      <div class="box__heading py-3 px-4 rounded-top">
        <div class="d-flex align-items-center">
          <h5 class="font-weight-normal mb-0">
            Selected options
          </h5>
        </div>
      </div>
      <div class="box__inner d-flex flex-wrap">
        <div class="w-100">
          <span 
            v-for="(item, index) in metric.selectedOptions"
            :key="`selected-metric-option-${index}`"
            class="extension-pill d-inline-block py-1 mt-2 mr-1 bg-blue-subtle text-primary rounded-pill"
          >
            {{ item }}
            <i
              v-if="isWrite"
              class="category-heading badge nulodgicon-android-close ml-2 rounded-circle"
              @click="removeItem(item)"
            />
          </span>
        </div>
        
        <hr class="w-100 my-4">

        <div 
          v-if="isWrite"
          class="form-group w-100"
        >
          <div class="d-flex">
            <div
              class="pl-0"
              :class="metricType === 'all_metrics' ? 'col-6' : 'col-12 pr-0'"
            >
              <label class="text-muted d-block">Add new {{ metric.label }} option</label>
              <software-list
                name="metric_option_list"
                :metric-options="metricCustomOptions"
                :value="newMetric"
                placeholder="Select option"
                :selected-menu="selectedMenu"
                @select="addItem"
                @remove="removeItem"
                @tab-change="fetchOptionsForTab"
                @add-new-option="handleAddNewOption"
              />
            </div>
          </div>
          <div class="text-muted smallest pt-1">
            Want to create a new software option?
            <a
              href="#"
              @click.prevent.stop="toggleNewOptionField"
            >
              Add new
            </a>
          </div>
        </div>
        <div 
          v-if="showNewOptionField"
          class="form-group d-flex align-items-center mt-2 clickable"
        >
          <input
            v-model="newOption"
            class="form-control clickable"
            type="text"
            placeholder="Add Option"
            @keypress.enter.prevent="saveSoftwareOption"
          > 
          <button
            class="btn submit-button font-weight-semi-bold ml-2 mr-2"
            @click.stop="handleCancel"
          >
            Cancel
          </button>
          <button
            class="btn submit-button font-weight-semi-bold"
            @click.stop.prevent="saveSoftwareOption"
          >
            Save
          </button>
        </div>
      </div>
    </div>
    <div class="sweet-modal__sticky-footer text-right pr-5">
      <submit-button
        btn-content="Save Metric"
        :btn-classes="'px-5'"
        :disabled="!isWrite"
        :is-validated="isWrite"
        @submit="createOrEditMetric"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapActions, mapGetters, mapMutations } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';
  import StyleSelectDropdown from 'components/shared/style_select_dropdown.vue';
  import SubmitButton from 'components/shared/submit_button.vue';
  import _cloneDeep from 'lodash/cloneDeep';
  import SoftwareList from './software_list.vue';

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    components: {
      SoftwareList,
      StyleSelectDropdown,
      SubmitButton,
    },
    mixins: [permissionsHelper],
    props: {
      data: {
        type: Object,
        default: () => {},
        required: false,
      },
      type: {
        type: String,
        default: '',
        required: true,
      },
    },
    data() {
      return {
        metric:  this.getInitialMetricData(),
        newOption: '',
        isSubmittingSoftwareOption: false,
        isSubmitting: false,
        showNewOptionField: false,
        selectedMenu: 'installed',
        iconOptions: [
          {class: 'genuicon-software'},
          {class: 'genuicon-antivirus'},
          {class: 'genuicon-data-encryption'},
          {class: 'genuicon-patch-management'},
          {class: 'genuicon-firewall'},
          {class: 'genuicon-device-health'},
          {class: 'genuicon-backups'},
          {class: 'genuicon-random-tabs'},
          {class: 'genuicon-accounting'},
          {class: 'genuicon-computer'},
          {class: 'genuicon-data'},
          {class: 'genuicon-it'},
          {class: 'genuicon-barcode'},
          {class: 'genuicon-keyboard'},
          {class: 'genuicon-star'},
          {class: 'genuicon-nav-vendor-spend'},
        ],
        metricType: '',
        newMetric: '',
      };
    },
    computed: {
      ...mapGetters([
        'metricCustomOptions',
      ]),
      isCustomWidget() {
        return this.metric.isCustom;
      },
      isFirewallOrEncryptionMetric() {
        return ['Firewall', 'Data Encryption'].includes(this.metric.name);
      },
      metricName() {
        return this.isCustomWidget ? this.metric.label : this.metric.name;
      },
    },
    methods: {
      ...mapActions([
        'fetchRiskCenterWidgetData',
        'getCustomMetricOptions',
      ]),
      ...mapMutations([
        'setMetricCustomOptions',
      ]),

      resetFields() {
        this.resetMetricData();
        this.$validator.reset();
      },

      fetchOptionsForTab(tab) {
        this.selectedMenu = tab;
        this.getCustomMetricOptions({ tab, widget_type: this.metric.name, is_custom: this.metric.isCustom });
      },
    
      getInitialMetricData() {
        return this.data && Object.keys(this.data).length > 0 
          ? _cloneDeep(this.data) 
          : this.getNewMetricDefaultData();
      },
      getNewMetricDefaultData() {
        return {
          name: '',
          label: '',
          description: '',
          icon: 'genuicon-software',
          isCustom: true,
          selectedOptions: [],
        };
      },
      addItem(item) {
        if (this.metric.selectedOptions?.some(selectedOption => selectedOption === item)) {
          this.emitError('This option is already selected.');
          return;
        }
        this.metric.selectedOptions.push(item);
      },
      handleAddNewOption(option) {
        this.newOption = option;
        this.saveSoftwareOption();
      },
      removeItem(item) {
        const unwantedItemIndex = this.metric.selectedOptions.indexOf(item);
        if (unwantedItemIndex !== -1) {
          this.metric.selectedOptions.splice(unwantedItemIndex, 1);
        }
      },
      changeMetricIcon(iconClass) {
        if (this.isWrite) {
          this.metric.icon = iconClass.class;
        }  
      },
      handleNameInput(event) {
        this.metric.label = event.target.value;
      },

      handleDescriptionInput(event) {
        this.metric.description = event.target.value;
      },
      createOrEditMetric() {
        const params = { ...this.metric, name: this.metricName, enabled: true };

        this.$validator.validateAll().then((result) => {
          if (result) {
            this.isSubmitting = true;
            const requestPromise = this.metric.id
              ? http.put(`/managed_assets/risk_center_widgets.json`, params)
              : http.post('/managed_assets/risk_center_widgets.json', params);
            requestPromise
              .then((res) => {
                const successMessage = this.metric.id 
                  ? 'Risk center metric updated successfully!' 
                  : 'Risk center metric created successfully!';
                  this.emitSuccess(successMessage);
                  this.$emit('widget-updated', res.data.widget);
                  if (!this.metric.id) {
                    this.resetMetricData();
                  }
                  this.$emit('close-modal');
                  this.isSubmitting = false;
                })
                .catch((error) => {
                  this.isSubmitting = false;
                  this.emitError(`Sorry, there was an error saving data (${error.response.data.message}).`);
                });
          } else {
            this.emitError(`Please correct the highlighted errors before submitting.`);
          }
        });
      },
      resetMetricData() {
        this.metric = { ...this.getInitialMetricData() };
      },
      saveSoftwareOption() {
        if (!this.newOption.trim()) {
          this.emitError('Option cannot be empty.');
          return;
        }

        if (this.metricCustomOptions?.some(option => option === this.newOption)) {
          this.emitError('This option already exists.');
          return;
        }
        this.isSubmittingSoftwareOption = true;
        const params = { 
          type: this.metric.id && !this.metric.isCustom ? this.metric.name : 'custom',
          option: this.newOption,
        };

        http.post('/managed_assets/risk_center_widget/widget_custom_option.json', params)
          .then(response => {
            const newSavedOption = response.data.option;
            if (this.isCustomWidget) {
              this.setMetricCustomOptions([...this.metricCustomOptions, newSavedOption]);
            } 
            this.newOption = '';
            this.showNewOptionField = false; 
            this.isSubmittingSoftwareOption = false;
            this.emitSuccess('Option added successfully!');
            this.fetchOptionsForTab(this.selectedMenu);
          })
          .catch(error => {
            this.isSubmittingSoftwareOption = false;
            this.emitError(`Sorry, there was an error saving the new option (${error.response.data.message}).`);
          });
      },
      toggleNewOptionField() {
        this.showNewOptionField = !this.showNewOptionField;
      },
      handleCancel() {
        this.newOption = '';
        this.showNewOptionField = false; 
      },
    },
  };
</script>

<style lang="scss" scoped>
  .category-heading {
    background-color: $themed-dark-drawer-bg;
    color: $white;
  }
  .side-menu-item {
    font-size: 1.05rem;
  }
  .badge { 
    padding: 0.1em 0.3em;
    font-size: 52%;
  }
  .extension-pill {
    text-align: center;
    position: relative;
    padding: 0 1.875rem 0 1.25rem;
    i {
      display: none;
      position: absolute;
      right: 0.25rem;
      top: 0.375rem;
      cursor: pointer;
    }
  }

  .extension-pill:hover {
    i {
      display: inline-block;
    }
  }
  .nulodgicon-android-close {
    &:before {
      color: $white;
      font-size: 1rem;
    }
  }
</style>
