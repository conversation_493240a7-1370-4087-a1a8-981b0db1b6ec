#!/usr/bin/env ruby
# Chrome Devices Processing Simulator
# Simulates the exact save_chrome_devices_worker.rb logic on NSBOROS CSV data

require 'csv'

class ChromeDevicesProcessingSimulator
  def initialize(csv_file_path)
    @csv_file_path = csv_file_path
    @processing_stats = {
      total_devices: 0,
      empty_mac_addresses: 0,
      invalid_mac_addresses: 0,
      valid_mac_addresses: 0,
      nil_mac_addresses: 0,
      all_zeros_mac: 0,
      invalid_format_mac: 0,
      successfully_processed: 0,
      would_be_skipped: 0
    }
    @sample_issues = {
      empty_mac: [],
      invalid_mac: [],
      nil_mac: [],
      processed_devices: []
    }
  end

  def run_simulation
    puts "🔍 CHROME DEVICES PROCESSING SIMULATION"
    puts "=" * 50
    puts "📁 Processing file: #{@csv_file_path}"
    
    process_csv_data
    generate_detailed_report
    analyze_your_selected_code
    provide_recommendations
  end

  private

  def process_csv_data
    CSV.foreach(@csv_file_path, headers: true) do |row|
      @processing_stats[:total_devices] += 1
      
      # Extract device data (matching your worker structure)
      device_data = {
        serial_number: row['serialNumber'],
        model: row['model'],
        os_version: row['osVersion'],
        mac_address: row['macAddress'],  # Column 14
        ethernet_mac: row['ethernetMacAddress'], # Column 13
        device_id: row['deviceId'],
        provision_status: row['provisionStatus']
      }
      
      # Simulate the exact worker logic
      simulate_device_processing(device_data)
    end
  end

  def simulate_device_processing(device_data)
    # Exact same logic as save_chrome_devices_worker.rb - NO MAC VALIDATION
    mac_addresses = []
    display_name = device_data[:model] && !device_data[:model].empty? ? device_data[:model] : "ChromeOS #{device_data[:os_version]}"
    serial_number = device_data[:serial_number]
    model = device_data[:model]
    operating_system = "ChromeOS #{device_data[:os_version]}"
    os_name = "ChromeOS"
    os_version = device_data[:os_version]

    # This is the critical line from your worker - just add MAC without validation
    mac_address = device_data[:mac_address] ? device_data[:mac_address].upcase : nil
    mac_addresses << mac_address if mac_address

    source = 'google_workspace'
    last_check_in_time = device_data[:last_sync]

    # Track MAC address states for analysis
    if mac_address.nil?
      @processing_stats[:nil_mac_addresses] += 1
    elsif mac_address.empty?
      @processing_stats[:empty_mac_addresses] += 1
    else
      @processing_stats[:valid_mac_addresses] += 1
    end

    # Simulate find_discovered_asset logic (simplified)
    # discovered_asset = find_discovered_asset(company, false, serial_number, mac_addresses, display_name, nil, nil, false)
    # For simulation, assume no existing asset found
    discovered_asset_exists = false

    # Simulate the selected code logic - YOUR CRITICAL LINE
    existing_asset_mac_addresses = discovered_asset_exists ? ["existing:mac"] : []
    mac_addresses_data = mac_addresses.any? ? mac_addresses : existing_asset_mac_addresses

    # Simulate discovered asset creation/update
    display_name_data = display_name && !display_name.empty? ? display_name : "existing_display_name"
    serial_data = serial_number && !serial_number.empty? ? serial_number : "existing_serial"
    ip_address_data = nil # No IP in CSV

    # Simulate asset assignment (like your worker does)
    asset_attributes = {
      display_name: display_name_data,
      machine_serial_no: serial_data,
      mac_addresses: mac_addresses_data,
      ip_address: ip_address_data,
      asset_type: "Laptop", # get_asset_type("Laptop")
      os: operating_system,
      os_name: os_name,
      os_version: os_version,
      model: model,
      source: source,
      discovered_asset_type: "device",
      last_check_in_time: last_check_in_time
    }

    # Track what would happen
    @processing_stats[:successfully_processed] += 1

    # Sample some devices for analysis
    if @sample_issues[:processed_devices].length < 10
      @sample_issues[:processed_devices] << {
        serial: serial_number,
        model: model,
        original_mac: device_data[:mac_address],
        processed_mac: mac_address,
        mac_addresses_array: mac_addresses,
        final_mac_data: mac_addresses_data,
        provision_status: device_data[:provision_status]
      }
    end
  end

  def mac_address_valid?(mac_address)
    return false if mac_address.nil? || mac_address.empty?

    regex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i
    mac_address.to_s.include?("00:00:00:00:00:00") ? false : !mac_address.to_s.match(regex).nil?
  end

  def generate_detailed_report
    puts "\n📊 WORKER PROCESSING SIMULATION RESULTS"
    puts "-" * 45

    total = @processing_stats[:total_devices]
    puts "Total devices in CSV: #{total}"
    puts "Expected in system: 4,957"
    puts "Missing devices: #{total - 4957}"

    puts "\n🔍 MAC Address Distribution (No Validation Applied):"
    puts "Devices with MAC addresses: #{@processing_stats[:valid_mac_addresses]} (#{percentage(@processing_stats[:valid_mac_addresses], total)}%)"
    puts "Devices with empty MAC: #{@processing_stats[:empty_mac_addresses]} (#{percentage(@processing_stats[:empty_mac_addresses], total)}%)"
    puts "Devices with nil MAC: #{@processing_stats[:nil_mac_addresses]} (#{percentage(@processing_stats[:nil_mac_addresses], total)}%)"

    puts "\n📈 Worker Processing Outcome:"
    puts "All devices would be processed: #{@processing_stats[:successfully_processed]}"
    puts "None would be skipped: #{@processing_stats[:would_be_skipped]}"

    puts "\n🔍 Sample Processed Devices (First 10):"
    @sample_issues[:processed_devices].each_with_index do |device, index|
      puts "\n#{index + 1}. Serial: #{device[:serial]}"
      puts "   Model: #{device[:model]}"
      puts "   Provision Status: #{device[:provision_status]}"
      puts "   Original MAC: '#{device[:original_mac]}'"
      puts "   Processed MAC: '#{device[:processed_mac]}'"
      puts "   MAC Array: #{device[:mac_addresses_array].inspect}"
      puts "   Final MAC Data: #{device[:final_mac_data].inspect}"
      puts "   Your Selected Code Result: #{device[:final_mac_data].any? ? 'Uses new MAC' : 'Would use existing asset MAC'}"
    end
  end

  def analyze_your_selected_code
    puts "\n🎯 YOUR SELECTED CODE ANALYSIS"
    puts "-" * 40
    puts "Code: mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses"

    devices_with_mac = @processing_stats[:valid_mac_addresses]
    devices_without_mac = @processing_stats[:empty_mac_addresses] + @processing_stats[:nil_mac_addresses]

    puts "\nReal Impact Analysis:"
    puts "Devices with MAC data: #{devices_with_mac}"
    puts "Devices without MAC data: #{devices_without_mac}"

    puts "\nWhat happens with your selected code:"
    puts "✅ #{devices_with_mac} devices: mac_addresses.present? = true → uses new MAC data"
    puts "⚠️  #{devices_without_mac} devices: mac_addresses.present? = false → uses existing asset MAC"
    puts "   └─ If existing asset also has empty MAC → results in empty MAC array"
    puts "   └─ This creates your 684 empty MAC rows in database"

    puts "\nThe Problem:"
    puts "- All #{@processing_stats[:total_devices]} devices get processed by the worker"
    puts "- But #{devices_without_mac} devices end up with empty MAC arrays"
    puts "- These devices may not be properly counted or deduplicated"
    puts "- This could explain the missing #{@processing_stats[:total_devices] - 4957} devices"
  end

  def provide_recommendations
    puts "\n💡 FINDINGS & RECOMMENDATIONS"
    puts "=" * 35

    devices_without_mac = @processing_stats[:empty_mac_addresses] + @processing_stats[:nil_mac_addresses]

    puts "\n🎯 Key Findings:"
    puts "- Your CSV contains #{@processing_stats[:total_devices]} total Chrome devices"
    puts "- #{devices_without_mac} devices (#{percentage(devices_without_mac, @processing_stats[:total_devices])}%) have no MAC address"
    puts "- The worker processes ALL devices (no filtering based on MAC)"
    puts "- Your selected code causes #{devices_without_mac} devices to have empty MAC arrays"
    puts "- This explains your 684 empty MAC rows in the database"

    puts "\n� The Real Problem:"
    puts "- It's NOT that devices are being filtered out"
    puts "- It's that devices without MAC addresses can't be properly:"
    puts "  • Deduplicated (multiple entries for same device)"
    puts "  • Matched with existing assets"
    puts "  • Identified uniquely"
    puts "- This leads to counting/inventory discrepancies"

    puts "\n🔧 Recommended Solutions:"
    puts "1. Use serial numbers as primary identifier instead of MAC addresses"
    puts "2. Add logging to track devices with empty MAC arrays"
    puts "3. Implement better deduplication logic for devices without MACs"
    puts "4. Investigate why Google Workspace doesn't provide MAC addresses for these devices"

    puts "\n� Expected Impact:"
    puts "- If you fix the MAC address issue, you should see closer to #{@processing_stats[:total_devices]} devices"
    puts "- Current gap: #{@processing_stats[:total_devices] - 4957} devices"
    puts "- Likely cause: Deduplication/counting issues with empty MAC devices"
  end

  def percentage(part, total)
    return 0 if total == 0
    ((part.to_f / total) * 100).round(2)
  end
end

# Run the simulation
if __FILE__ == $0
  csv_path = "/home/<USER>/Downloads/nsboro google devices console.csv"
  simulator = ChromeDevicesProcessingSimulator.new(csv_path)
  simulator.run_simulation
end
