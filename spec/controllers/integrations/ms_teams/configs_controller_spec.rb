require 'rails_helper'
include CompanyUserHelper

describe Integrations::MsTeams::ConfigsController, type: :controller do
  create_company_and_user

  let!(:ms_teams_config) { create(:ms_teams_config, company: company) }

  describe '#index' do
    it 'will return all the company MS Teams integrations' do
      get :index
      parsed_response = JSON.parse(response.body)

      expect(parsed_response.count).to eq(1)
      expect(parsed_response.first['team_name']).to eq('Help desk alerts')
      expect(parsed_response.first['channel_name']).to eq('helpdesk')
    end
  end

  describe "#destroy" do
    it "will destroy company MS Teams integration" do
      delete :destroy, params: { id: ms_teams_config.id }
      parsed_response = JSON.parse(response.body)

      expect(parsed_response['message']).to eq('Integration deleted successfully')
      expect(response.status).to eq(200)
    end

    it "will raise error on company MS Teams integration deletion" do
      delete :destroy, params: { id: 0 }
      expect(response.status).to eq(404)
    end
  end
end
