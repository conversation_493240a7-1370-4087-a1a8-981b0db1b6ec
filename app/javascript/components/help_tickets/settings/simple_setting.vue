<template>
  <tbody :data-tc="toTitle(setting.name)">
    <tr>
      <td class="position-relative">
        <span 
          v-if="shouldShowOverlay"
          v-tooltip.right="'These settings will only take affect if the workspace is not hidden'" 
          class="box--disabled-overlay t-0"
        />
        <p class="not-as-small font-weight-bold mb-1">
          {{ toTitle(setting.name) }}
        </p>
        <p
          v-if="setting.settingType == 'allow_faq_page_to_logged_out_users'"
          class="text-secondary small mb-0 p--responsive"
        >
          {{ setting.description }} <br>
          URL for your FAQ page will be
          <a
            :href="faqsLink"
            target="_blank"
          >
            {{ faqsLink }}
          </a>
        </p>
        <p
          v-else-if="setting.settingType == 'allow_public_articles_to_logged_out_users'"
          class="text-secondary small mb-0 p--responsive"
        >
          {{ setting.description }} <br>
          URL for your Public articles page will be
          <a
            :href="knowledgeBaseLink"
            target="_blank"
          >
            {{ knowledgeBaseLink }} 
          </a>
        </p>
        <p
          v-else-if="setting.settingType == 'set_assigned_to_first_agent_respond_to_ticket'"
          class="text-secondary small mb-0 p--responsive input-group"
        >
          {{ setting.description }}
          <span
            class="form-group mt-3"
          >
            <label>Assignee's Groups</label>
            <i
              v-tooltip="'Only responses from members to these groups will be considered for ticket assignment.'"
              class="nulodgicon-information-circled"
            />
            <div class="mt-1 w-75">
              <assignees
                :contributors="assigneeGroups"
                :isHelpdeskSetting="true"
                :disabled="!enabled"
                @remove="removeGroup"
              />
            </div>
            <div
              class="d-flex"
              style="width: 25rem"
            >
              <groups-select
                name="name"
                compact
                :excludes="excluedGroups"
                :disabled="!enabled"
                @select="selectGroup"
                @remove="removeGroup"
              />
              <button
                class="btn btn-primary w-auto ml-5"
                type="button"
                :disabled="!enabled"
                @click="setAssigneeGroups"
              >
                Save
              </button>
            </div>
          </span>
        </p>
        <p
          v-else-if="setting.settingType == 'customize_ticket_number_for_help_desk'"
          class="text-secondary small mb-0 p--responsive input-group"
        >
          {{ setting.description }} <br>
          <span class="mb-4 col-5 px-0 mr-2 mt-1">
            Prefix
            <input
              v-model="simpleSetting.options.customPrefix"
              v-validate="'required|alpha'"
              :disabled="!enabled"
              type="text"
              class="d-inline-block form-control mb-1"
              :class="{ 'is-invalid': errors.has('custom prefix') }"
              name="custom prefix"
              data-tc-input="prefix"
            >
            <span
              v-if="errors.has('custom prefix')"
              class="text-danger smallest"
            >
              {{ errors.first('custom prefix') }}
            </span>
          </span>
          <span class="mb-4 col-5 px-0 mt-1">
            Initial Ticket Number
            <input
              v-model="simpleSetting.options.customTicketNumber"
              v-validate="'required|integer'"
              :disabled="!enabled"
              type="number"
              class="d-inline-block form-control mb-1"
              :class="{ 'is-invalid': errors.has('custom ticket number') }"
              min="1"
              name="custom ticket number"
              data-tc-input="ticket number"
            >
            <span
              v-if="errors.has('custom ticket number')"
              class="text-danger smallest"
            >
              {{ errors.first('custom ticket number') }}
            </span>
          </span>
          <span class="mt-3 ml-5 py-1">
            <button
              class="btn btn-primary"
              type="button"
              :disabled="!enabled || !!errors.items.length"
              :data-tc-save-btn="enabled"
              :data-tc-disable-save="!enabled || !!errors.items.length"
              @click="customizeTicketNumber"
            >
              Save
            </button>
          </span>
        </p>
        <p
          v-else-if="setting.settingType === 'expand_add_new_comment_section'"
          class="text-secondary small mb-0 p--responsive"
        >
          {{ setting.description }} <br>
        </p>
        <p
          v-else
          class="text-secondary small mb-0 p--responsive"
        >
          {{ setting.description }}
        </p>
      </td>
      <td class="text-center position-relative">
        <span 
          v-if="shouldShowOverlay"
          class="box--disabled-overlay t-0"
        />
        <material-toggle
          class="mt-1"
          :init-active="enabled"
          @toggle-sample="update"
        />
      </td>
    </tr>
  </tbody>
</template>

<script>
  import strings from 'mixins/string';
  import helpdeskSetting from 'mixins/helpdesk_setting';
  import http from "common/http";
  import GroupsSelect from 'components/shared/groups_select.vue';
  import Assignees from 'components/shared/assignees.vue';
  import { mapGetters } from 'vuex';
  import MaterialToggle from '../../shared/material_toggle.vue';

  export default {
    $_veeValidate: {
      validator: "new",
    },
    components: {
      MaterialToggle,
      GroupsSelect,
      Assignees,
    },
    mixins: [strings, helpdeskSetting],
    props: {
      setting: {
        required: true,
      },
    },
    data() {
      return {
        currentOrigin: document.location.origin,
        assigneeGroups: [],
        helpCenterSettings: [
          'select_new_ui_for_open_ticket_portal',
          'bg_color_for_open_ticket_portal',
          'hyperlinks_color_for_open_ticket_portal',
          'allow_public_custom_forms_to_logged_out_users',
          'allow_faq_page_to_logged_out_users',
          'allow_public_articles_to_logged_out_users',
        ],
      };
    },
    computed: {
      ...mapGetters(['helpCenterModernDesignEnabled', 'includeWorkspaceEnabled']),
      enabled() {
        return this.setting.enabled;
      },
      excluedGroups() {
        return this.assigneeGroups.map(g => g.id);
      },
      simpleSetting() {
        return this.setting;
      },
      knowledgeBaseLink() {
        return this.helpCenterModernDesignEnabled ? `${ this.currentOrigin }/help_center/knowledge_base` : `${ this.currentOrigin }/knowledge_base`;
      },
      faqsLink() {
        return this.helpCenterModernDesignEnabled ? `${ this.currentOrigin }/help_center/faqs` : `${ this.currentOrigin }/faqs`;
      },
      shouldShowOverlay() {
        return !this.includeWorkspaceEnabled && this.helpCenterSettings.includes(this.setting.settingType);
      },
    },
    mounted() {
      if (this.setting.settingType === 'customize_ticket_number_for_help_desk') {
        if (!this.setting.options.customPrefix) {
          this.simpleSetting.options = { customPrefix: "", customTicketNumber: 1 };
        }
      }
      if (this.setting.settingType === 'set_assigned_to_first_agent_respond_to_ticket') {
        if (!this.setting.options.assigneeGroups) {
          this.fetchAssigneeGroups(true).then(() => {
            this.simpleSetting.options = { assigneeGroups: this.assigneeGroups.map(g => g.contributorId) };
          });
        } else {
          this.fetchAssigneeGroups();
        }
      }
    },
    methods: {
      update() {
        const settingParams = {
          id: this.setting.id,
          companyId: this.setting.companyId,
          enabled: !this.setting.enabled,
          options: this.setting.options,
        };
        this.updateSetting(settingParams);
      },
      customizeTicketNumber() {
        this.$validator.validateAll().then((result) => {
          if (result) {
            http
              .put(`/helpdesk_settings/${this.setting.id}.json`, { setting: this.setting })
              .then(() => {
                this.emitSuccess(`Successfully applied custom ticket number settings to create new ticket numbers.`);
              })
              .catch(err => {
                this.emitError(`Sorry, we encountered an error updating this custom ticket number setting. ${err.response.data.message}`);
              });
          } else {
            this.emitError(`Please correct the highlighted errors before submitting.`);
          }
        });
      },
      fetchAssigneeGroups(defaultValue = false) {
        const groupParams = {
          groups_ids: this.setting.options.assigneeGroups,
          default_value: defaultValue,
        };
        return http
          .get(`/fetch_assignee_groups`,  { params: groupParams })
          .then(res => {
            this.assigneeGroups = res.data.gorups;
          })
          .catch(err => {
            this.emitError(`Something went wrong while fetcing assignee groups. ${err.response.data.message}`);
          });
      },
      setAssigneeGroups() {
        if (this.simpleSetting.options.assigneeGroups.length) {
          http
            .put(`/helpdesk_settings/${this.setting.id}.json`, { setting: this.setting })
            .then(() => {
              this.emitSuccess(`Successfully enabled automatic ticket assignment.`);
            })
            .catch(() => {
              this.emitError(`Sorry, we encountered an error updating automatic assignment setting.`);
            });
        } else {
          this.emitError('Automatic ticket assignment requires the presence of at least one group.');
        }
      },
      selectGroup(group) {
        this.assigneeGroups.push(group);
        this.simpleSetting.options.assigneeGroups.push(group.contributorId);
      },
      removeGroup(group) {
        const contributorIndex = this.setting.options.assigneeGroups.indexOf(group.contributorId);
        const assigneeGroupsIndex = this.assigneeGroups.findIndex(obj => obj.id === group.id);
        if (contributorIndex !== -1) {
          this.simpleSetting.options.assigneeGroups.splice(contributorIndex, 1);
        }
        if (assigneeGroupsIndex !== -1) {
          this.assigneeGroups.splice(assigneeGroupsIndex, 1);
        }
      },
    },
  };
</script>
