class CompanyUserActivity < ActiveRecord::Base
  include HandleCompanyCacheKeys
  include CompanyUserCache

  belongs_to :company_user
  belongs_to :owner, class_name: "CompanyUser", foreign_key: "owner_id", optional: true

  enum activity_type: %w(created added updated removed archived invited access imported permission email_requested)

  validates :activity_type, presence: true

  delegate :company, to: :company_user

  before_create :set_contributor_name
  
  def as_json(options = nil)
    company = self.company
    user_name = company.company_users.find_by_cache(id: self.company_user_id)&.name 
    owner_name = company.company_users.find_by_cache(id: self.owner_id)&.name || company.contributors.find_by(id: self.owner_id)&.name
    set_location_or_group(self)
    
    super.merge(user_name: user_name, owner_name: owner_name)
  end     

  def set_location_or_group(activity)
    if activity.data["field_type"].present?
      if activity.data['current_value'].present? 
        if activity.data["field_type"] == "location_list"
          location_name = Location.find_by(id: activity.data['current_value'])&.name
          activity.data['current_value'] = location_name if location_name.present?
        elsif activity.data["field_type"] == "group"
          group_name = Group.find_by(id: activity.data['current_value'])&.name
          activity.data['current_value'] = group_name if group_name.present?
        end
      end
      if activity.data['previous_value'].present? 
        if activity.data["field_type"] == "location_list"
          location_name = Location.find_by(id: activity.data['previous_value'])&.name
          activity.data['previous_value'] = location_name if location_name.present?
        elsif activity.data["field_type"] == "group"
          group_name = Group.find_by(id: activity.data['previous_value'])&.name
          activity.data['previous_value'] = group_name if group_name.present?
        end
      end
    end
    activity
  end

  def set_contributor_name
    if self.data["field_type"].present? && self.data["field_type"] == "people_list"
      if self.data['current_value'].present?
        contributor = find_contributor_by_id(self.data['current_value'])
        contributor_name = contributor&.name
        self.data['current_value'] = contributor_name if contributor_name.present?
      end

      if self.data['previous_value'].present?
        contributor = find_contributor_by_id(self.data['previous_value'])
        contributor_name = contributor&.name
        self.data['previous_value'] = contributor_name if contributor_name.present?
      end
    end
  end                   
end
