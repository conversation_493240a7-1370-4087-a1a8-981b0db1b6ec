ActiveAdmin.register Prompt, as: 'ticket_summary_prompts' do
  menu parent: 'Manage Services', priority: 7
  breadcrumb do
    ['admin', 'manage services']
  end

  actions :all
  config.batch_actions = false

  index do
    column :name
    column 'Defaults' do |option|
      status_tag 'Global Default' if option.is_active
      status_tag 'My Default', class: "green" if option.admin_default_prompt.include?(current_user.id)
    end
    column :model
    column :prompts_template
    column :temperature
    column :created_at
    column :updated_at
    actions defaults: false do |prompt|
      item 'View', admin_ticket_summary_prompt_path(prompt), class: 'member_link'
      item 'Edit', edit_admin_ticket_summary_prompt_path(prompt), class: 'member_link'
      item 'Delete', admin_ticket_summary_prompt_path(prompt),
           method: :delete,
           data: { confirm: 'Are you sure you want to delete this prompt?' },
           class: 'member_link delete_link'
    end
  end

  show do
    attributes_table do
      row :name
      row :model
      row 'Defaults' do |option|
        status_tag 'Global Default' if option.is_active
        status_tag 'My Default', class: "green" if option.admin_default_prompt.include?(current_user.id)
      end
      row :prompts_template
      row :response_format
      row :fields_description
      row :example_output
      row :additional_instructions
      row :temperature
      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs 'Prompt Details' do
      f.input :name
      f.input :model, as: :select, collection: ['gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano']
      f.input :is_active, label: 'Global Default', as: :boolean, input_html: { class: 'toggle-status' }
      f.input :admin_default_prompt,
              as: :boolean,
              label: 'My Default',
              input_html: {
               class: 'toggle-status',
               checked: f.object.admin_default_prompt.include?(current_user.id)
              }
      f.input :temperature,
              as: :select,
              label: 'Temperature',
              collection: (1..10).map { |i| (i / 10.0).round(1) },
              include_blank: false
      f.input :prompts_template, as: :select, collection: PromptsTemplate.all.pluck(:name, :id)
      f.input :response_format, input_html: { class: 'handle-textarea-height' }
      f.input :fields_description, input_html: { class: 'handle-textarea-height' }
      f.input :example_output, input_html: { class: 'handle-textarea-height' }
      f.input :additional_instructions, input_html: { class: 'handle-textarea-height' }
    end
    f.actions
  end

  controller do
    def create
      updated_params = permitted_params[:prompt].to_h
      updated_params[:admin_default_prompt] = [current_user.id]
      prompt = Prompt.new(updated_params)

      if prompt.save
        remove_from_other_prompts(prompt.id)
        redirect_to admin_ticket_summary_prompts_path, notice: "Prompt was successfully updated."
      else
        redirect_to admin_ticket_summary_prompt_path(prompt), alert: prompt.errors.full_messages.to_sentence
      end
    end

    def update
      prompt = Prompt.find(params[:id])
      user_id = current_user.id
      user_ids = prompt.admin_default_prompt.dup

      updated_params = permitted_params[:prompt].to_h
      should_add_user = updated_params[:admin_default_prompt].to_i == 1

      updated_params[:admin_default_prompt] = should_add_user ? (user_ids | [user_id]) : (user_ids - [user_id])

      if prompt.update(updated_params)
        remove_from_other_prompts(prompt.id) if should_add_user
        redirect_to admin_ticket_summary_prompts_path, notice: "Prompt was successfully updated."
      else
        redirect_to admin_ticket_summary_prompt_path(prompt), alert: prompt.errors.full_messages.to_sentence
      end
    end

    def destroy
      prompt = Prompt.find(params[:id])
      if prompt.destroy
        redirect_to admin_ticket_summary_prompts_path, notice: "Prompt was successfully deleted."
      else
        redirect_to admin_ticket_summary_prompt_path(prompt), alert: prompt.errors.full_messages.to_sentence
      end
    end

    def remove_from_other_prompts(prompt_id)
      Prompt.where.not(id: prompt_id).find_each do |prompt|
        next unless prompt.admin_default_prompt.include?(current_user.id)

        updated_ids = prompt.admin_default_prompt - [current_user.id]
        prompt.update_column(:admin_default_prompt, updated_ids)
      end
    end

    def permitted_params
      params.permit(prompt: [
        :name,
        :model,
        :is_active,
        :response_format,
        :fields_description,
        :example_output,
        :prompts_template_id,
        :temperature,
        :admin_default_prompt,
        :additional_instructions
      ])
    end
  end
end
