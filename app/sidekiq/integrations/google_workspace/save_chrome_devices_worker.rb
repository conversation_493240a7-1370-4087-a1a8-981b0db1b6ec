class Integrations::GoogleWorkspace::SaveChromeDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include GoogleWorkspaceCommonMethods
  include IntegrationAlertsHelper

  require 'google/apis/admin_directory_v1'

  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, discovered_asset_count = 0, managed_asset_count = 0)
    @starting_time = Time.current
    @config = Integrations::GoogleWorkspace::Config.find_by(id: config_id)
    return if @config.nil?

    @import_target = @config.import_type
    @new_discovered_assets_count = discovered_asset_count
    @new_managed_assets_count = managed_asset_count
    @first_time_flag = first_time_flag
    pusher(@config, true, 0.6)
    refresh_access_token
    save_chrome_devices
    pusher(@config, true, 0.9)
    company_integration.assign_attributes(sync_status: :successful,
                                          status: true,
                                          last_synced_at: DateTime.now,
                                          active: true,
                                          error_message: nil,
                                          notified: nil,
                                          failure_count: 0,
                                          alert_info: IntegrationAlertsHelper::SUCCESS_STATE)
    company_integration.save!
    pusher(@config, true, 1, {
      new_google_workspace_discovered_assets: @new_discovered_assets_count,
      new_google_workspace_managed_assets: @new_managed_assets_count
    })
    intg_duration_analysis(@company.id, @first_time_flag)
  rescue Exception => e
    company_integration.assign_attributes(sync_status: :failed,
                                          status: false,
                                          last_synced_at: DateTime.now,
                                          error_message: e.message,
                                          failure_count: company_integration.failure_count + 1,
                                          alert_info: IntegrationAlertsHelper::ERROR_STATE)
    company_integration.save!
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'google_workspace_integration').to_json)
    pusher(@config, false, 0)
  end

  def company
    @company ||= @config.company
  end

  def company_integration
    @company_integration ||= @config.company_integration
  end

  def chromeos_devices
    next_page_token = nil
    all_devices = []

    loop do 
      result = client.fetch_all_chrome_os_devices(next_page_token)
      all_devices.concat(result.chromeosdevices) if result.chromeosdevices
      next_page_token = result.next_page_token
      break unless next_page_token
    end
    return all_devices
  end

  def save_chrome_devices
    chromeos_devices.each do |device|
      @discovered_asset = nil
      @managed_asset = nil
      mac_addresses = []
      display_name = device.model.presence || "ChromeOS #{device.os_version}"
      serial_number = device.serial_number
      model = device.model
      operating_system = device.try(:chrome_os_type) || "ChromeOS #{device.os_version}"
      os_name = "ChromeOS"
      os_version = device.os_version
      mac_address = device.mac_address.try(:upcase)
      mac_addresses << mac_address if mac_address_valid?(mac_address)
      source = 'google_workspace'
      last_check_in_time = device.last_sync

      @discovered_asset = find_discovered_asset(company, false, serial_number, mac_addresses, display_name, nil, nil, false)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: company.id)

      mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      ip_address_data = @discovered_asset.ip_address
      serial_data = serial_number.present? ? serial_number : @discovered_asset.machine_serial_no

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        machine_serial_no: serial_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        asset_type: get_asset_type("Laptop"),
        os: operating_system,
        os_name: os_name,
        os_version: os_version,
        model: model,
        source: source,
        discovered_asset_type: :device,
        lower_precedence: @is_lower_precedence,
        last_check_in_time: last_check_in_time
      )

      @discovered_asset.asset_softwares.find_or_initialize_by(
        software_type: 'Operating System',
        name: os_name || ""
      )

      managed_asset = @discovered_asset.managed_asset || find_managed_asset(company, serial_data, mac_addresses_data, display_name_data, nil)

      if managed_asset.present?
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset_id = managed_asset.id
      end

      assign_discovered_assets_hardware_details(device)
      is_new = @discovered_asset.new_record?
      @discovered_asset.save
      @new_discovered_assets_count += 1 if is_new

      asset_source_data = {
        display_name: display_name,
        machine_serial_no: serial_data,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        os_name: os_name,
        source: source
      }

      add_source(asset_source_data)
      @discovered_asset.reload

      if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
        BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
        @new_managed_assets_count += 1
      end
    end
  end

  def assign_discovered_assets_hardware_details(device)
    hardware_detail = @discovered_asset.discovered_assets_hardware_detail ||= DiscoveredAssetsHardwareDetail.new
    hardware_detail.assign_attributes(
      memory: memory_in_gb(device.system_ram_total)
    )
  end

  def memory_in_gb(bytes)
    gb = bytes.to_f / (1024 * 1024 * 1024)
    gb.round
  end

  def client
    Integrations::GoogleWorkspace::FetchData.new(company.id)
  end
end
