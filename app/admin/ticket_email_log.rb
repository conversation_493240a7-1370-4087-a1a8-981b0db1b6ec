ActiveAdmin.register Logs::TicketEmailLog, as: 'ticket_email_log' do
  menu parent: "Logs", label: "Help Ticket Email Logs", priority: 5
  breadcrumb do
    ['admin', 'logs']
  end
  filter :to, as: :string, filters: [:equals, :contains, :starts_with, :ends_with]
  filter :from, as: :string, filters: [:equals, :contains, :starts_with, :ends_with]
  filter :subject, as: :string, filters: [:contains], label: "Subject"
  filter :body_text, as: :string, filters: [:contains], label: "Email Body Text"
  filter :log_type, as: :select, collection: Logs::TicketEmailLog.log_types.map {|key, value| [key, value]}
  filter :company, as: :select, collection: Company.order("lower(name) ASC")
  filter :email_message_id, label: "Email Message Id"

  index do
    id_column
    column :to
    column :from
    column :log_type
    column ("S3 Message Id") { |ticket_email_log| "#{ticket_email_log.s3_message_id}" }
    column ("Email Message Id") { |ticket_email_log| "#{ticket_email_log.email_message_id}" }
    column ("Company") { |ticket_email_log| "#{ticket_email_log.company&.name}" }
    column :error
    column :source
    column ("created_at") { |event| "#{event.created_at.to_written_time}" }
    column ("updated_at") { |event| "#{event.updated_at.to_written_time}" }
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
