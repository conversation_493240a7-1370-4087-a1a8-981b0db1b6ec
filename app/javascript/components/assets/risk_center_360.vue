<template>
  <div>
    <div>
      <div class="row mt-5">
        <div class="col-4 d-none flex-column">
          <h5 class="font-weight-normal mb-3 asset-summary-heading" />
          <div class="box bg-very-light pl-1 pr-2">
            <i class="icon genuicon-device-health mr-2 ml-n2 h4 mb-0" />
          </div>
        </div>

        <div class="col-4 d-flex flex-column">
          <h5
            class="font-weight-normal mb-3 asset-summary-heading d-flex align-items-center"
          >
            <i class="icon genuicon-device-health mr-2 ml-0 h4 mb-0" />
            Risk Center Summary
          </h5>
        </div>

        <div class="col-8 d-flex justify-content-between">
          <div>
            <span
              v-if="!hasAgentInstalled"
              class="py-1 px-2 mb-1 refresh-alert d-flex align-center d-block"
            >
              <i class="icon genuicon-refresh smallest text-muted d-flex align-self-center mr-1" />
              <span class="small text-secondary">Agent not installed</span>
            </span>
          </div>
          <div class="d-flex align-items-start">
            <filters
              class="risk-filters-container"
              is-risk-center-module
              @filters-updated="filtersUpdate"
            />
            <div
              class="d-flex align-items-center ml-3 pb-3 justify-content-end"
            >
              <chart-type-button
                :show-bar-chart="false"
                :default-type="'horizontalBar'"
                @set-chart-filter="updateChartType"
              />
            </div>
          </div>
          
        </div>
      </div>

      <div class="row mb-5">
        <div class="col-4 d-flex flex-column">
          <div class="box bg-very-light pl-1 pr-2">
            <div
              v-if="!hasAgentInstalled"
              class="text-center px-3 mx-auto h-100"
            >
              <h6 class="h5 mx-auto mb-4 mt-2">
                Setup your Risk Center
              </h6>
              <router-link to="/discovery_tools/connectors?setup=risk_center">
                <img
                  src="https://nulodgic-static-assets.s3.amazonaws.com/images/discovery_agent_windows.svg"
                  height="75px"
                  class="my-3"
                >
              </router-link>
              <div
                v-if="isWrite"
                class="text-secondary font-weight-normal not-as-small align-self-start mt-3"
              >
                First learn about the 
                <router-link to="/discovery_tools/connectors?setup=risk_center">
                  Genuity Agent
                </router-link>.
              </div>
            </div>
            <div
              v-else-if="!loadingAssetsTypeCount && !chartDataPresent"
              class="text-center px-3"
            >
              <h6 class="h5 mx-auto mb-0">
                Not tracking anything here, yet.
              </h6>
              <span
                v-if="isWrite"
                class="text-secondary font-weight-normal not-as-small align-self-start"
              >
                Please customize your metrics below to best match your assets.
              </span>
            </div>
            <div
              v-else-if="!loadingAssetsTypeCount"
              class="d-flex w-100"
            >
              <div class="col-8 px-0">
                <div class="asset-summary">
                  <insights-donut-chart
                    :custom-colors="chartCustomColors"
                    :data="chartData.datasets[0]"
                    :donut-height="'175px'"
                    :inner-text-header="innerTextHeader"
                  />
                </div>
              </div>
              <div
                class="col-lg-4 pl-2 pr-0"
                :class="{ 'insights-summary': applySummaryClass }"
              >
                <chart-legend
                  is-assets-insights
                  :custom-colors="chartCustomColors"
                  :data="assetsCount"
                  @option-filter="getOptionData"
                />
              </div>
            </div>
            <donut-with-legends-skeleton v-else />
          </div>
        </div>

        <div class="col-8">

          <!-- <i class="icon genuicon-refresh small text-secondary position-absolute pt-2 mt-0.5" />
          <span class="small text-secondary position-absolute mt-2 pl-3 ml-0.5">{{ lastRiskCenterAgentUpdate }}</span> -->

          <!-- temporarily commented code to display agent update label -->

          <assets-summary
            v-if="!loadingAssetsByChartData && hasAgentInstalled"
            :is-asset-risk-center = "isAssetRiskCenter"
            current-module="assets"
            :data="riskData.widgetsSummaryData"
            :is-any-widget-enabled="isAnyWidgetEnabled"
            :custom-fields="['Devices Needing Attention']"
            detail-component="insight-asset-detail"
            :chart-type="chartType"
          />

          <insights-summary-skeleton 
            v-else-if="loadingAssetsByChartData && hasAgentInstalled"
            :is-asset-risk-center = "isAssetRiskCenter"
          />

          <div
            v-if="!hasAgentInstalled"
            class="box box--natural-height bg-very-light p-4 position-relative install-agent-container"
            :class="{ 'insights-summary-skeleton': isAssetRiskCenter }"
          >
            <div
              v-if="isWrite"
              class="text-secondary font-weight-normal align-self-start"
            >
              <div>Risk center metrics give you a quick and easy overview of the security health of your desktops, laptops, and servers.</div>

              <div class="mt-3 mb-2 ml-2">
                <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">1</div>
                <h6 class="d-inline pl-2 text-secondary align-middle">
                  Install the read-only <router-link to="/discovery_tools/connectors?setup=risk_center">Genuity Agent</router-link>
                </h6>
                <p class="small text-muted ml-5 pl-1 mr-3 mt-1 mb-2">
                  Keep your device info up-to-date, hassle-free. The Genuity Agent is a read-only password-protected application that runs in the background and scans your device for up-to-date information to update the system.
                </p>
              </div>

              <div
                class="py-1 px-2 mb-0 refresh-alert d-flex align-center border-success-light position-relative not-as-small"
                style="width: fit-content; margin-left: 3rem;"
              >
                The Genuity Agent is read only – it has no abilty to make changes to your systems.
              </div>

              <div class="mb-3 mt-4 ml-2">
                <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">2</div>
                <h6 class="d-inline pl-2 text-secondary align-middle">
                  Customize your Risk Center Metrics
                </h6>
                <p class="small text-muted ml-5 pl-1 mr-3 mt-1">
                  Our prebuilt defaults will get you started and each risk center metric can be configured to best match the devices and software you use most. 
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <h5 class="font-weight-normal mb-0 asset-summary-heading d-flex align-items-center">
      <i class="icon nulodgicon-stats-bars mr-2 ml-0 h4 mb-0" />
      Risk Metrics
    </h5>
    <div
      :key="widgetsBox"
      :class="{ 'row': riskCenterWidgetsData.length }"
    >
      <draggable
        class="row"
        v-bind="dragOptions"
        handle=".handle"
        dragover-bubble="true"
        animation="200"
        :class="{ 'w-100': allWidgets && allWidgets.length == 1 }"
        :set-data="modifyDragItem"
        :value="allWidgets"
        @change="updatePosition"
      >
        <template
          v-for="(title, index) in Object.keys(riskCenterWidgetsData)"
        >
          <component
            :is="'chart-data-card'"
            v-if="riskCenterWidgetsData[title]"
            :key="index"
            module-name="risk center"
            class="col-4 my-3 custom-widget"
            :card-title="riskCenterWidgetsData[title].label"
            is-asset-risk-center
            :card-icon-class="riskCenterWidgetsData[title].icon"
            detail-component="insight-asset-detail"
            :data="riskCenterWidgetsData[title]"
            :default-chart-type="defaultChartType(getWidgetName(title))"
            :custom-colors="riskCenterChartCustomColors"
            :is-write="isWrite"
            @set-chart-type="setChartType($event, getWidgetName(title))"
            @widget-updated="handleWidgetUpdate"
            @widget-removed="openRemoveMetricModal(title)"
          />
        </template>
        <div
          v-if="!loadingAssetsByChartData"
          class="col-4 my-3 custom-widget"
        >
          <div class="h-100">
            <span
              v-if="!hasAgentInstalled"
              class="box--disabled-overlay t-0"
            />
            <div
              class="box box--with-hover box--with-title box--natural-height widget-box h-100 justify-content-center"
              @click="openCustomizeDataModal"
            >
              <div class="mb-3 pr-3">
                <div class="h4 font-weight-normal mb-3">
                  Manage your metrics
                </div>
                <p
                  class="m-auto text-center not-as-small"
                  style="max-width: 12rem;"
                >
                  Create and customize your risk center metrics
                </p>
              </div>
              <i class="genuicon-plus-minus icon" />
            </div>
            <Teleport to="body">
              <sweet-modal
                ref="customizeDataModal"
                v-sweet-esc
                modal-theme="right"
                :width="'60%'"
                @close="handleModalClose"
              >
                <template slot="title">
                  <div class="d-flex align-items-center h-100">
                    <h4 class="mb-0">
                      <span class="h5 mr-0.5 pr-1 pl-2 mb-0 ml-0 pt-1 mt-0.5" />
                      {{ ` Create New Metric` }}
                    </h4>
                  </div>
                </template>
                <CustomizeMetricModal
                  ref="customizeMetricModal"
                  :type="'Create New Metric'"
                  @widget-updated="handleWidgetUpdate"
                  @close-modal="closeCustomizeDataModal"
                />
              </sweet-modal>
            </Teleport>
          </div>
        </div>
      </draggable>
      <sweet-modal
        ref="removeMetricModal"
        v-sweet-esc
        title="Delete Metric"
      >
        <template slot="default">
          <p>
            Are you sure you want to delete this metric?
          </p>
          <div class="sweet-buttons sweet-custom-footer pb-0 pt-3 border-top">
            <button
              slot="button"
              class="btn btn-link text-secondary mr-2"
              @click.prevent="closeRemoveMetricModal"
            >
              Cancel
            </button>
            <button
              slot="button"
              class="btn btn-primary"
              @click.prevent="deleteWidget"
            >
              Delete
            </button>
          </div>
        </template>
      </sweet-modal>
    </div>
    <div v-if="showListDetails">
      <sweet-modal
        ref="viewDetail"
        v-sweet-esc
        width="90%"
        title="Asset Type"
      >
        <insight-asset-detail
          ref="insightDetail"
          :is-asset-risk-center = "isAssetRiskCenter"
          :data="assetsCount"
          :insight-type="'Asset Type'"
          :module-type="'assets'"
        />
      </sweet-modal>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import _get from 'lodash/get';
  import math from 'mixins/math';
  import colorCalcs from 'mixins/color_calculations';
  import _sortBy from 'lodash/sortBy';
  import _cloneDeep from 'lodash/cloneDeep';
  import { mapMutations, mapGetters, mapActions} from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import { insightCardColors, riskCenter360CardColors } from 'common/chart_colors';
  import ChartDataCard from '../shared/chart_data_card.vue';
  import AssetsSummary from '../shared/insights_summary.vue';
  import Filters from './filters.vue';
  import InsightsDonutChart from '../shared/custom_donut_chart.vue';
  import ChartLegend from '../shared/chart_legends.vue';
  import string from '../../mixins/string';
  import insights from '../../mixins/asset_insights';
  import assetRiskCenter from '../../mixins/asset_risk_center';
  import mockRiskCenterData from '../../mixins/mock_risk_center_data';
  import InsightAssetDetail from './insights_asset_details.vue';
  import DonutWithLegendsSkeleton from '../shared/skeletons/donut_with_legends_skeleton.vue';
  import InsightsSummarySkeleton from '../shared/skeletons/insights_summary_skeleton.vue';
  import CustomizeMetricModal from '../shared/customize_metric.vue';
  import ChartTypeButton from '../shared/chart_type_button.vue';

  export default {
    components: {
      SweetModal,
      ChartDataCard,
      AssetsSummary,
      Filters,
      InsightsDonutChart,
      ChartLegend,
      InsightAssetDetail,
      DonutWithLegendsSkeleton,
      InsightsSummarySkeleton,
      CustomizeMetricModal,
      ChartTypeButton,
    },
    mixins: [math, colorCalcs, string, permissionsHelper, insights, assetRiskCenter, mockRiskCenterData],
    data() {
      return {
        loadingAssetsByChartData: true,
        loadingAssetsTypeCount: true,
        widgetLoader: false,
        widgetsBox: 0,
        allWidgets: null,
        riskData: null,
        summaryFilter: 'riskCenter',
        showListDetails: false,
        assetTypeCounts: false,
        widgetsSummaryData: null,
        activeAssetType: null,
        assetTypeDataCount: null,
        offset: 0,
        perPage: 100,
        currentLocationId: null,
        widgetToRemove: null,
        chartType: 'horizontalBar',
      };
    },
    computed: {
      ...mapGetters({
        assetInsightsSelectedChartTypes: 'assetInsightsSelectedChartTypes',
        riskCenterWidgetData: 'riskCenterWidgetData',
        assetTypeData: 'assetTypeData',
        assetTypes: 'assetTypes',
        isVerticalNav: 'GlobalStore/isVerticalNav',
        hasAgentInstalled: 'hasAgentInstalled',
      }),
      riskCenterWidgetsData() {
        return this.allWidgets?.reduce((result, widget) => {
          const widgetData = this.riskCenterWidgetData[widget.id];
          const widgetResult = result;
          widgetResult[`widget_${widget.name}`] = widgetData;
          return widgetResult;
        }, {}) || [];
      },
      chartCustomColors() {
        return insightCardColors;
      },
      riskCenterChartCustomColors() {
        return riskCenter360CardColors;
      },
      applySummaryClass() {
        return this.chartData.datasets[0].data.length > 3;
      },
      chartData() {
        const { percentArr, labelsArr, colorArr } = this.processDataSet();
        return {
          datasets: [
            {
              backgroundColor: colorArr,
              data: percentArr,
              labels: labelsArr,
            },
          ],
        };
      },
      assetsCount() {
        const dataSet = this.assetDataWithPercents();
        const dataArr = [];
        const labelsArr = [];

        dataSet.forEach((data) => {
          if (this.activeAssetType && this.activeAssetType.name === data.type) {
            dataArr.push(data.total);
            labelsArr.push(data.type);
          } else if (this.activeAssetType === null) {
            dataArr.push(data.total);
            labelsArr.push(data.type);
          }
        });

        return {
          data: dataArr,
          labels: labelsArr,
        };
      },
      isAnyWidgetEnabled() {
        return Object.values(this.riskCenterWidgetsData).some(widget => widget && widget.enabled);
      },
      isAssetRiskCenter(){
        return this.summaryFilter === 'riskCenter';
      },
    },
    watch: {
      locations() {
        this.selectLocation();
      },
    },
    destroyed() {
      this.currentLocationId = null;
      this.setCurrentLocation(null);
    },

    methods: {
      ...mapMutations([
        'setAssetInsightsSelectedChartTypes',
        'setAssetType',
        'setCurrentLocation',
      ]),
      ...mapActions([
        'getInsightsSummary',
        'fetchRiskCenterWidgetData',
        'fectchAgentStatus',
      ]),
      ...mapActions({
        fetchLocations: 'GlobalStore/fetchLocations',
      }),

      filtersUpdate() {
        this.$store.dispatch('fetchAssetInfo');
        this.$store.commit('setReloadTypes', true);
        this.fetchAssetTypeAndRiskSummary();
        this.getAssetRiskCenterWidgets();
      },
      openRemoveMetricModal(title) {
        this.widgetToRemove = title;
        this.$refs.removeMetricModal.open();
      },
      closeRemoveMetricModal() {
        this.$refs.removeMetricModal.close();
        this.widgetToRemove = null;
      },
      onWorkspaceChange() {
        this.checkParams();
        if (!this.locations?.length) {
          this.fetchLocations({ offset: this.offset, limit: this.perPage });
        }
        this.fectchAgentStatus();
        this.getAssetRiskCenterWidgets();
        this.$store.dispatch('fetchAssetInfo');
        this.fetchAssetTypeAndRiskSummary();
      },
      async fetchAssetTypeAndRiskSummary() {
        this.assetTypeCounts = true;
        this.loadingAssetsByChartData = true;

        await this.getRiskCenterData();
        this.assetTypeCounts = false;
        this.loadingAssetsTypeCount = false;

        await this.getRiskCenterData();
        this.loadingAssetsByChartData = false;
      },
      assetDataWithPercents() {
        const myArray = [];
        const typeData = this.riskData.assetsCount;
        if (typeData) {
          const total = typeData.reduce((a, b) => a + b.count, 0);

          typeData.forEach((data) => {
            myArray.push({ type: data.name, percent: this.calculatePercent(data.count, total), total: data.count, color: this.getTypeColor(data.name) });
          });
        }
        return myArray;
      },
      getWidgetName(title) {
        return this.riskCenterWidgetsData[title].name;
      },
      updateChartType(activeFilter) {
        this.chartType = activeFilter;
      },
      getAssetRiskCenterWidgets() {
        http
          .get('/managed_assets/assets/risk_center_widgets.json')
          .then((res) => {
            let widgets = res.data.allWidgets;

            // Check if there's an updated order in localStorage
            const savedOrder = JSON.parse(localStorage.getItem('riskCenterWidgets'));
            if (savedOrder) {
              widgets = _sortBy(widgets, (widget) => {
                const savedWidget = savedOrder.find(item => item.id === widget.id);
                return savedWidget ? savedWidget.order : widget.order;
              });
            }

            this.allWidgets = widgets;
            this.loadWidgetData(this.allWidgets);
          })
          .catch(() => {
            this.emitError('Sorry, there was an error gathering widgets. Please refresh the page and try again.');
          });
      },
      loadWidgetData(widgets) {
        widgets.forEach(widget => {
          this.widgetLoader = true;
          const params = { id: widget.id };
          this.fetchRiskCenterWidgetData(params).then(() => {
            this.widgetLoader = false;
          });
        });
      },
      handleWidgetUpdate(updatedWidget) {
        const index = this.allWidgets.findIndex(widget => widget.id === updatedWidget.id);
        if (index === -1) {
          this.allWidgets.push({
            id: updatedWidget.id,
            name: updatedWidget.name,
            enabled: updatedWidget.enabled,
          });
        } else {
          this.$set(this.allWidgets[index], 'name', updatedWidget.name);
          this.$set(this.allWidgets[index], 'enabled', updatedWidget.enabled);   
        }
        this.allWidgets = _sortBy(this.allWidgets, widget => widget.enabled ? 0 : 1);
        const cards = _cloneDeep(this.allWidgets);
        this.assignOrder(cards);

        this.loadWidgetData([updatedWidget]);
      },
      defaultChartType(title) {
        const storedChartType = this.assetInsightsSelectedChartTypes.find(val => val.id === title.toLowerCase());
        return typeof storedChartType === 'undefined' ? 'donut' : storedChartType.type;
      },
      setChartType(type, title) {
        this.setAssetInsightsSelectedChartTypes({'id': title.toLowerCase() , 'type': type});
      },
      getOptionData(option) {
        this.showListDetails = true;
        this.$nextTick(() => {
          this.$refs.viewDetail.open();
          this.$refs.insightDetail.setFilterName(option);
        });
      },
      dragOptions() {
        return { animation: 200, ghostClass: "ghost" };
      },
      updatePosition(data) {
        const movedElement = _get(data, 'moved.element');
        const newIndex = _get(data, 'moved.newIndex');
        const oldIndex = _get(data, 'moved.oldIndex');

        if (!movedElement) {
          return;
        }

        const cards = _cloneDeep(this.allWidgets);

        cards.splice(oldIndex, 1);
        cards.splice(newIndex, 0, movedElement);
        this.assignOrder(cards);
      },
      assignOrder(cards) {
        let idx = 0;
        const idMap = cards.map((card) => {
          idx += 1;
          return {
            id: card.id || card.cardId,
            order: idx,
          };
        });

        const updatedRiskCenterWidgets = this.allWidgets.map((widget) => {
          const matchingIdMapItem = idMap.find((item) => item.id === widget.id);
          if (matchingIdMapItem) {
            return {
              ...widget,
              order: matchingIdMapItem.order,
            };
          }
          return widget;
        });
        
        this.allWidgets = updatedRiskCenterWidgets;
        this.allWidgets = _sortBy(this.allWidgets, 'order');
        localStorage.setItem('riskCenterWidgets', JSON.stringify(this.allWidgets));
      },
      modifyDragItem(dataTransfer, originalEl) {
        const dragEl = originalEl.querySelector('.drag-placeholder');
        dataTransfer.setDragImage(dragEl, 16, 20);
      },
      checkParams() {
        const id = this.$route.query.location_id;
        if (id) {
          this.currentLocationId = id;
          this.selectLocation();
        }
      },
      selectLocation() {
        if (this.currentLocationId && this.locations?.length > 0) {
          this.locations.forEach((location) => {
            if (location.id === this.currentLocationId) {
              this.setCurrentLocation(location);
            }
          });
        } else {
          this.setCurrentLocation(null);
        }
      },
      deleteWidget() {
        const params = { id: this.riskCenterWidgetsData[this.widgetToRemove].id };
        http
          .delete(`/managed_assets/risk_center_widgets.json`, { params  })
          .then(() => {
            this.closeRemoveMetricModal();
            this.emitSuccess('widget deleted successfully!.');
            this.allWidgets = this.allWidgets.filter(widget => widget.id !== params.id);
          })
          .catch((error) => {
            this.closeRemoveMetricModal();
            this.emitError(`Sorry, there was an error deleting widget. (${error.response.data.message}).`);
          });
      },
      toggleFilterMenu() {
        this.$refs.dismissibleContainer.toggleOpen();
      },
    },
  };
</script>

<style lang="scss" scoped>
  $chart-type-dropdown-height: 28px;

  .asset-summary-heading {
    min-height: $chart-type-dropdown-height;
  }

  .genuicon-plus-minus {
    font-size: 200px;
    line-height: normal;
    color: #e5ebf9;
  }

  :deep(.percentage-bar) {
    background-color: $pastel-red-200 !important;
  }

  :deep() {
    .sweet-title {
      background-color: $themed-dark-drawer-bg;
      color: white;
    }

    .sweet-action-close {
      color: white !important;
    }

    .sweet-content-content {
      width: 100%;
    }
  }

  .install-agent-container {
    min-height: 16.2rem;
  }

 .refresh-alert {
  background: var(--themed-light);
  border-left: 4px solid #ffb648;
  border-radius: $border-radius-sm;
  display: block;
  position: absolute;
 }
</style>
