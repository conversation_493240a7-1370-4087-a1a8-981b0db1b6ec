class BulkDiscoveredAssetsUpdate
  include DiscoveredManagedAssetFinder
  include ReadReplicaDb

  def initialize(assets, company, company_user, used_by_contributor_id = nil)
    @assets = assets
    @company = company
    @company_user = company_user
    @used_by_contributor_id = used_by_contributor_id
  end

  def import_assets(from_worker = false)
    duplicate_asset_ids = []
    @from_worker = from_worker
    @assets.each do |asset|
      begin
        move_to_managed_asset(asset)
      rescue Exception => e
        duplicate_id = handle_duplicate_asset_error(e)
        duplicate_asset_ids << duplicate_id if duplicate_id
      end
    end
    Pusher.trigger("#{@company.id}assets",'discovered-assets', {})
    duplicate_asset_ids
  end

  def archive_assets
    @assets.update_all(status: "ignored")
  end

  def unarchive_assets
    @assets.find_each do |asset|
      update_asset_status asset
    end
  end

  def delete_assets
    @assets.destroy_all
  end

  def update_asset_status asset
    status = "incomplete"

    if asset.display_name.present? && asset.manufacturer.present? &&
       asset.mac_address.present? && asset.machine_serial_no.present?
      status = "ready_for_import"
    end
    asset.update(status: status)
  end

  def move_to_managed_asset(asset)
    @discovered_asset = nil
    @managed_asset = nil
    asset_id = asset["id"] || asset[:id]
    @discovered_asset = @company.discovered_assets.find_by_id(asset_id)
    display_name = asset["display_name"] || asset[:display_name]
    @discovered_asset.display_name = display_name

    if save_asset(@discovered_asset)
      disc_asset_type = asset["asset_type"] || asset[:asset_type]
      asset_type = @company.asset_types.find_by_name disc_asset_type
      type = asset_type || @company.asset_types.find_by_name("Other") || @company.asset_types.find_by_name("Unrecognized")

      # params company, serial_number, mac_addresses, display_name, manufacturer
      @managed_asset = find_managed_asset(@company, @discovered_asset.machine_serial_no, @discovered_asset.mac_addresses, @discovered_asset.display_name, @discovered_asset.manufacturer)

      if @managed_asset.present?
        already_existing_source = @managed_asset.sources.include?(@discovered_asset.source)
        @managed_asset.source  = @discovered_asset.source unless already_existing_source
      else
        @managed_asset = set_asset_params(type, asset)
        set_asset_attributes type.name
        set_assignment_information asset
        set_cost
      end
      set_cloud_asset_attributes
      @managed_asset.asset_tag = @discovered_asset.asset_tag

      if save_asset(@managed_asset)
        @discovered_asset.status = "imported"
        @discovered_asset.managed_asset_id = @managed_asset.id
        update_system_details unless @managed_asset.system_details.present?
        save_asset(@discovered_asset)
      end
    end
  rescue => e
    handle_duplicate_asset_error(e)
  end

  def update_system_details
    @discovered_asset.system_details.update_all(managed_asset_id: @managed_asset.id)
  end

  def set_cloud_asset_attributes
    @managed_asset.cloud_asset_attributes << @discovered_asset.cloud_asset_attributes
    @managed_asset.google_project = @discovered_asset.google_project if @discovered_asset.google_project.present?
  end

  def set_cost
    @managed_asset.build_cost(cost: 0.0)
  end

  def set_assignment_information asset
    department = @company.departments.find_or_create_by(name: asset["department"])
    @managed_asset.build_assignment_information(
      used_by_contributor_id: asset[:used_by_contributor_id] || asset["used_by_contributor_id"] || @used_by_contributor_id,
      managed_by_contributor_id: asset[:managed_by_contributor_id] || asset["managed_by_contributor_id"],
      department_id: asset[:department_id] || asset["department_id"] || department.id || ''
    )
  end

  def set_asset_params(type, asset)
    ManagedAsset.new({
      name:                   asset[:display_name] || asset["display_name"],
      company_asset_type_id:  type.id,
      creator_id:             @company_user&.contributor&.id,
      location_id:            asset[:location_id] || asset["location_id"],
      impact:                 asset[:impact] || asset["impact"],
      company_id:             @discovered_asset.company_id,
      manufacturer:           @discovered_asset.manufacturer || '',
      operating_system:       @discovered_asset.os,
      machine_serial_number:  @discovered_asset.machine_serial_no,
      update_by_source_at:    DateTime.now,
      created_by:             @company_user,
      mac_addresses:          @discovered_asset.mac_addresses,
      details:                @discovered_asset.optional_details,
      firmware:               @discovered_asset.firmware,
      model:                  @discovered_asset.model,
      os_version:             @discovered_asset.os_version,
      system_up_time:         @discovered_asset.system_up_time,
      system_uuid:            @discovered_asset.system_uuid,
      source:                 asset[:source] || asset["source"]
    })
  end

  def set_asset_attributes(parent)
    return if @company&.asset_types&.where&.not(name: "Other")&.find_by_name(parent).nil?
    os_name = @discovered_asset.os_name || @discovered_asset.os

    if os_name.present?
      asset_software = set_read_replica_db do
        @discovered_asset.asset_softwares.find_or_initialize_by(software_type: "Operating System", name: os_name)
      end
      asset_software.save! if asset_software.new_record?
      @managed_asset.asset_softwares << asset_software
    end

    return if parent == "Software"
    association_info = @company.asset_types.get_asset_fields(parent)
    association_klass = Object.const_get association_info[:name]
    assoc = association_klass.new

    if @discovered_asset.discovered_assets_hardware_detail
      valid_keys = assoc.attributes.keys - ["id", "created_at", "updated_at", "managed_asset_id"]
      valid_keys.each do |key|
        assoc[key] = @discovered_asset.discovered_assets_hardware_detail[key]
      end
    end

    assoc.save!
    @managed_asset.hardware_detail_type = assoc.class.name
    @managed_asset.hardware_detail_id = assoc.id
  end

  def save_asset(asset)
    begin
      @from_worker ? asset.save : asset.save!
    rescue ActiveRecord::RecordInvalid => e
      if e.message.include?("mac addresses has already been taken")
        if Rails.env.production?  
          Bugsnag.notify(
            "Error saving asset: #{e.message}",
            metadata: {
              company_id: @company.id,
              asset_details: asset.to_json,
              error_class: e.class.name,
              full_error_message: e.full_message,
              backtrace: e.backtrace[0..5]
            }
          )
        end
      else
        raise e
      end
    end
  end

  def handle_duplicate_asset_error(error)
    if error.message.include?("Managed asset has already been taken")
      return error.record.id
    else
      raise error
    end
  end
end
