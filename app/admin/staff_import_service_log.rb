ActiveAdmin.register Logs::StaffImportServiceLog, as: 'staff_import_service_log' do
  menu parent: 'Logs', priority: 15
  breadcrumb do
    ['admin', 'logs']
  end

  filter :by_company_id_in, as: :string, :label => 'Company Id'
  filter :by_company_name_in, as: :string, :label => 'Company Name'
  filter :by_company_subdomain_in, as: :string, :label => 'Company Subdomain'
  filter :created_at

  index do
    column :id
    column :company
    column :response do |log|
      log.response.nil? ? "Empty" : log.response.to_s.truncate(250)
    end
    column :attachment do |log|
      log.attachment.url.present? ? link_to('download', log.attachment.url) : 'No File'
    end
    column ("created_at") { |event| "#{event.created_at.to_written_time}" }
    actions
  end

  controller do
    include MultiCompany::GeneralScoping

    before_action :verify_super_admin_mfa
  end
end
