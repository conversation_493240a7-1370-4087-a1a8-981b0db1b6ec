<template>
  <div> 
    <multi-select
      :options="metricOptions"
      :value="value"
      :placeholder="placeholder"
      :searchable="searchable"
      :close-on-select="closeOnSelect"
      :internal-search="true"
      @select="onSelect"
      @remove="onRemove"
      @open="openOptions"
      @search-change="onSearchChange"
    >
      <template v-slot:beforeList>
        <div class="sticky-tabs">
          <li class="subpage-menu mt-2 mb-1 ml-2 true-small">
            <a
              v-for="tab in tabs"
              :key="tab.key"
              href="#"
              class="subpage-menu__item true-small px-2"
              :class="{'router-link-exact-active router-link-active': selectedMenu === tab.key}"
              @click.prevent="changeTab(tab.key)"
            >
              {{ tab.label }}
            </a>
          </li>
        </div>
      </template>
      <template v-slot:noResult>
        <div v-if="selectedMenu === 'additional' && searchQuery && !optionMatches(searchQuery)">
          <div class="text-muted">
            No options found for "<strong>{{ searchQuery }}</strong>".
            <a 
              href="#" 
              @click="onNewOptionAdded"
            >
              Add it as a new option
            </a>
          </div>
        </div>
      </template>
    </multi-select>
  </div>
</template>

<script>
  import MultiSelect from 'vue-multiselect';

  export default {
    components: {
      MultiSelect,
    },
    props: {
      metricOptions: {
        type: Array,
        required: true,
      },
      value: {
        type: String,
        default: '',
      },
      placeholder: {
        type: String,
        default: 'Select option',
      },
      searchable: {
        type: Boolean,
        default: true,
      },
      closeOnSelect: {
        type: Boolean,
        default: true,
      },
      selectedMenu: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        tabs: [
          { key: 'installed', label: 'Installed Software' },
          { key: 'additional', label: 'Additional Software' },
        ],
        searchQuery: '',
      };
    },
    methods: {
      openOptions() {
        this.$emit('tab-change', this.selectedMenu);
      },
      changeTab(tabKey) {
        this.$emit('tab-change', tabKey);
      },
      onSelect(option) {
        this.$emit('select', option);
      },
      onRemove(option) {
        this.$emit('remove', option);
      },
      onSearchChange(searchQuery) {
        this.searchQuery = searchQuery;
      },
      onNewOptionAdded() {
        this.$emit('add-new-option', this.searchQuery);
      },
      optionMatches(searchQuery) {
        return this.metricOptions.some(option => option.toLowerCase().includes(searchQuery.toLowerCase()));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .sticky-tabs {
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
  }
</style>
