<template>
  <div class="clearfix row mx-1">
    <div class="col mt-5">
      <h4 class="font-weight-normal mb-3 h5--responsive">
        Help Desk Connectors
      </h4>
      <div class="box box--with-heading">
        <h6 class="box__heading">
          Create tickets from your business-messaging apps
          <div class="small mt-1">
            Connect your messaging app channels with Genuity Help Desk to generate tickets with ease.
          </div>
        </h6>
        <div class="box__inner">
          <div class="p-3 connectors-alert">
            <span class="genuicon-info-circled d-inline-block info-icon" />
            <span>
              These connectors will be integrated with the company's default workspace only. 
              <strong>Tickets</strong> created through these connectors will only be visible in the default workspace.
            </span>
          </div>
          <div class="connectors-page integrations w-100">
            <div
              class="row"
            >
              <add-onboarding-item
                :add-item-option="slackIntegrationData"
                :intg-detail="{isConnected: !!connectors.slackConfigsLength, isHelpDeskIntegration: true, link: '/settings/connectors/slack'}"
                :loading-value="0"
                class="col-12 col-md-6 col-lg-4 mt-5"
              />

              <add-onboarding-item
                :add-item-option="msTeamsIntegrationData"
                :intg-detail="{isConnected: !!connectors.msTeamsConfigsLength, isHelpDeskIntegration: true, link: '/settings/connectors/ms-teams'}"
                :loading-value="0"
                class="col-12 col-md-6 col-lg-4 mt-5"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import addOnboardingItem from "../../shared/module_onboarding/add_onboarding_item.vue";

export default {
  components: {
    addOnboardingItem,
  },
  data() {
    return {
      connectors: {},
      slackIntegrationData: {
        name: 'Slack',
        blurb: 'Connect your channel with Genuity Help Desk',
        helpCenterLink: 'https://docs.gogenuity.com/docs/slack',
        thirdPartyConnector: true,
        imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/slack.svg",
      },
      msTeamsIntegrationData: {
        name: 'Microsoft Teams',
        blurb: 'Connect your channel with Genuity Help Desk',
        helpCenterLink: 'https://docs.gogenuity.com/docs/ms-teams',
        thirdPartyConnector: true,
        imgPath: "https://nulodgic-static-assets.s3.amazonaws.com/images/ms_teams.svg",
      },
    };
  },
  mounted() {
    this.fetchHelpDeskConnectors();
  },
  methods: {
    fetchHelpDeskConnectors() {
      http
        .get(`/help_desk_connectors`)
        .then(res => {
          this.connectors = res.data;
        })
        .catch(() => {
          this.emitError('There was an issue fetching your connectors information. Please refresh the page.');
        });
    },
  },
};
</script>

<style lang="scss" scoped>
  .connectors-alert {
    display: flex;
    font-size: 0.8rem;
    border-left: 4px solid $color-caution;
    background: $themed-light;
  }
  .info-icon {
    margin-top: 2px;
    color: $color-caution;
    margin-right: 4px;
  }
</style>
